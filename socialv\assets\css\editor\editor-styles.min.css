@media (min-width:1920px){:root{--font-size-body:.834vw}}@media (max-width:991px){:root{--font-size-h1:2em;--font-size-h2:1.8em;--font-size-h3:1.6em;--font-size-h4:1.4em;--font-size-h5:1.2em;--font-size-h6:1em}}@media (max-width:767px){:root{--font-size-body:.875em;--font-size-h1:1.8em;--font-size-h2:1.6em;--font-size-h3:1.4em;--font-size-h4:1.2em;--font-size-h5:1.1em;--font-size-h6:1em}}[data-mode=dark]{--color-theme-white:#fff;--global-body-bgcolor:#091025;--global-body-lightcolor:#f9fbff;--global-font-color:#a5a8ab;--global-font-title:#fff;--border-color-light:#101421;--border-color-dark:#7f858b;--border-color-light-opacity:rgba(0,0,0,0.19);--dark-icon-color:#fff;--color-post-sticky-bg:#e5efff;--color-input-placeholder:#7c859b;--color-input-border:#cbcbcb;--color-menu-font:#7f858b;--disable-color:#cbcbcb;--color-default-bg-avatar:#ddeaff;--color-theme-white-box:#080d1e;--unread-message-color:#0b1126;--menu-label-color:#566b8c;--color-theme-skeleton:#080f26;--color-theme-light-grey:#212224;--color-default-bg-avata:#010f26;--comment-font-color:#a5a8ab;--color-theme-primary-light:#0e1e37;--color-theme-success-light:#08202f;--color-theme-danger-light:#1d1528;--color-theme-warning-light:#2e3131;--color-theme-info-light:#081c34;--color-theme-orange-light:#1d1a27}body,body.socialv-learnpress,body.learnpress{background:#f8f9fa;color:#6f7f92;font-family:Plus Jakarta Sans,sans-serif;font-size:1em;line-height:1.75}h1,h2,h3,h4,h5,h6{font-family:Plus Jakarta Sans,sans-serif;font-stretch:condensed;clear:both;margin:.625em 0;word-break:break-word;text-transform:capitalize}.h1,h1{font-size:2.5em}.h1,.h2,h1,h2{color:#07142e;line-height:1.3;letter-spacing:0;font-weight:600}.h2,h2{font-size:2.074em}.h3,h3{font-size:1.728em}.h3,.h4,h3,h4{color:#07142e;line-height:1.3;letter-spacing:0;font-weight:500}.h4,h4{font-size:1.44em}.h5,h5{font-size:1.2em}.h5,.h6,h5,h6{color:#07142e;line-height:1.3;letter-spacing:0;font-weight:500}.h6,h6{font-size:1em}button,input,optgroup,select,textarea{color:#6f7f92}.text_small,small{font-size:.875em}.overflow-hidden{overflow:hidden}.z-index-minus{z-index:-1}.letter-spacing{letter-spacing:.25em}:is(h1,h2,h3,h4,h5,h6,.h1,.h2,.h3,.h4,.h5,.h6) :is(a,a:visited){font-size:inherit;color:inherit}p{word-break:break-word;line-height:1.75}pre{background:#fff;color:#6f7f92;padding:1em;border:.0625em solid #2f65b9}.socialv h1,.socialv h2,.socialv h3,.socialv h4,.socialv h5,.socialv h6,body.buddypress h1,body.buddypress h2,body.buddypress h3,body.buddypress h4,body.buddypress h5,body.buddypress h6{margin:0}::-moz-selection{background:#2f65b9;color:#fff;text-shadow:none}::selection{background:#2f65b9;color:#fff;text-shadow:none}html{-webkit-box-sizing:border-box;box-sizing:border-box}*,:after,:before{-webkit-box-sizing:inherit;box-sizing:inherit}a:focus{text-decoration:none!important}a:hover{color:#2f65b9;text-decoration:none}:focus,a{outline:none}p{margin:1em 0;line-height:1.66em}code{color:#2f65b9}hr{background-color:#7f858b;border:0;height:.0625em;margin:0;padding:0}ol,ul{margin-bottom:1em}ol,ol ol,ul{padding-left:1.563em}dd{margin:0 1.5em 1.5em}dl dd{margin-bottom:1em}dl dd:last-child,li>ol,li>ul{margin-bottom:0}dt{font-weight:700}img{max-width:100%;height:auto}table{width:100%;margin-bottom:1.25em}table,table td,table th{border:.0625em solid #f1f1f1}table td,table th{padding:.313em .5em}table td a{color:#6f7f92}audio{width:100%}[type=radio]{width:1em;height:1em;margin-right:.3em}input[type=checkbox]{width:1em;height:1em;margin-right:.625em;line-height:2}.comment-respond .comment-form input,.comment-respond .comment-form textarea,.form-control,.woocommerce form .form-row .input-text,input,input[type=color],input[type=date],input[type=datetime-local],input[type=datetime],input[type=email],input[type=month],input[type=number],input[type=password],input[type=range],input[type=search],input[type=tel],input[type=text],input[type=time],input[type=url],input[type=week],textarea{width:100%;padding:0 1em;height:3.123em;line-height:3.123em;font-family:Plus Jakarta Sans,sans-serif;font-size:1em;font-weight:400;background-color:#f8f9fa;border:.0625em solid #f1f1f1;border-radius:.313em;color:#6f7f92}.form-select,.woocommerce form .form-row select,select{line-height:3.123em;height:3.123em;padding:0 .625em;width:100%;color:#6f7f92;background-color:#f8f9fa;border:.0625em solid #f1f1f1;border-radius:.313em;-webkit-transition:all .45s ease-in-out;transition:all .45s ease-in-out}.form-floating>textarea.form-control,textarea{padding:1em 0;min-height:9.375em;line-height:1.75;border-color:#f1f1f1}textarea{padding:1em}select option{padding:6em;color:#6f7f92;font-family:Plus Jakarta Sans,sans-serif;font-size:1em;line-height:3}input::-webkit-input-placeholder{color:#6f7f92;font-size:.875em;font-weight:500}input:-ms-input-placeholder,input::-moz-placeholder,input::-webkit-input-placeholder,input::-webkit-placeholder,input::placeholder,input[type=email]:-ms-input-placeholder,input[type=email]::-moz-placeholder,input[type=email]::-webkit-input-placeholder,textarea:-ms-input-placeholder,textarea::-moz-placeholder,textarea::-webkit-input-placeholder{color:#6f7f92;font-size:.875em;font-weight:500}.comment-respond .comment-form input:focus,.comment-respond .comment-form textarea:focus,.form-control:focus,.woocommerce form .form-row .input-text:focus,input:focus,input[type=color]:focus,input[type=date]:focus,input[type=datetime-local]:focus,input[type=datetime]:focus,input[type=email]:focus,input[type=month]:focus,input[type=number]:focus,input[type=password]:focus,input[type=range]:focus,input[type=search]:focus,input[type=tel]:focus,input[type=text]:focus,input[type=time]:focus,input[type=url]:focus,input[type=week]:focus,select:focus,textarea:focus{color:#07142e;border-color:#2f65b9;background:transparent;-webkit-box-shadow:none;box-shadow:none;outline:none}.socialv-login-form .form-control:focus{background-color:#f8f9fa;-webkit-box-shadow:none;box-shadow:none}embed,iframe,object{width:100%}iframe{border:none}label{color:#07142e}.form-editor-box,.form-floating{margin-bottom:2em}.form-editor-box label{font-size:1em;color:#07142e;font-weight:600;font-family:Plus Jakarta Sans,sans-serif}.form-floating>label{color:#6f7f92;font-size:.875em;font-weight:400;height:auto;padding:.85em 1em;border:none;-webkit-transition:all .2s ease-in-out;transition:all .2s ease-in-out}.form-floating>.form-control:not(:-moz-placeholder-shown)~label{opacity:1;transform:scale(.75) translateY(-.6em) translateX(1.5em);padding:0 .5em;background:#fff;border:.0625em solid #f1f1f1;border-top:none;border-bottom:none;line-height:1.1em;letter-spacing:.1em}.form-floating>.form-control:not(:-ms-input-placeholder)~label{opacity:1;transform:scale(.75) translateY(-.6em) translateX(1.5em);padding:0 .5em;background:#fff;border:.0625em solid #f1f1f1;border-top:none;border-bottom:none;line-height:1.1em;letter-spacing:.1em}.form-floating>.form-control:focus~label,.form-floating>.form-control:not(:placeholder-shown)~label,.form-floating>.form-select~label,.form-floating>.select2-container--default.select2-container--focus~label{opacity:1;-webkit-transform:scale(.75) translateY(-.6em) translateX(1.5em);transform:scale(.75) translateY(-.6em) translateX(1.5em);padding:0 .5em;background:#fff;border:.0625em solid #f1f1f1;border-top:none;border-bottom:none;line-height:1.1em;letter-spacing:.1em}.form-floating>.form-control:focus~label{color:#2f65b9;border-color:#2f65b9}.form-floating>.form-control{height:3.123em;line-height:3.123em;padding:0 1em}.select2-container--default .select2-selection--multiple{min-height:3.123em;background:transparent;border-color:#f1f1f1;border-radius:.313em}.select2-container--default.select2-container--focus .select2-selection--multiple{border-color:#2f65b9}.select2-container--default .select2-selection--multiple input{height:auto;line-height:normal}.select2-container--default .select2-selection--multiple .select2-selection__choice{background:#f8f9fa;border-color:#7f858b;border-radius:.313em}.select2-container--default .select2-selection--multiple .select2-selection__rendered li{margin:10px 10px 0 0}.form-floating>.form-control:not(:-moz-placeholder-shown){padding:1em;background:transparent;border-color:#f1f1f1}.form-floating>.form-control:not(:-ms-input-placeholder){padding:1em;background:transparent;border-color:#f1f1f1}.form-floating>.form-control:focus,.form-floating>.form-control:not(:placeholder-shown){padding:1em;background:transparent;border-color:#f1f1f1}.form-floating>.form-control:focus{border-color:#2f65b9}.radio-button-options .option-label{margin-right:1.5em;display:-webkit-inline-box;display:-ms-inline-flexbox;display:inline-flex;-webkit-box-align:center;-ms-flex-align:center;align-items:center}.form-floating .bp-xprofile-checkbox-acceptance-field{display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-align:center;-ms-flex-align:center;align-items:center}#buddypress .form-floating .select2-container--default .select2-selection--single .select2-selection__rendered{line-height:3.123em}#buddypress .form-floating .select2-container--default .select2-selection--single .select2-selection__arrow,#buddypress .form-floating .select2-container .select2-selection--single{height:3.123em}#buddypress .field-visibility-settings .radio{display:-webkit-box;display:-ms-flexbox;display:flex;-ms-flex-wrap:wrap;flex-wrap:wrap;-webkit-box-align:center;-ms-flex-align:center;align-items:center;grid-gap:1.5em;grid-row-gap:.5em;margin-top:.5em}.field-visibility-settings-close{margin-top:.8em}.socialv-login-form input:-internal-autofill-selected,.socialv-login-form input:-webkit-autofill,.socialv-login-form input:-webkit-autofill:active,.socialv-login-form input:-webkit-autofill:focus,.socialv-login-form input:-webkit-autofill:hover{background-color:#f8f9fa!important;-webkit-transition:none;transition:none}input:focus-visible+label{outline:none!important;border-radius:none}@media (max-width:479px){.form-floating>label{font-size:.75em;padding:1.1em 1em}}a{color:#2f65b9}a:hover{color:#2a5aa6}a:focus{outline:none!important;-webkit-box-shadow:none;box-shadow:none}a:active,a:hover{outline:0}.comment-content .wp-smiley,.entry-content .wp-smiley,.page-content .wp-smiley{border:none;margin-bottom:0;margin-top:0;padding:0}.wp-caption,embed,iframe,object{max-width:100%}.wp-caption{margin-bottom:1.5em}.wp-caption img[class*=wp-image-]{display:block;margin-left:auto;margin-right:auto}.wp-caption-text{text-align:center}.wp-caption .wp-caption-text{margin:.8075em 0;font-style:italic}.wp-caption.alignleft .wp-caption-text{text-align:left}.wp-caption.alignright .wp-caption-text{text-align:right}@media (min-width:768px){.wp-block-image.alignright,.wp-caption.alignright{margin-right:calc(50% - 42.2165em)}.wp-block-image.alignleft,.wp-caption.alignleft{margin-left:calc(50% - 42.2165em)}}.gallery-item{padding:0 1em 1em 0}.gallery-icon{display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-pack:center;-ms-flex-pack:center;justify-content:center}.gallery-caption{font-size:80%}@media (max-width:479px){.gallery-item{padding:0 0 1em}}@media (min-width:1499px){.container{max-width:75em}.socialv .container{max-width:84.433em}}@media (min-width:1300){.container{max-width:75em}.socialv .container{max-width:84.433em}}body .container{max-width:75em}.container-fluid{padding:0 2em}body .socialv .container,body .elementor-section.elementor-section-boxed>.elementor-container{max-width:84.433em}.sidebar+.main-container,.sidebar+.main-container>header{margin-left:16.862em;-webkit-transition:all .45s ease;transition:all .45s ease}.sidebar.sidebar-mini+.main-container,.sidebar.sidebar-mini+.main-container>header{margin-left:5.5em;-webkit-transition:all .45s ease;transition:all .45s ease}.content-area .site-main,.socialv-activity-site-main{padding:2em 0}.content-area .site-main.socialv-bp-main{padding-top:0}.home .socialv .content-area .site-main{padding:0}.home.blog .socialv .content-area .site-main{padding:2em 1em}.content-area .socialv-bp-default-main.site-main,.home.buddypress.activity .socialv .content-area .site-main{padding:2em 0}@media (max-width:1024px){.container-fluid{padding:0 2em}.content-area .site-main,.socialv-activity-site-main,.home.blog .socialv .content-area .site-main,.home.buddypress.activity .socialv .content-area .site-main{padding:2em 0}}@media (max-width:767px){.container-fluid{padding:0 1em}.content-area .socialv-bp-default-main.site-main,.content-area .site-main,.socialv-activity-site-main,.home.blog .socialv .content-area .site-main{padding:1em 0}}::-webkit-scrollbar{width:.6em;height:.25em}.header-notification-icon .dropdown-menu .item-body::-webkit-scrollbar,.select2-container--default .select2-results>.select2-results__options::-webkit-scrollbar{width:.25em}::-webkit-scrollbar-track{border-radius:.313em}::-webkit-scrollbar-thumb{background:#2f65b9;border-radius:.313em}.scrollbar-track{display:none!important}.scrollbar-thumb{width:0!important;background:#2f65b9;border-radius:.313em;scrollbar-width:none}.yScroller::-webkit-scrollbar{display:none}.yScroller{scrollbar-width:none}.css-prefix-top{opacity:0;-webkit-transition:all .2s ease 0s;transition:all .2s ease 0s}#back-to-top .top{margin:0;background:#2f65b9;position:fixed;bottom:2.063em;right:1.875em;z-index:999;font-size:1.625em;width:1.875em;height:1.875em;text-align:center;line-height:1.875em;border-radius:100%}#back-to-top .top,#back-to-top .top i{color:#fff;-webkit-transition:all .45s ease-in-out;transition:all .45s ease-in-out}#back-to-top .top:hover{background:#000505}#loading{width:100%;height:100%;display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-orient:vertical;-webkit-box-direction:normal;-ms-flex-direction:column;flex-direction:column;-webkit-box-pack:center;-ms-flex-pack:center;justify-content:center;-webkit-box-align:center;-ms-flex-align:center;align-items:center;position:fixed;top:0;left:0;right:0;bottom:0;background:#f8f9fa;z-index:99999}.socialv-breadcrumb{background-repeat:no-repeat!important;background-position:50%!important;background-size:cover!important;background:#2f65b9;padding:7.166em 1em}.socialv-header-over .socialv-breadcrumb{padding:12.125em 0 6.875em}.breadcrumb li{display:inline-block;word-break:break-word}.socialv-breadcrumb .socialv-breadcrumb-box,.socialv-breadcrumb .socialv-breadcrumb-nav{position:relative;z-index:2}.socialv-breadcrumb ol li,.socialv-breadcrumb ol li a{font-size:.875em;color:#fff;text-decoration:none;text-transform:uppercase;font-weight:500;letter-spacing:.0625em}.socialv-breadcrumb ol li a{font-size:inherit}.socialv-breadcrumb ol li a:hover{color:#fff}.socialv-breadcrumb ol li{list-style:none;display:inline-block;position:relative}.socialv-breadcrumb .breadcrumb-item+.breadcrumb-item:before{content:"\e90d";font-family:iconly;font-weight:200;color:#fff;position:absolute;padding:0;left:-4px;top:50%;-webkit-transform:translateY(-45%);transform:translateY(-45%);display:none}.socialv-breadcrumb li.breadcrumb-item span{padding-right:.5em}.socialv-breadcrumb .breadcrumb{position:relative;display:inline-block}.socialv-breadcrumb .breadcrumb-item.active{color:#fff}.socialv-breadcrumb ol{background:transparent;padding:0;margin-bottom:0}.socialv-breadcrumb .title{margin-bottom:.313em;margin-top:0;color:#fff}.socialv-breadcrumb-nav .breadcrumb{display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-align:center;-ms-flex-align:center;align-items:center;line-height:normal;-webkit-box-pack:center;-ms-flex-pack:center;justify-content:center;-ms-flex-wrap:wrap;flex-wrap:wrap}.socialv-breadcrumb-nav ol li{display:inline-block}.breadcrumb-bg,.breadcrumb-video,.video-socialv-bg-over{position:relative;overflow:hidden}.breadcrumb-bg #video-background,.breadcrumb-bg video,.breadcrumb-video video,.video-breadcrumb-bg #video-background,.video-socialv-bg-over #video-background,.video-socialv-bg-over video{position:absolute;left:50%;top:50%;-webkit-transform:translate(-50%,-50%);transform:translate(-50%,-50%);display:inline-block;width:100%}@media(max-width:767px){.socialv-default-header.socialv-header-over .socialv-breadcrumb,.socialv-header-over .socialv-breadcrumb{padding:9.125em 1em 5.125em}}.socialv-bp-banner{background:#2f65b9 url(../images/redux/bp-banner.jpg);background-repeat:no-repeat;background-size:cover;padding:7.688em 2em;border-radius:.375em;margin-top:2em;margin-bottom:1em}.socialv-bp-banner .socialv-bp-banner-title .title{color:#fff}.socialv-bp-banner .socialv-subtitle{color:#fff;width:80%}@media (max-width:1024px){.socialv-bp-banner{padding:4em 2em}.socialv-bp-banner .socialv-subtitle{width:100%}}@media (max-width:991px){.socialv-bp-banner{margin-top:2em;margin-bottom:1em}}@media (max-width:767px){.socialv-bp-banner{margin-top:1em;margin-bottom:.5em}}button,option,select{color:inherit;font-family:inherit;font-size:inherit;line-height:inherit;border-radius:inherit}.wp-block.editor-post-title.editor-post-title__block:after{display:block;clear:both;content:""}.editor-post-title__input{font-family:Plus Jakarta Sans,sans-serif;font-stretch:condensed;font-size:2.5rem;line-height:1.4;margin-top:0;margin-bottom:0}.gallery .gallery-item,.wp-block-freeform .gallery .gallery-item{width:auto}.wp-block{max-width:calc(84.433em + 30px)}.wp-block[data-align=wide]{max-width:calc(50% + 42.2165em + 76px)}.wp-block[data-align=full]{max-width:none}.wp-block-cover h2,.wp-block-cover h3,.wp-block-cover h4{color:inherit}.wp-block-cover .wp-block-cover-text{font-size:2em;line-height:1.25}.wp-block-archives,.wp-block-categories__list,.wp-block-latest-comments,.wp-block-latest-posts{margin-left:auto;margin-right:auto}.wp-block-latest-comments{padding-left:2.5em;padding-right:2.5em}
/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */