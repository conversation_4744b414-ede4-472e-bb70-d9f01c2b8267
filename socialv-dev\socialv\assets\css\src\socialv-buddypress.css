/* Authentication popup Design */
.socialv-authentication-modal .modal-dialog {
	max-width: 35.375em;
	margin: 1em auto;
}

.socialv-authentication-modal .modal-content {
	border-radius: var(--border-radius-16);
	background-color: var(--color-theme-white-box);
	margin: 0 1em;
}

.socialv-authentication-modal .modal-body {
	padding: 0;
}

.socialv-authentication-modal .modal-header {
	border: none;
	position: absolute;
	left: auto;
	right: 0;
	z-index: 9;
}

.socialv-login-form .socialv-info p {
	font-size: 1em;
	font-weight: var(--font-weight-regular);
	color: var(--global-font-color);
	padding: 0 4em;
	margin-top: 1em;
}

.editfield input::-webkit-file-upload-button {
	background: var(--color-theme-white-box);
	border: 1px solid var(--global-font-color);
	line-height: normal;
	margin-right: 10px;
	font-size: 12px;
	color: var(--global-font-color);
}

.socialv-login-form label,
.form-editor-box label,
.editfield legend {
	font-size: 1em;
	color: var(--global-font-title);
	font-weight: var(--font-weight-medium);
	font-family: var(--highlight-font-family);
	margin-bottom: .875em;
	width: auto;
}

.socialv-login-form .register-section .editfield input {
	padding: 0 1em;
}

.socialv-login-form .register-section .editfield .input-group input {
	padding-left: 0;
}

.socialv-login-form .bbp-remember-me label {
	margin-bottom: 0;
}

.socialv-login-form input {
	padding-left: 0;
	background-color: var(--global-body-bgcolor);
	border: .0625em solid var(--global-body-bgcolor);
}

.socialv-login-form input:focus {
	border-color: var(--global-body-bgcolor);
}

.socialv-login-form .input-group-text {
	color: var(--global-font-color);
	border-color: var(--global-body-bgcolor);
	background-color: var(--global-body-bgcolor);
}

.socialv-login-form .input-group {
	margin-bottom: 2em;
}

.field_type_multiselectbox .clear-value {
	display: none;
}

.forgot-pwd {
	font-size: var(--font-size-normal);
	font-weight: var(--font-weight-medium);
	font-style: italic;
	color: var(--color-theme-primary);
}

.socialv-login-form .socialv-info {
	margin-bottom: 2em;
}

.socialv-login-form .login-submit,
.socialv-login-form .socialv-auth-button {
	margin-top: 1.5em;
	margin-bottom: 1.5em;
}

.socialv-login-form .register-link {
	font-size: 1em;
	color: var(--global-font-title);
	font-family: var(--highlight-font-family);
	font-weight: var(--font-weight-medium);
	text-transform: capitalize;
	margin-top: 1em;
}

.socialv-login-form .register-link a {
	color: var(--color-theme-primary);
	padding-left: .5em;
}

.socialv-login-form {
	display: flex;
	justify-content: center;
	flex-direction: column;
}

.sociel-media-opt {
	position: relative;
	margin: 1.5em 0;
}

.sociel-media-opt::after {
	position: absolute;
	content: "";
	top: .8em;
	bottom: 0;
	left: 0;
	right: 0;
	background-color: var(--border-color-light);
	height: .0625em;
}

.sociel-media-opt span {
	display: inline-block;
	background: var(--color-theme-white-box);
	position: relative;
	z-index: 9;
	padding: 0 1em;
}

.sociel-media-button button {
	height: 2.375em;
	width: 2.375em;
	line-height: 1.688em;
	text-align: center;
	background: var(--global-body-bgcolor);
}

.socialv-login-form label.login-info {
	display: block;
	color: var(--global-font-color);
	font-size: var(--font-size-normal);
}

.socialv-login-form p {
	margin: 0;
}

p.login-remember label {
	margin: 0;
}

#checkout-account-logged-in {
	margin-bottom: 1em;
}

.error-login {
	color: var(--color-theme-danger);
}

.editfield .checkbox .option-label {
	margin-right: .875em;
}

.socialv-items-list-widget .apsl-avatar-social-login,
.widget_iqonic_user_profile .apsl-avatar-social-login,
.activity-post-upload .apsl-avatar-social-login,
.socialv-member-main .apsl-avatar-social-login,
.socialv-member-info .apsl-avatar-social-login,
.socialv-list-post .apsl-avatar-social-login,
.socialv-activity-item .apsl-avatar-social-login,
.socialv-widget-image-content-wrap .apsl-avatar-social-login,
.socialv-notification-box .avatar.apsl-avatar-social-login.photo {
	border-radius: var(--border-radius-full) !important;
}

p.register-message {
	padding: 1em;
	color: var(--color-theme-danger);
	background: var(--color-theme-danger-light);
	border-left: .1875em solid var(--color-theme-danger);
	margin-bottom: 2em;
	border-radius: 0 var(--border-radius) var(--border-radius) 0;
}

.register-section .error {
	color: #dc3545 !important;
}

.socialv-bp-login .bbp-submit-wrapper {
	float: inherit;
	margin-top: 1.5em;
}

.socialv-bp-login .bbp-submit-wrapper button {
	width: 100%;
}

.register-page-scroll .socialv-login-form {
	height: 90vh;
	overflow-y: auto;
	overflow-x: hidden;
	padding: 2em 0;
	display: flex;
	justify-content: flex-start;
	flex-direction: column;
	transition: none;
}

.undo_activity_post .undo-btn .hide-post-btn {
	padding: 10px 20px;
	background: var(--color-theme-light-grey);
	text-transform: capitalize;
	color: var(--global-font-color);
	border-radius: var(--border-radius);
	font-size: 14px;
}

.undo_activity_post .undo-btn .hide-post-btn:hover,
.imt-report-button-wrap p,
.dropdown-item {
	color: var(--global-font-title);
}

a.activity-time-since:hover {
	text-decoration: none;
}

.socialv-login-form .verification-email {
	background: var(--color-theme-primary-light);
	padding: 1rem;
	border-radius: 5px;
}

@media (max-width: 1024px) {
	.socialv-login-form .socialv-info p {
		padding: 0;
	}
}

@media (max-width: 767px) {
	.authpage.socialv-background-white {
		background: var(--color-theme-primary);
		height: 100vh;
		overflow-y: auto;
		overflow-x: hidden;
	}
}

#buddypress .form-edit-btn input.btn {
	margin-top: 1em;
	height: auto;
}

.checkbox>label {
	display: flex;
	align-items: center;
}

/* page style */
.card-main.socialv-bp-login {
	max-width: 35.375em;
	margin: 0 auto;
	box-shadow: none;
}

.socialv-login-form .register-section .editfield {
	margin-bottom: 1em;
}

.socialv-login-form .register-section .editfield.field_type_datebox .input-group {
	margin: 0 -1em;
	width: auto;
}

@media (max-width: 767px) {
	.socialv-info p {
		padding: 0;
	}
}

@media (max-width: 479px) {
	.socialv-authentication-modal .modal-body {
		padding: 1em;
	}
}

/* Profile Page css start */

/* top profile */
#buddypress #header-cover-image {
	background-color: var(--color-theme-primary);
	height: 25em;
	position: static;
}

.header-cover-image.has-cover-image>img {
	height: 26.375em;
	object-fit: cover;
}

#buddypress #item-header-cover-image {
	min-height: auto !important;
}

#buddypress #item-header-cover-image #item-header-avatar {
	margin-top: 0 !important;
}

.socialv-profile-center {
	margin-top: -9em;
}

.card-main.socialv-profile-box {
	margin-top: -8em;
	position: relative;
}

.socialv-profile-center .header-avatar {
	position: relative;
	margin-bottom: 2.188em;
	display: inline-block;
	padding: .25em;
	border-radius: var(--border-radius);
}

.socialv-profile-center .header-avatar .chat-status {
	position: absolute;
	top: auto;
	bottom: -.5em;
	left: 0;
	right: 0;
}

.socialv-profile-center .header-avatar .chat-status span {
	font-size: .8em;
	padding: .297em 1em;
	line-height: 1.125em;
	font-weight: var(--font-weight-medium);
	letter-spacing: var(--letter-spacing-one);
	display: inline-block;
	color: var(--color-theme-white);
	background: var(--color-theme-grey);
	border-radius: var(--border-radius);
}

.socialv-profile-center .header-avatar .chat-status span.online {
	background: var(--color-theme-success);
}

#item-header-content .bp-verified-badge,
#item-header-content .bp-unverified-badge,
#profile-header .bp-verified-badge,
#profile-header .bp-unverified-badge {
	height: .875em;
	width: .875em;
	background-size: .4em;
}

.buddypress .link-change-profile-image {
	position: absolute;
	left: auto;
	right: -.5em;
	top: -.8em;
	background: var(--color-theme-primary);
	color: var(--color-theme-white);
	height: 2em;
	width: 2em;
	line-height: 2em;
	border-radius: 50%;
	font-size: 1em;
}

.buddypress .link-change-cover-image {
	position: absolute;
	left: auto;
	right: 2em;
	top: 2em;
	background: var(--color-theme-white);
	color: var(--color-theme-primary);
	height: 2em;
	width: 2em;
	line-height: 2em;
	border-radius: 50%;
	font-size: 1em;
	text-align: center;
}

.socialv-userinfo {
	margin-top: .5em;
}

.socialv-userinfo a {
	color: var(--global-font-color);
}

.socialv-userinfo a:hover {
	color: var(--color-theme-primary);
}

.socialv-profile-tab-button {
	margin-top: 1em;
	display: flex;
	align-items: center;
	justify-content: center;
	flex-wrap: wrap;
	gap: 1em;
}

.socialv-profile-tab-button .dropdown-menu li a.imt-block-button {
	background: transparent;
}

.socialv-profile-tab-button .generic-button {
	line-height: normal;
}

#buddypress .socialv-profile-tab-button .generic-button a.friendship-button.btn.btn-sm {
	padding: .813em 2em;
	font-size: var(--font-size-normal);
}

#item-header-content .socialv-profile-center,
#item-header-content .socialv-profile-left,
#item-header-content .socialv-profile-right {
	padding: 0;
}

.socialv-profile-left .item-social {
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 1em;
	flex-wrap: wrap;
	list-style: none;
	margin: 0;
}

.socialv-profile-left .item-social li a {
	display: block;
	height: 2.249em;
	width: 2.249em;
	line-height: 2.249em;
	font-size: .9em;
	text-align: center;
	background: var(--color-theme-primary);
	color: var(--color-theme-white);
	border-radius: var(--border-radius);
}

.socialv-profile-left .item-social li.Facebook a {
	background: #1877F2;
}

.socialv-profile-left .item-social li.Twitter a {
	background: #1DA1F2;
}

.socialv-profile-left .item-social li.Dribbble a {
	background: #EA4C89;
}

.socialv-profile-left .item-social li.Behance a {
	background: #1157FF;
	line-height: 1.9;
}

.socialv-profile-left .item-social li.YouTube a {
	background: #F9101E;
}

.socialv-profile-left .item-social li.Instagram a {
	background: -moz-linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%);
	background: -webkit-linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%);
	background: linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%);
	filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#f09433', endColorstr='#bc1888', GradientType=1);
}

.socialv-profile-left .item-social li.linkedin a {
	background: #0a66c2;
}

.socialv-profile-left .item-social li.Pinterest a {
	background: #d50123;
}

.socialv-profile-left .item-social li.Flickr a {
	background: #f70084;
	line-height: 1.7;
}

.socialv-profile-left .item-social li.Skype a {
	background: #00aae7;
}

.socialv-profile-left .item-social li.RSS a {
	background: #ef8021;
}

.socialv-profile-left .item-social li.Telegram a {
	background: #28a8e9;
}

.socialv-userinfo .info-meta {
	display: inline-block;
	padding: 0 1em;
	font-size: var(--font-size-normal);
	font-weight: var(--font-weight-medium);
	color: var(--global-font-color);
}

.socialv-userinfo .info-meta>i {
	padding: 0 .5em;
	color: var(--global-font-title);
	display: inline-block;
	vertical-align: middle;
}

.socialv-user-meta {
	display: flex;
	align-items: center;
	justify-content: end;
	margin: 0;
	gap: 1em;
}

.socialv-user-meta li {
	display: block;
	text-align: center;
	width: 33.33%;
	padding: 0 1em;
	position: relative;
	color: var(--global-font-color);
	font-size: var(--font-size-normal);
	font-weight: var(--font-weight-medium);
	letter-spacing: var(--letter-spacing-one);
}

.socialv-user-meta li::after {
	position: absolute;
	content: "";
	top: 50%;
	bottom: 0;
	left: auto;
	right: 0;
	background: var(--border-color-light);
	height: .875em;
	width: .125em;
	transform: translateY(-50%);
}

.socialv-user-meta li:last-child:after {
	display: none;
}

.socialv-user-meta li.group-type i {
	color: var(--global-font-title);
	font-size: inherit;
	line-height: inherit;
	display: block;
}

.socialv-user-meta li>h5 {
	margin-bottom: .25em;
	margin-top: 0;
	font-weight: var(--font-weight-semi-bold);
}

.socialv-profile-center {
	text-align: center;
}

@media (min-width:768px) and (max-width:1250px) {
	#buddypress .select2-container--default .select2-selection--single .select2-selection__arrow {
		width: .5em;
	}

	.select2-container .select2-selection--single .select2-selection__rendered {
		padding: 0 2em 0 .5em;
	}
}

@media (min-width:992px) and (max-width:1199px) {
	.socialv-user-meta li {
		padding: 0 .5em;
		font-size: .688em;
	}
}

@media (max-width: 1199px) {
	#buddypress #header-cover-image {
		height: 20em;
	}
}

@media (max-width: 991px) {
	#item-header-content .socialv-profile-center {
		order: 1;
	}

	#item-header-content .socialv-profile-right {
		order: 2;
		margin: 2em 0;
		text-align: center;
	}

	#item-header-content .socialv-profile-left {
		order: 3;
		text-align: center;
	}

	.socialv-profile-left .item-social {
		display: inline-flex;
	}

	.socialv-user-meta {
		justify-content: center;
	}

	.socialv-profile-center .header-avatar {
		position: relative;
		margin-bottom: 1.188em;
	}

	#buddypress #header-cover-image {
		height: 18em;
	}
}

@media (max-width: 767px) {
	#item-header-content {
		flex-direction: column;
	}

	.socialv-profile-left .item-social {
		justify-content: center;
	}

	#buddypress #header-cover-image {
		height: 13em;
	}

	.card-main.socialv-profile-box {
		margin-top: -2em;
	}

	.socialv-profile-center {
		margin-top: -6em;
	}
}

@media (max-width: 479px) {
	.socialv-user-meta li {
		padding: 0 .5em;
	}

	#buddypress #header-cover-image {
		height: 10em;
	}
}


/* tab design */
#buddypress div.item-list-tabs.socialv-tab-lists {
	display: flex;
	align-items: center;
	padding: 0 1em;
}

#buddypress div.item-list-tabs.socialv-tab-lists .left,
#buddypress div.item-list-tabs.socialv-tab-lists .right {
	cursor: pointer;
	position: relative;
	z-index: 9;
	display: none;
}

#buddypress div.item-list-tabs.socialv-tab-lists .left {
	left: -.6em;
}

#buddypress div.item-list-tabs.socialv-tab-lists .right {
	order: 3;
	right: -.6em;
}

#buddypress .socialv-tab-lists ul.socialv-tab-container li {
	padding: 2.5em .5em;
	display: inline-block;
	float: inherit;
	position: relative;
	width: 11.3em;
	margin: 0 auto;
	text-align: center;
}

#buddypress .socialv-tab-lists ul.socialv-tab-container li::after {
	position: absolute;
	content: "";
	top: 0;
	bottom: 0;
	left: auto;
	right: 0;
	background: var(--border-color-light);
	height: 100%;
	width: .0625em;
}

#buddypress .socialv-tab-lists ul.socialv-tab-container li:last-child:after {
	display: none;
}

#buddypress .socialv-tab-container::-webkit-scrollbar,
.socialv-subtab-container::-webkit-scrollbar {
	display: none;
}

#buddypress .socialv-tab-container,
.socialv-subtab-container {
	scrollbar-width: none;
}

#buddypress .socialv-tab-lists ul.socialv-tab-container {
	white-space: nowrap;
	overflow-x: auto;
}

#buddypress div.item-list-tabs ul.socialv-tab-container li a {
	position: relative;
	display: block;
	padding-top: 4.3em;
	padding-bottom: 0;
	color: var(--global-font-color);
	font-size: var(--font-size-normal);
	font-weight: var(--font-weight-medium);
	font-family: var(--highlight-font-family);
	background: transparent;
}

#buddypress div.item-list-tabs ul li a>span,
#buddypress div.item-list-tabs ul li a>span.count,
#buddypress div.item-list-tabs ul li a>span.no-count {
	display: none;
}

#buddypress div.item-list-tabs ul.socialv-tab-container li.current a,
#buddypress div.item-list-tabs ul.socialv-tab-container li.selected a {
	color: var(--color-theme-primary);
}

#buddypress div.item-list-tabs ul.socialv-tab-container li.current a::before,
#buddypress div.item-list-tabs ul.socialv-tab-container li.selected a::before {
	color: var(--color-theme-white);
	background: var(--color-theme-primary);
}

#buddypress div.item-list-tabs ul.socialv-tab-container li a::before {
	position: absolute;
	content: "\e937";
	font-weight: 200;
	font-family: 'iconly';
	height: 2.2em;
	width: 2.2em;
	line-height: 2.5em;
	text-align: center;
	font-size: 1.5em;
	background: var(--global-body-bgcolor);
	top: 0;
	left: 0;
	right: 0;
	margin: 0 auto;
	border-radius: var(--border-radius);
}

#buddypress div.item-list-tabs ul.socialv-tab-container li a#user-activity::before {
	content: "\e91a";
}

#buddypress div.item-list-tabs ul.socialv-tab-container li a#user-xprofile::before {
	content: "\e949";
}

#buddypress div.item-list-tabs ul.socialv-tab-container li a#user-friends::before {
	content: "\e95a";
}

#buddypress div.item-list-tabs ul.socialv-tab-container li a#user-notifications::before {
	content: "\e93f";
}

#buddypress div.item-list-tabs ul.socialv-tab-container li a#user-groups::before {
	content: "\e95b";
}

#buddypress div.item-list-tabs ul.socialv-tab-container li a#user-messages::before,
#buddypress div.item-list-tabs ul.socialv-tab-container li a#user-bp_better_messages_tab::before {
	content: "\e93c";
}

#buddypress div.item-list-tabs ul.socialv-tab-container li a#user-membership::before {
	content: "\e04c";
	font-weight: 400;
}

#buddypress div.item-list-tabs ul.socialv-tab-container li a#nav-media::before,
#buddypress div.item-list-tabs ul.socialv-tab-container li a#user-mediapress::before,
#buddypress div.item-list-tabs ul.socialv-tab-container li a#user-photos::before,
#buddypress div.item-list-tabs ul.socialv-tab-container li a#photos::before {
	content: "\e934";
}

#buddypress div.item-list-tabs ul.socialv-tab-container li a#user-videos::before,
#buddypress div.item-list-tabs ul.socialv-tab-container li a#videos::before {
	content: "\e95c";
}

#buddypress div.item-list-tabs ul.socialv-tab-container li a#user-audios::before,
#buddypress div.item-list-tabs ul.socialv-tab-container li a#audios::before {
	content: "\e95d";
}

#buddypress div.item-list-tabs ul.socialv-tab-container li a#user-forums::before {
	content: "\e928";
}

#buddypress div.item-list-tabs ul.socialv-tab-container li a#user-settings::before {
	content: "\e94d";
}

#buddypress div.item-list-tabs ul.socialv-tab-container li a#user-badges::before {
	content: "\e918";
}

#buddypress div.item-list-tabs ul.socialv-tab-container li a#user-invitations::before {
	content: "\e91e";
}

#buddypress div.item-list-tabs ul.socialv-tab-container li a#home::before {
	content: "\e933";
}

#buddypress div.item-list-tabs ul.socialv-tab-container li a#members::before {
	content: "\e95b";
}

#buddypress div.item-list-tabs ul.socialv-tab-container li a#nav-forum::before {
	content: "\e940";
}

#buddypress div.item-list-tabs ul.socialv-tab-container li a#invite::before {
	content: "\e94c";
}

#buddypress div.item-list-tabs ul.socialv-tab-container li a#admin::before {
	content: "\e900";
}

#buddypress div.item-list-tabs ul.socialv-tab-container li a#user-points::before {
	content: "\e940";
}

#buddypress div.item-list-tabs ul.socialv-tab-container li a#user-ranks::before {
	content: "\e951";
}

#buddypress div.item-list-tabs ul.socialv-tab-container li a#user-shop::before {
	content: "\e917";
}

#buddypress div.item-list-tabs ul.socialv-tab-container li a#user-courses::before {
	content: "\e928";
}

#buddypress div.item-list-tabs ul.socialv-tab-container li a#nav-bp-messages::before {
	content: "\e922";
}

/* sub tab design */
.socialv-subtab-container {
	overflow-x: auto;
	white-space: nowrap;
}

.socialv-subtab-lists {
	display: flex;
	align-items: center;
}

.socialv-subtab-lists .left,
.socialv-subtab-lists .right {
	cursor: pointer;
	position: relative;
	z-index: 9;
}

.socialv-subtab-lists .left {
	left: -1em;
}

.socialv-subtab-lists .right {
	order: 3;
	right: -1em;
}

#buddypress div.item-list-tabs .socialv-subtab-container ul>li {
	margin-right: 3em;
}

#buddypress div.item-list-tabs .socialv-subtab-container ul>li,
#buddypress div.item-list-tabs>ul>li {
	display: inline-block;
	padding: 1.624em 0;
	float: inherit;
}

#buddypress div.item-list-tabs>ul>li.socialv-search {
	display: inherit;
	margin: 0;
}

#buddypress div.item-list-tabs#subnav {
	overflow: visible;
}

.socialv-subtab-container ul>li.socialv-rss i.icon-rss {
	width: 1.999em;
	height: 1.999em;
	font-size: var(--font-size-normal);
	line-height: 1.999em;
	text-align: center;
	background: #f78422;
	color: var(--color-theme-white);
	border-radius: var(--border-radius);
	margin-right: .5em;
}

.socialv-subtab-container ul li.socialv-rss a:after {
	display: none;
}

.socialv-subtab-container ul li:last-child {
	margin-right: 0;
}

.socialv-subtab-container ul li a,
#buddypress div.item-list-tabs .socialv-subtab-container ul li a {
	position: relative;
	display: inline-block;
	font-size: var(--global-font-size);
	color: var(--global-font-color);
	font-weight: var(--font-weight-medium);
	font-family: var(--highlight-font-family);
	padding: 0;
	transition: all .45s ease;
}

.socialv-subtab-container ul li.current a,
.socialv-subtab-container ul li a.active {
	color: var(--color-theme-primary);
}

.video-wrap .mejs-container,
.video-wrap .wp-video {
	width: 100% !important;
	border-radius: var(--border-radius);
	height: 100% !important;
}

.mejs-container,
.mejs-container .mejs-controls,
.mejs-embed,
.mejs-embed body {
	border-radius: 0 0 var(--border-radius) var(--border-radius);
}

.wp-video-shortcode video,
video.wp-video-shortcode,
.socialv-gallery-status.socialv-swiper-slider,
.video-wrap iframe {
	border-radius: var(--border-radius);
}

#buddypress .wp-video button,
.wp-video button {
	padding: 0;
	display: inherit;
}

.socialv-subtab-container ul li a::after {
	position: absolute;
	content: '';
	width: 75%;
	height: .04em;
	background: currentColor;
	top: 100%;
	left: 0;
	right: 0;
	margin: 0 auto;
	pointer-events: none;
	transform-origin: 50% 100%;
	transition: clip-path .45s, transform .45s cubic-bezier(.2, 1, .8, 1);
	clip-path: polygon(0% 0%, 0% 100%, 0 100%, 0 0, 100% 0, 100% 100%, 0 100%, 0 100%, 100% 100%, 100% 0%);
}

.socialv-subtab-container ul li.socialv-rss a:after {
	display: none;
}

.socialv-subtab-container ul li:hover a::after,
.socialv-subtab-container ul li.selected a::after,
.socialv-subtab-container ul li a.active::after {
	transform: translate3d(0, 2px, 0) scale3d(1.08, 3, 1);
	clip-path: polygon(0% 0%, 0% 100%, 50% 100%, 50% 0, 50% 0, 50% 100%, 50% 100%, 0 100%, 100% 100%, 100% 0%);
}


#buddypress div.item-list-tabs .socialv-subtab-container ul li.current a,
#buddypress div.item-list-tabs .socialv-subtab-container ul li.selected a,
#buddypress div.item-list-tabs .socialv-subtab-container ul li a.active {
	color: var(--global-font-title);
	background: transparent;
	opacity: 1;
}

#buddypress .socialv-subtab-container ul li>span.count,
#buddypress div.item-list-tabs .socialv-subtab-container ul li span.count {
	font-weight: var(--font-weight-bold);
	background: var(--color-theme-orange);
	color: var(--color-theme-white);
	border-radius: var(--border-radius-12);
	font-size: .625em;
	padding: 0 .7em;
	margin-left: .5em;
	vertical-align: middle;
	display: inherit;
}

.socialv-product-view-buttons .socialv-data-filter-by,
.socialv-data-filter-by {
	display: flex;
	align-items: center;
	justify-content: flex-end;
	position: relative;
	padding: 1em 0 1.217em 2em;
	border-left: .0625em solid var(--border-color-light);
}

.socialv-sub-tab-lists li.socialv-search {
	padding: .9em 0;
}

.socialv-full-width .socialv-data-filter-by {
	border: none;
	padding-left: 0;
}

.socialv-full-width .socialv-group-filter {
	display: block;
}

.socialv-data-filter-by>form {
	display: flex;
	align-items: center;
}

.socialv-data-filter-by label {
	padding-right: 1em;
	white-space: nowrap;
	font-family: var(--highlight-font-family);
}

.socialv-activity-data {
	display: flex;
	align-items: center;
	justify-content: end;
	flex-direction: row-reverse;
}


@media (max-width: 767px) {
	.socialv-data-filter-by {
		border-top: .0625em solid var(--border-color-light);
		border-left: none;
		padding: 1em 0 1.5em;
	}

	.socialv-subtab-container ul>li {
		padding: 1em 0;
	}

	#buddypress .socialv-tab-lists ul.socialv-tab-container li {
		padding: 1.5em .5em;
	}

	.socialv-subtab-lists .left {
		left: 0;
	}

	.socialv-subtab-lists .right {
		right: 0;
	}

	.list-view .group-header {
		margin: 0;
	}
}


/* profile edit  */
.socialv-account-head {
	display: flex;
	align-items: center;
	gap: 1em;
	border-bottom: .0625em solid var(--border-color-light);
	padding-bottom: 2em;
	margin-bottom: 2em;
}

.socialv-head-buttons-inner {
	display: flex;
	align-items: center;
	flex-wrap: wrap;
	gap: 1em;
}

.socialv-head-buttons-inner .socialv-button-item a {
	height: 2.4985em;
	width: 2.4985em;
	line-height: 2.35em;
	font-size: 1.5em;
	text-align: center;
	margin-top: .3em;
	display: block;
	color: var(--global-font-color);
	background: var(--global-body-bgcolor);
	border-radius: var(--border-radius);
}

.socialv-head-buttons-inner .socialv-button-item.current a {
	background: var(--color-theme-primary);
	color: var(--color-theme-white);
}

.socialv-head-buttons-inner .socialv-button-item a i {
	display: inline-block;
	vertical-align: middle;
}

.accordion-button:focus {
	box-shadow: none;
}

.socialv-profile-edit-dropdown .accordion-button {
	background: var(--color-theme-white-box);
	color: var(--global-font-color);
	font-size: var(--font-size-h6);
	line-height: var(--font-line-height-h6);
	letter-spacing: var(--font-letter-spacing-h6);
	font-weight: var(--font-weight-h6);
	padding: 1em 1.25em;
	gap: .5em;
	border-radius: var(--border-radius);
}

.socialv-profile-edit-dropdown .accordion-item {
	margin-bottom: 1.5em;
	background: transparent;
	border: none;
}

.socialv-profile-edit-dropdown .accordion-item .accordion-collapse {
	margin-top: .75em;
	background: var(--color-theme-white-box);
}

.socialv-profile-edit-dropdown .accordion-item .accordion-collapse .accordion-body {
	padding: 0 1.5em;
	box-shadow: var(--global-box-shadow);
}

.socialv-profile-edit-dropdown .accordion-item .accordion-collapse .accordion-body li a {
	display: block;
	font-size: var(--font-size-normal);
	font-weight: var(--font-weight-medium);
	padding: 1.5em 0;
	color: var(--global-font-color);
	border-bottom: .0625em solid var(--border-color-light);
}

.socialv-profile-edit-dropdown .accordion-item .accordion-collapse .accordion-body li:last-child a {
	border: none;
}

.socialv-profile-edit-dropdown .accordion-item .accordion-collapse .accordion-body li.current a {
	color: var(--global-font-title);
}

.socialv-profile-edit-dropdown .accordion-button:not(.collapsed) {
	color: var(--color-theme-white);
	background: var(--color-theme-primary);
	box-shadow: none;
	gap: .5em;
}

.socialv-profile-edit-dropdown .accordion-button::after {
	position: absolute;
	content: "\e90d";
	font-weight: 200;
	font-family: 'iconly' !important;
	background: transparent;
	left: auto;
	right: 1em;
}

.accordion-button:not(.collapsed)::after {
	transform: rotate(90deg);
}

/* profile */
div.bp-avatar-nav {
	margin: 2em 0 0;
}

.bp-avatar-nav ul.avatar-nav-items li,
.bp-avatar-nav ul.avatar-nav-items li.current {
	border: none;
	padding-right: 1.5em;
}

.bp-avatar-nav li a,
.bp-avatar-nav li.current a {
	padding: 0;
	font-size: var(--font-size-normal);
	font-weight: var(--font-weight-medium);
	color: var(--global-font-color);
}

.bp-avatar-nav li.current a {
	color: var(--color-theme-primary);
}

.drag-drop-inside p.drag-drop-info {
	color: var(--global-font-title);
	font-size: var(--global-font-size);
	font-weight: var(--font-weight-medium);
}

.bm-dropzone,
.bm-dropzone.bm-dropzone-dragging-over {
	background: var(--color-theme-white-box);
	color: var(--global-font-title);
	border-color: var(--global-font-title);
}

.bp-avatar-nav ul {
	border: none;
	background: var(--global-body-bgcolor);
	margin: 0;
	padding: 1.5em 1.5em 1em;
	border-radius: var(--border-radius) var(--border-radius) 0 0;
}

.bp-avatar .bp-uploader-window {
	background: var(--global-body-bgcolor);
	padding: 0 1.5em 1.5em;
	border-radius: 0 0 var(--border-radius) var(--border-radius);
}

a.lost-password {
	font-style: italic;
	font-size: var(--font-size-normal);
	color: var(--color-theme-primary);
}

#drag-drop-area {
	border: .15em dashed rgba(111, 127, 146, .4);
	padding: 2.5em;
}

.drag-drop-buttons input#bp-browse-button,
#buddypress .mpp-dropzone input[type=button].button.mpp-button-select-files {
	width: auto;
	height: auto;
	line-height: normal;
	padding: .813em 2em;
	background: var(--color-theme-primary);
	border-color: var(--color-theme-primary);
	color: var(--color-theme-white);
}

.drag-drop-buttons input#bp-browse-button:hover {
	background: var(--color-theme-primary-dark);
	border-color: var(--color-theme-primary-dark);
	color: var(--color-theme-white);
}

#buddypress a.button#bp-delete-cover-image {
	background: var(--color-theme-danger);
	border-color: var(--color-theme-danger);
	color: var(--color-theme-white);
	padding: .813em 2em;
}

#buddypress a.button#bp-delete-cover-image:hover {
	background: var(--color-theme-danger-dark);
	border-color: var(--color-theme-danger-dark);
	color: var(--color-theme-white);
}

#buddypress a.button#bp-delete-avatar {
	background: var(--color-theme-danger);
	border-color: var(--color-theme-danger);
	color: var(--color-theme-white);
	padding: .813em 2em;
}

#buddypress a.button#bp-delete-avatar:hover {
	background: var(--color-theme-danger-dark);
	border-color: var(--color-theme-danger-dark);
	color: var(--color-theme-white);
}

.bp-avatar .items {
	padding: 1.5em;
	background: var(--global-body-bgcolor);
	border-radius: 0 0 var(--border-radius) var(--border-radius);
}

.bp-cover-image .bp-uploader-window {
	padding: 1.5em;
	background: var(--global-body-bgcolor);
	border-radius: var(--border-radius);
}

@media (max-width: 767px) {
	.socialv-account-head {
		padding-bottom: 1em;
		margin-bottom: 1em;
	}

	.socialv-profile-edit-dropdown .accordion-item {
		margin-bottom: 1em;
	}
}

@media (max-width: 479px) {
	.socialv-head-buttons-inner .socialv-button-item a {
		height: 2em;
		width: 2em;
		line-height: 2em;
		font-size: 1em;
	}
}

/* tab about */

.title-btn .cart-edit {
	background: var(--color-theme-primary);
	color: var(--color-theme-white);
	height: 1.7em;
	width: 1.7em;
	line-height: 1.7em;
	text-align: center;
	border-radius: var(--border-radius);
	display: block;
}

.title-btn .cart-edit:hover {
	background: var(--color-theme-primary-dark);
}

.socialv-about-info {
	display: flex;
	flex-wrap: wrap;
	margin: 0 -1em;
}

.socialv-about-info li {
	margin-bottom: 2.5em;
	width: 50%;
	padding: 0 1em;
}

.socialv-about-info li:last-child,
.socialv-about-info li:nth-last-child(-n+2) {
	margin-bottom: 0;
}

.socialv-about-info li label {
	color: var(--global-font-color);
	font-size: var(--font-size-normal);
	font-weight: var(--font-weight-medium);
	letter-spacing: var(--letter-spacing-one);
	text-transform: uppercase;
	margin-bottom: .5em;
}

.socialv-about-info .h6 {
	text-transform: unset;
}

.socialv-about-info .h6 p {
	margin: 0;
}

.card-main.card-space.card-view-profile-list:first-child {
	margin-top: 0;
}

/* edit form */
#buddypress .field-visibility-settings-toggle,
#buddypress .field-visibility-settings-notoggle {
	margin-bottom: 0;
	margin-top: .8em;
	color: var(--global-font-color);
	font-size: var(--font-size-normal);
	font-style: italic;
}

#buddypress .field-visibility-settings-toggle {
	display: flex;
	align-items: center;
	justify-content: space-between;
}

#buddypress .field-visibility-settings-notoggle {
	color: var(--global-font-title);
}

#buddypress .field-visibility-settings-toggle .current-visibility-level,
#buddypress .field-visibility-settings-notoggle .current-visibility-level {
	color: var(--global-font-title);
}

.form-edit-btn input {
	width: auto;
}

.form-edit-btn .submit {
	text-align: right;
}

.form-edit-btn .submit input+input {
	margin-left: 1em;
}

.datebox {
	display: flex;
	align-items: center;
	justify-content: start;
	margin: 0 -.5em;
}

.datebox .form-floating {
	margin: 0 .5em;
	width: 100%;
}

#buddypress .field-visibility-settings .radio label {
	display: block;
	font-size: var(--font-size-normal);
	color: var(--global-font-color);
}

.socialv-export-data>p {
	margin-top: 0;
}

#buddypress .standard-form div.submit input {
	margin-right: 0;
}

/* notification */
#buddypress table.notifications tbody tr td.notification-since {
	font-size: var(--font-size-normal);
}

.notification-data {
	display: flex;
	align-items: center;
	justify-content: space-between;
	background: var(--global-body-bgcolor);
	padding: 1.3em 2em;
	margin-bottom: 1.25em;
	border-radius: var(--border-radius);
}

.notification-data .notification-title {
	padding-right: .8em;
}

/* input switch */
.notification-switch i.icon-dash {
	background: var(--global-font-color);
	height: .125em;
	width: .875em;
	position: absolute;
	top: 50%;
	left: .5em;
	right: auto;
	border-radius: var(--border-radius);
}

.radio-switch {
	background-color: var(--color-theme-white-box);
	display: inline-block;
	overflow: hidden;
	height: 1.625em;
	width: 3.25em;
	position: relative;
	cursor: pointer;
	border-radius: 3.125em;
	-webkit-border-radius: 3.125em;
	-moz-border-radius: 3.125em;
	-o-border-radius: 3.125em;
	-ms-border-radius: 3.125em;
	display: flex;
	justify-content: center;
	align-items: center;
}

.radio-switch label {
	display: inline-block;
	width: 2em;
	height: 2.2em;
	padding: 0;
	margin: 0;
	text-align: center;
	vertical-align: middle;
	line-height: 1.8em;
}

.radio-switch label input[type=radio] {
	display: none;
}

.radio-switch label input[type=radio]+span {
	display: inline-block;
	font-weight: var(--font-weight-bold);
	padding: 0;
	margin: 0;
	text-align: center;
}

.radio-switch label input[type=radio]+span::after {
	background-repeat: repeat-x;
	content: ' ';
	background-color: var(--global-font-color);
	position: absolute;
	top: .25em;
	height: 1.125em;
	width: 1.125em;
	border-radius: 50%;
	-wenkit-border-radius: 50%;
	-moz-border-radius: 50%;
	-o-border-radius: 50%;
	-ms-border-radius: 50%;
	-moz-transition: left .2s linear, visibility 0s linear .2s;
	-webkit-transition: left .2s linear, visibility 0s linear .2s;
	transition: background-position .1s linear, left .2s linear, visibility 0s linear .2s;
}

.radio-switch label input[type="radio"]+span::before {
	content: " ";
	opacity: 0;
	width: 100%;
	height: 100%;
	position: absolute;
	left: 0;
	top: 0;
	cursor: pointer;
}

.radio-switch label input[type=radio]:checked+span:after {
	background: var(--color-theme-success);
}

.radio-switch label input[type=radio]:checked+span::after,
.radio-switch label input[type=radio]:checked+span::before {
	visibility: hidden;
}

.radio-switch label:nth-child(1) input+span::after {
	left: .35em;
}

.radio-switch label:nth-child(1) input:checked+span::after {
	left: 55%;
	background: var(--color-theme-success);
}

.radio-switch label:nth-child(2) input+span::after {
	left: 55%;
	background: var(--color-theme-success);
}

.radio-switch label:nth-child(2) input:checked+span::after {
	left: .35em;
}

.radio-switch label:nth-child(2) input+span i.icon-dash {
	left: auto;
	right: .5em;
}

#buddypress table.notification-settings th.title,
#buddypress table.profile-settings th.title {
	width: 60%;
}

@media (max-width: 767px) {
	.socialv-about-info li {
		margin-bottom: 1em;
	}

	.notification-data {
		padding: 1em;
	}
}

@media (max-width: 479px) {
	.socialv-about-info li {
		width: 100%;
	}

	.socialv-about-info li:nth-last-child(-n+2) {
		margin-bottom: 1em;
	}
}

/* Tab Notification */
#buddypress .table-data-action>a.btn {
	height: 2.186em;
	width: 2.186em;
	line-height: 2.186em;
	font-size: 1.2em;
	margin: 0 .2em;
	display: inline-block;
	text-align: center;
}


.notifications-options-nav,
.messages-options-nav,
.invitations-options-nav {
	display: flex;
	align-items: center;
	justify-content: space-between;
}

/*==========
resposive
===============*/
@media(max-width: 696px) {
	.messages .text-end .messages-options-nav {
		margin-top: 1em;
	}
}

.notifications-options-nav input,
.messages-options-nav input,
.invitations-options-nav input {
	margin-left: 1em;
	width: auto;
}

.notifications-options-nav .select2-container,
.messages-options-nav .select2-container,
.invitations-options-nav .select2-container {
	width: auto !important;
}

#buddypress table.notifications thead tr {
	background: var(--global-body-bgcolor);
}

#buddypress table.notifications tr td {
	padding: 1em;
	border: none;
	border-bottom: .0625em solid var(--border-color-light);
}

#buddypress table.notifications tr td.notification-actions,
#buddypress table.messages-notices tr td.table-data-action {
	min-width: 10em;
}

#buddypress table.notifications tr td.bulk-select-check {
	padding: 0;
}

@media (max-width: 1199px) {

	#buddypress table.notifications tr th.actions.text-center,
	#buddypress table.messages-notices tr th.thread-options {
		width: 120px;
		display: block;
	}
}

/* Tab Message */
#buddypress .messages-notices td.thread-from .thread-avatar,
#buddypress .messages-notices td.thread-from .thread-details {
	display: inline-block;
	vertical-align: middle;
}

#buddypress .messages-notices td.thread-from .thread-details a {
	color: var(--global-font-title);
}

#buddypress span.activity {
	font-size: var(--font-size-small);
	color: var(--global-font-color);
}

#buddypress .messages-notices td.thread-from .thread-details .thread-count {
	color: var(--global-font-title);
}

#buddypress #message-threads .thread-info a {
	color: var(--global-font-title);
}

#buddypress table.forum tr.alt td,
#buddypress table.messages-notices tr.alt td,
#buddypress table.notifications tr.alt td,
#buddypress table.notifications-settings tr.alt td,
#buddypress table.profile-fields tr.alt td,
#buddypress table.profile-settings tr.alt td,
#buddypress table.wp-profile-fields tr.alt td {
	background: transparent;
	color: inherit;
}

.standard-form ul.acfb-holder li {
	float: inherit;
}

/* message chat */
.friend-tab {
	margin-bottom: 2em;
}

.friend-tab img {
	height: 2.5em;
	width: 2.5em;
	min-width: 2.5em;
	margin-right: 1em;
	border-radius: var(--border-radius);
}

.friend-tab a {
	color: var(--global-font-title);
}

.form-editor-box .friend-tab {
	display: inline-block;
	padding: .1em .5em;
	background: var(--disable-color);
	border-radius: var(--border-radius);
	margin-right: 1em;
	margin-bottom: .5em;
}

.form-editor-box .friend-tab a {
	font-size: .8em;
	display: inline-block;
	vertical-align: middle;
}

.form-editor-box .friend-tab img {
	min-width: 1.5em;
	height: 1.5em;
	width: 1.5em !important;
	margin-right: 0;
}

.ac_results .ac_over {
	background: var(--global-body-bgcolor);
	color: var(--global-font-color);
	font-size: var(--font-size-normal);
}

.ac_results .ac_results li {
	padding: 1em;
}

.ac_results .ac_results li img {
	border-radius: var(--border-radius);
}

#buddypress div#message-thread p#message-recipients {
	margin: .5em 0 2em;
	display: flex;
	align-items: center;
	justify-content: space-between;
	flex-wrap: wrap;
	gap: 1em;
}

#buddypress div#message-thread .data-buttons a+a {
	margin-left: 1em;
}

#buddypress div#message-thread .data-buttons {
	margin-top: -5.313em;
}

#buddypress div#message-thread div.message-box {
	padding: 2em;
	background: var(--global-body-bgcolor);
	border-radius: var(--border-radius);
	margin-bottom: 2em;
}

#buddypress div#message-thread .socialv-single-message-top img.avatar {
	float: inherit;
	margin: 0;
	border-radius: var(--border-radius);
}

.socialv-single-message-top {
	display: flex;
	align-items: center;
	gap: 1.25em;
}

#buddypress div#message-thread div.message-content {
	margin-left: 0;
	margin-top: 1.5em;
}

#buddypress div#message-thread div.message-content p {
	margin: 0;
}

#buddypress div#message-thread img.avatar {
	float: inherit;
}

#send-reply .message-metadata {
	margin-bottom: 1.5em;
}

@media (max-width: 1199px) {
	#buddypress div#message-thread .data-buttons {
		margin-top: 0;
	}
}

/* Tab Video, Audio, Photo */
.mpp-media-entry a img {
	width: 100%;
	height: 100%;
	object-fit: cover;
}

.mfp-image-holder .mfp-close,
.mfp-iframe-holder .mfp-close {
	background: var(--color-theme-white-box);
	color: var(--global-font-title);
	left: auto;
	top: -.7em;
	right: -.3em;
	width: 1.3em;
	height: 1.3em;
	line-height: 1.3em;
	text-align: center;
	padding: 0;
	border-radius: var(--border-radius);
	cursor: pointer;
	opacity: 1;
}

.mfp-close-btn-in .mfp-close:hover {
	background: var(--color-theme-white-box);
	color: var(--global-font-title);
	cursor: pointer;
}

.mfp-arrow {
	height: 2em;
	width: 2em;
	line-height: 2em;
	text-align: center;
	font-size: 2em;
	opacity: 1;
}

.mfp-arrow:before,
.mfp-arrow:after {
	font-family: 'iconly';
	font-weight: 200;
	border: none;
	margin: 0;
	top: 0;
	left: 0;
	right: 0;
	border: 0;
	height: auto;
	width: auto;
}

.mfp-arrow-left:after {
	content: "\e908";
}

.mfp-arrow-right:after {
	content: "\e90d";
}

.mfp-arrow.mfp-arrow-left {
	left: 3em;
}

.mfp-arrow.mfp-arrow-right {
	right: 3em;
}

.mfp-arrow:before {
	display: none;
}

.mpp-widget-video-item .video-wrap.user-video video {
	height: 100%;
	width: 100%;
}

.mpp-item {
	padding-top: 0;
	padding-bottom: 0;
	margin-bottom: 2em;
	line-height: 0;
}

.bp-messages-wrap .expandingButtons .bpbm-dropdown-menu {
	background-color: var(--color-theme-white-box);
	border-color: var(--border-color-light);
	box-shadow: var(--global-box-shadow);
	color: var(--global-font-color);
	border-radius: var(--border-radius);
}

.bp-messages-wrap.bp-messages-full-screen .expandingButtons .bpbm-dropdown-menu {
	right: 20px;
}

.bp-messages-wrap .expandingButtons .bpbm-dropdown-menu .bpbm-dropdown-item {
	color: var(--global-font-color);
}

.bp-messages-wrap .expandingButtons .bpbm-dropdown-menu .bpbm-dropdown-item:hover {
	color: var(--color-theme-primary);
}

.bp-messages-wrap .bpbm-user-options .bpbm-user-blacklist .socialv-bbp_blockuser_main+.bpbm-user-blacklist-empty {
	display: none;
}

.bp-messages-wrap .bpbm-user-options .bpbm-user-option {
	margin-top: 0;
	margin-bottom: 2em;
}

/* media */

form#mpp-whats-new-form textarea,
div.mpp-activity-comments form textarea {
    background: var(--global-body-bgcolor);
    padding: 1em;
    font-size: 1em;
    min-height: 11.554em;
	width: 100%;
    color: var(--global-font-color);
	border: 1px solid var(--border-color-light);
}

form#mpp-whats-new-form #mpp-whats-new-submit {
	margin-top: 0;
	height: auto;
}

@media (min-width:1200px) {
	.mpp-audio-list .mpp-item:nth-last-child(-n+3) {
		margin-bottom: 0;
	}
}

@media (min-width: 992px) {
	.mpp-video-list .mpp-item:nth-last-child(-n+3) {
		margin-bottom: 0;
	}
}

@media (min-width: 768px) {
	.mpp-photo-list .mpp-item:nth-last-child(-n+4) {
		margin-bottom: 0;
	}
}

@media screen and (min-width: 768px) and (max-width: 992px) {
	.mpp-photo-list .mpp-item:nth-last-child(-n+3) {
		margin-bottom: 0;
	}
}

@media screen and (min-width: 421px) and (max-width: 767px) {
	.mpp-photo-list .mpp-item:nth-last-child(-n+2) {
		margin-bottom: 0;
	}
}

@media (min-width:576px) {
	.mpp-audio-list .mpp-item:nth-last-child(-n+2) {
		margin-bottom: 0;
	}

	.mpp-video-list .mpp-item:nth-last-child(-n+2) {
		margin-bottom: 0;
	}
}

@media (max-width: 575px) {
	.mpp-audio-list .mpp-item:last-child {
		margin-bottom: 0;
	}

	.mpp-video-list .mpp-item:last-child {
		margin-bottom: 0;
	}
}

@media (max-width: 767px) {
	.mpp-item {
		margin-bottom: 1em;
	}
}

@media (max-width: 420px) {
	.mpp-photo-list .mpp-item:last-child {
		margin-bottom: 0;
	}
}

/* Group tab */
#buddypress a.bp-secondary-action,
#buddypress span.highlight {
	font-size: var(--font-size-normal);
	font-weight: var(--font-weight-regular);
}

#buddypress .info-meta span.activity {
	font-size: var(--font-size-normal);
}

.list-view .socialv-groups-lists.row>[class*="col-"] {
	width: 100%;
}

.list-view .socialv-groups-lists .socialv-group-info .text-center {
	display: flex;
	align-items: center;
	justify-content: space-between;
	gap: 1em;
	padding: 2em 2em 2em 7em;
	position: relative;
}

.list-view .top-bg-image {
	position: absolute;
	top: 0;
	bottom: 0;
}

.list-view .socialv-groups-lists .group-has-avatar .socialv-group-info .cover-img {
	width: 5em;
	height: 100%;
}

.list-view .socialv-groups-lists .group-has-avatar .socialv-group-info .cover-img img {
	height: 100%;
	width: 100%;
}

.list-view .socialv-groups-lists .socialv-group-info .group-icon {
	position: relative;
	margin: 0;
}

.list-view .socialv-groups-lists .socialv-group-info .group-name {
	margin-bottom: 0;
}

.list-view .socialv-groups-lists .socialv-group-info .socialv-group-details {
	padding-bottom: 0;
}

.list-view .socialv-groups-lists .group-member {
	padding: 0;
	border: none;
	min-width: 7rem;
}

.list-view .group-button.generic-button {
	margin: 0 -1em;
}

.list-view .group-header {
	display: flex;
	align-items: center;
	gap: 1em;
	margin-left: -5em;
}

@media (max-width: 767px) {
	.list-view .socialv-groups-lists .socialv-group-info .text-center {
		flex-direction: column;
		gap: 2em;
		padding: 2em 1em;
	}

	.list-view .top-bg-image {
		display: none;
	}
}

/* Profile Page css End */

/* Group Detail Page */
.socialv-group-left {
	margin-top: -1em;
	position: relative;
}

.socialv-group-left .header-avatar {
	margin-top: -3em;
}

.socialv-group-right {
	display: flex;
	align-items: center;
	gap: 1em;
	padding-top: 1em;
}

.socialv-group-left .avtar-details p {
	font-size: var(--font-size-normal);
	margin-bottom: 0;
	margin-top: .5em;
	width: 90%;
}

.socialv-group-left .avtar-details .socialv_group_type_list{
	width: 100%;
}

.description-content.hideContent {
	overflow: hidden;
	line-height: 1em;
	height: 3.5em;
}

.description-content.showContent {
	line-height: 1em;
	height: auto;
}

.description-content {
	height: auto;
	overflow: hidden;
}

.socialv-group-btn-action .group-button.generic-button {
	justify-content: end;
	margin: 0;
}

.radio.invitations-list label {
	display: block;
}

.socialv-member-left.item-avatar img {
	border-radius: var(--border-radius);
}

.socialv-group-right .socialv-group-btn-action {
	display: flex;
	align-items: center;
	gap: 1em;
}

@media (max-width: 991px) {
	.socialv-group-right {
		margin-top: 2em;
	}

	.socialv-group-profile-box #item-header-content {
		flex-direction: column;
	}
}

@media (max-width: 767px) {
	.socialv-group-right {
		flex-direction: column;
		margin-top: 1em;
		width: 100%;
	}

	.group-profile-details {
		display: block !important;
		text-align: center;
	}

	.socialv-group-left .avtar-details {
		padding-left: 0;
		margin-top: 2em;
	}

	.socialv-group-left .avtar-details p {
		width: 70%;
		margin: .5em auto 0;
	}

	.socialv-group-profile-box #item-header-content {
		align-items: center !important;
	}
}

@media (max-width: 479px) {
	.socialv-group-left .avtar-details p {
		width: 100%;
	}
}


/* send invites */
#buddypress form#send-invite-form {
	margin-top: 0;
}

#buddypress div#invite-list {
	width: 100%;
	background: var(--global-body-bgcolor);
	padding: 1.25em;
	border-radius: var(--border-radius);
}

#buddypress div#invite-list ul li {
	margin-bottom: 1em;
}

#buddypress div#invite-list label {
	font-size: 1em;
	color: var(--global-font-color);
	font-weight: var(--font-weight-regular);
	line-height: 1.838em;
}

div#invite-list input[type=checkbox] {
	height: 1.25em;
	width: 1.25em;
	line-height: normal;
	border: none;
	background: var(--color-theme-white-box);
	border-radius: var(--border-radius);
	vertical-align: middle;
}

/* manage -> member */
.group-members-list .user-data {
	display: flex;
	align-items: center;
	gap: 1em;
}

.group-members-list .user-data .joined.item-meta {
	margin: 0;
	font-size: var(--font-size-normal);
}

#buddypress .group-members-list ul.item-list li img.avatar {
	margin: 0;
}

.bp-messages-wrap .threads-list .thread .bm-info h4 .bm-thread-icon {
	color: var(--global-font-title);
}

h4.socialv-setting-title,
.socialv h4.socialv-setting-title {
	margin: 1em 0;
}

#buddypress .group-members-list ul.item-list li {
	display: flex;
	align-items: center;
	justify-content: space-between;
	gap: 1em;
	padding: 2em 0;
}

#buddypress .group-members-list ul.item-list li:last-child {
	padding-bottom: 0;
}

#buddypress ul.item-list li div.action {
	position: static;
}

#buddypress .group-members-list .action a.button {
	background: var(--color-theme-primary-light);
	color: var(--color-theme-primary);
	border-color: transparent;
	margin: .2em .3em;
	font-size: 1em;
	padding: 0;
	height: 2.4982em;
	width: 2.4982em;
	line-height: 2.4em;
	text-align: center;
}

#buddypress .group-members-list .action a.button:first-child {
	margin-left: 0;
}

#buddypress .group-members-list ul.item-list {
	border: none;
}

#buddypress .group-members-list:first-child {
	margin: 0;
}

#buddypress .group-members-list {
	margin: 1.5em 0 0;
}

#buddypress .group-members-list .section-header {
	border-bottom: .0625em solid var(--border-color-light);
	padding-bottom: .5em;
}

#buddypress ul.item-list li {
	padding: 2em 0;
}

#buddypress ul.item-list li:last-child {
	border-bottom: none;
}

#buddypress .group-members-list #message.info p {
	margin: 0;
}

/* manage -> setting */
.radio-data-box {
	background: var(--global-body-bgcolor);
	padding: 1em;
	border-radius: var(--border-radius);
	margin-bottom: 2em;
}

.radio-data-box ul.socialv-group-data {
	padding-left: 2.6em;
}

.radio-data-box ul li {
	font-size: var(--font-size-normal);
}

fieldset.group-create-invitations {
	margin-top: 2em;
}

/* manage tab -> forum */
#group-settings-form h2 {
	font-size: 1.44em;
	font-size: var(--font-size-h4);
	line-height: 1.3;
	line-height: var(--font-line-height-h4);
	letter-spacing: 0;
	letter-spacing: var(--font-letter-spacing-h4);
	font-weight: 500;
	font-weight: var(--font-weight-h4);
}

#group-settings-form .field-group .checkbox {
	background: var(--global-body-bgcolor);
	padding: 1em;
	border-radius: var(--border-radius);
}

#group-settings-form .field-group .description {
	margin-top: .5em;
	font-style: italic;
}

#group-settings-form .field-group {
	position: relative;
}

#group-settings-form fieldset input[type="submit"] {
	width: auto;
	float: right;
	color: var(--color-theme-white);
	background: var(--color-theme-success);
	border-color: var(--color-theme-success);
	font-size: var(--font-size-normal);
	font-family: var(--highlight-font-family);
	letter-spacing: var(--letter-spacing-one);
	font-weight: var(--font-weight-semi-bold);
	line-height: var(--font-line-height-body);
	border-radius: var(--border-radius);
	padding: .813em 2em;
	border: .063em solid transparent;
	line-height: normal;
	display: inline-block;
	text-transform: uppercase;
	transition: all .45s ease-in-out;
	-moz-transition: all .45s ease-in-out;
	-ms-transition: all .45s ease-in-out;
	-o-transition: all .45s ease-in-out;
	-webkit-transition: all .45s ease-in-out;
}

#group-settings-form fieldset input[type="submit"]:hover {
	color: var(--color-theme-white);
	background: var(--color-theme-success-dark);
	border-color: var(--color-theme-success-dark);
}

.replies-tab>ul {
	list-style: none;
	padding: 0;
}

.replies-tab .main-bp-details>*:not(.socialv_topic_reply_details) ul {
	list-style: none;
	padding: 0;
}

.bpbm-private-message-link-buddypress {
	color: var(--color-theme-primary);
}

@media (max-width: 767px) {
	#buddypress .group-members-list ul.item-list li {
		flex-direction: column;
		justify-content: start;
		align-items: flex-start;
		padding: 1em 0;
	}
}

@media (max-width: 320px) {

	#buddypress li div.item,
	#buddypress ul.item-list li div.action {
		margin-left: 0;
	}
}

/* buddypress css overide start */
#buddypress table.forum,
#buddypress table.messages-notices,
#buddypress table.notifications,
#buddypress table.notifications-settings,
#buddypress table.profile-fields,
#buddypress table.profile-settings,
#buddypress table.wp-profile-fields,
#buddypress table.invitations {
	border-collapse: separate;
	border-spacing: 0;
	border-radius: var(--border-radius);
}

#buddypress table.invitations tr td {
	position: relative;
}

#buddypress table.forum tr th,
#buddypress table.profile-settings tr th,
#buddypress table.messages-notices tr th,
#buddypress table.notifications tr th,
#buddypress table.notifications-settings tr th,
#buddypress table.profile-fields tr th,
#buddypress table.wp-profile-fields tr th,
#buddypress table.invitations tr th {
	font-size: 1em;
	font-weight: var(--font-weight-medium);
	padding: 1em;
	border-color: transparent;
}

#buddypress table tr:first-child th:first-child {
	border-top-left-radius: var(--border-radius);
}

#buddypress table tr:first-child th:last-child {
	border-top-right-radius: var(--border-radius);
}

#buddypress table tr:last-child td:first-child {
	border-bottom-left-radius: var(--border-radius);
}

#buddypress table tr:last-child td:last-child {
	border-bottom-right-radius: var(--border-radius);
}

#buddypress table.invitations tr th.actions {
	min-width: 9em;
}

#buddypress table.forum thead tr,
#buddypress table.messages-notices thead tr,
#buddypress table.notifications thead tr,
#buddypress table.notifications-settings thead tr,
#buddypress table.profile-fields thead tr,
#buddypress table.profile-settings thead tr,
#buddypress table.wp-profile-fields thead tr,
#buddypress table.invitations thead tr {
	background: var(--color-theme-primary);
	color: var(--color-theme-white);
}

#buddypress table.forum tr td,
#buddypress table.messages-notices tr td,
#buddypress table.notifications tr td,
#buddypress table.notifications-settings tr td,
#buddypress table.profile-fields tr td,
#buddypress table.profile-settings tr td,
#buddypress table.wp-profile-fields tr td,
#buddypress table.invitations tr td {
	padding: 1em;
	border: none;
}

#buddypress table.forum tr:nth-child(n+1),
#buddypress table.messages-notices tr:nth-child(n+1),
#buddypress table.notifications tr:nth-child(n+1),
#buddypress table.notifications-settings tr:nth-child(n+1),
#buddypress table.profile-fields tr:nth-child(n+1),
#buddypress table.profile-settings tr:nth-child(n+1),
#buddypress table.wp-profile-fields tr:nth-child(n+1),
#buddypress table.invitations tr:nth-child(n+1) {
	position: relative;
}

#buddypress table.forum tr:nth-child(n+1):after,
#buddypress table.messages-notices tr:nth-child(n+1):after,
#buddypress table.notifications tr:nth-child(n+1):after,
#buddypress table.notifications-settings tr:nth-child(n+1):after,
#buddypress table.profile-fields tr:nth-child(n+1):after,
#buddypress table.profile-settings tr:nth-child(n+1):after,
#buddypress table.wp-profile-fields tr:nth-child(n+1):after,
#buddypress table.invitations tr:nth-child(n+1):after {
	content: "";
	position: absolute;
	left: 1.5em;
	right: 1.5em;
	border-bottom: .0625em solid var(--border-color-light);
	bottom: 0;
}

#buddypress table.forum tr:last-child:after,
#buddypress table.messages-notices tr:last-child:after,
#buddypress table.notifications tr:last-child:after,
#buddypress table.notifications-settings tr:last-child:after,
#buddypress table.profile-fields tr:last-child:after,
#buddypress table.profile-settings tr:last-child:after,
#buddypress table.wp-profile-fields tr:last-child:after,
#buddypress table.invitations tr:last-child:after {
	display: none;
}


#buddypress .standard-form textarea,
#buddypress .standard-form input[type=text] {
	width: 100%;
}

#buddypress div.pagination#pag-bottom {
	padding: 1.5em 0 0;
}

.message-action-star span.icon:before {
	color: inherit;
}

#buddypress table#message-threads tr.unread td {
	background-color: var(--unread-message-color);
	border-color: var(--unread-message-color);
}

.avatars-history {
	flex-direction: column;
}

.avatars-history .avatar-history-list {
	width: 100%;
	margin-top: 1em;
}

.avatars-history .avatar-history-actions {
	margin-left: 0;
	margin-top: 1.5em;
}

.avatar-history-list label {
	display: flex;
	align-items: center;
	gap: 1em;
	margin: auto;
	width: auto;
}

.avatar-history-actions button {
	margin-top: 1.5em;
}

.historic-avatar .bp-screen-reader-text {
	clip: auto;
	position: static !important;
}

.bp-screen-reader-text:focus {
	box-shadow: none;
	width: 1em;
	height: 1em;
	border: none;
	font-size: inherit;
}

.message-action-unstar span.icon:before {
	color: var(--color-theme-warning);
}

#pass-strength-result,
.ac-form.socialv-comment-form,
.gamipress-spinner {
	display: none;
}

.avatar-history-actions button.avatar-history-action.recycle:not(.disabled) {
	background: var(--color-theme-primary-light);
	color: var(--color-theme-primary);
	border-color: var(--color-theme-primary-light);
}

.avatar-history-actions button.delete:not(.disabled) {
	background: var(--color-theme-danger-light);
	color: var(--color-theme-danger);
	border-color: var(--color-theme-danger-light);
}

#message_content {
	height: 6.25em;
}

/* buddypress css overide end */

/* all message */

#buddypress #message.info p+p {
	margin-top: 1.5em;
}

#buddypress div#message p,
#sitewide-notice #message {
	border: none;
	border-left: .1875em solid var(--color-theme-info);
	background-color: var(--color-theme-info-light);
	color: var(--color-theme-info);
	margin: 0;
	padding: 1em;
	border-radius: 0 var(--border-radius) var(--border-radius) 0;
	-webkit-border-radius: 0 var(--border-radius) var(--border-radius) 0;
	-moz-border-radius: 0 var(--border-radius) var(--border-radius) 0;
	-o-border-radius: 0 var(--border-radius) var(--border-radius) 0;
	-ms-border-radius: 0 var(--border-radius) var(--border-radius) 0;
}

#buddypress div#message.updated p {
	border: none;
	border-left: .1875em solid var(--color-theme-success);
	background-color: var(--color-theme-success-light);
	color: var(--color-theme-success);
}

#buddypress div#message.error p {
	border: none;
	border-left: .1875em solid var(--color-theme-danger);
	background-color: var(--color-theme-danger-light);
	color: var(--color-theme-danger);
	margin: 0;
	padding: 1em;
	border-radius: 0 var(--border-radius) var(--border-radius) 0;
	-webkit-border-radius: 0 var(--border-radius) var(--border-radius) 0;
	-moz-border-radius: 0 var(--border-radius) var(--border-radius) 0;
	-o-border-radius: 0 var(--border-radius) var(--border-radius) 0;
	-ms-border-radius: 0 var(--border-radius) var(--border-radius) 0;
}

#buddypress #message.info+#message.info {
	margin-top: 1.5em;
}

/* warning message */
#buddypress p.warning,
body.profile_page_bp-profile-edit.modal-open #TB_ajaxContent p.warning,
body.users_page_bp-profile-edit.modal-open #TB_ajaxContent p.warning {
	border: none;
	border-left: .1875em solid var(--color-theme-danger);
	background-color: var(--color-theme-danger-light);
	color: var(--color-theme-danger);
	margin: 0;
	padding: 1em;
	border-radius: 0 var(--border-radius) var(--border-radius) 0;
	-webkit-border-radius: 0 var(--border-radius) var(--border-radius) 0;
	-moz-border-radius: 0 var(--border-radius) var(--border-radius) 0;
	-o-border-radius: 0 var(--border-radius) var(--border-radius) 0;
	-ms-border-radius: 0 var(--border-radius) var(--border-radius) 0;
}

/* post detail Page */
#buddypress ul.item-list.activity-list li.has-comments {
	padding: 2em;
}

body.activity-permalink #buddypress .activity-list>li:first-child {
	padding-top: 2em;
}

body.activity-permalink #buddypress ul.activity-list li.has-comments {
	padding-bottom: 2em;
}

body.activity-permalink #buddypress .activity-list li .activity-content,
body.activity-permalink #buddypress div.activity-comments {
	margin-left: 0;
}

body.activity-permalink #buddypress div.activity-comments>ul {
	padding: 0;
}

body.activity-permalink #buddypress div.activity-comments ul li>ul {
	margin-top: 1.5em;
}

/* new post btn */
#buddypress .activity-list.socialv-list-post li.load-newest {
	position: fixed;
	top: var(--header-height);
	margin: 0 auto;
	left: 0;
	right: 0;
	z-index: 99;
	background: transparent;
	padding: 0;
	background: rgba(0, 0, 0, .5);
	color: var(--color-theme-white);
	font-size: var(--font-size-normal);
	text-align: center;
	width: 9em;
}

.admin-bar #buddypress .activity-list.socialv-list-post li.load-newest {
	margin-top: 32px;
}

.sidebar-boxed+.main-content #buddypress .activity-list.socialv-list-post li.load-newest {
	margin-top: 16px;
}

.admin-bar .sidebar-boxed+.main-content #buddypress .activity-list.socialv-list-post li.load-newest {
	margin-top: 32px;
}

#buddypress .activity-list.socialv-list-post li.load-newest>a {
	padding: .5em 1em;
	width: 9em;
	color: var(--color-theme-white);
	display: inline-block;
	font-size: var(--font-size-normal);
}

@media (max-width: 782px) {
	.admin-bar #buddypress .activity-list.socialv-list-post li.load-newest {
		margin-top: 46px;
	}
}

.bm_user_selector .bm_user_selector__multi-value .bm_user_selector__multi-value__remove svg {
	vertical-align: inherit;
}

.bp-messages-wrap .bm-messages-list .bm-list .bm-conversation-start {
	color: var(--global-font-color) !important;
	opacity: 1;
	font-size: var(--font-size-small);
}

.bp-messages-wrap .bm-messages-list .bm-list .bm-date-stack .bm-unread-messages {
	color: var(--global-font-color) !important;
	opacity: 1;
}

/* Badges Page */
.badge-box {
	width: 100%;
	padding: 3em 2em;
	background: var(--global-body-bgcolor);
	border: .063em solid var(--border-color-light);
	border-radius: var(--border-radius);
}

.badge-box .badge-icon {
	margin-bottom: 2em;
}

.badge-box .badge-title {
	margin-bottom: .5em;
}

.badge-box .badge-desc {
	font-size: var(--font-size-normal);
	font-weight: var(--font-weight-regular);
}

.badge-box .badge-desc p {
	color: var(--global-font-color);
	margin: 0;
}

.badge-box .badge-member-info {
	background: var(--color-theme-white-box);
	padding: 1.5em;
	margin-top: 2em;
	border-radius: var(--border-radius);
}

.badge-member-info .list-img-group {
	padding: 0;
}

.badge-member-info .socialv-achievement-earn-user {
	color: var(--global-font-color);
	font-size: var(--font-size-normal);
	font-weight: var(--font-weight-medium);
	line-height: 1.74em;
}

.badge-box .badge-icon img {
	border-radius: var(--border-radius);
	object-fit: contain;
}

@media (min-width:1199px) and (max-width:1365px) {
	.badge-box {
		padding: 2em 1em;
	}

	.badge-box .badge-desc p {
		font-size: var(--font-size-small);
	}
}


/* badge details */
.entry.badge {
	display: inherit;
	padding: 0;
	white-space: inherit;
	border-radius: inherit;
	font-size: inherit;
	font-weight: inherit;
	line-height: inherit;
	color: inherit;
	text-align: inherit;
	vertical-align: inherit;
}

/* gamipress css */
[class*="gamipress-columns"] {
	width: auto;
	margin: 0 -1em;
}

.widget-area [class*=gamipress-columns] {
	width: 100%
}

.gamipress-achievement:not(.gamipress-layout-none),
.gamipress-rank:not(.gamipress-layout-none),
.single-achievement.achievement-wrap:not(.gamipress-layout-none),
.single-rank.rank-wrap:not(.gamipress-layout-none),
s .gamipress-user-points:not(.gamipress-layout-none) {
	margin-bottom: 2em;
}

.gamipress-columns-6 .badge-box {
	padding: 2em 1em;
}

.gamipress-columns-6 .badge-box .badge-member-info {
	padding: 1em;
}

.gamipress-achievement:not(.gamipress-layout-none) .badge-box,
.gamipress-rank:not(.gamipress-layout-none) .badge-box,
.single-achievement.achievement-wrap:not(.gamipress-layout-none) .badge-box,
.single-rank.rank-wrap:not(.gamipress-layout-none) .badge-box,
.gamipress-user-points:not(.gamipress-layout-none) .badge-box {
	display: flex;
	flex-wrap: wrap;
	align-self: flex-start;
}

.gamipress-achievement.gamipress-layout-right .badge-box {
	flex-direction: row-reverse;
}

.gamipress-achievement.gamipress-layout-bottom .badge-box,
.gamipress-rank.gamipress-layout-bottom .badge-box,
.single-achievement.gamipress-layout-bottom .badge-box,
.single-rank.gamipress-layout-bottom .badge-box,
.gamipress-user-points.gamipress-layout-bottom .gamipress-points .badge-box,
.gamipress-points-types.gamipress-layout-bottom .gamipress-points-type .badge-box {
	flex-direction: column-reverse;
}

.gamipress-achievement-congratulations,
.gamipress-rank-congratulations {
	background-color: var(--global-body-bgcolor);
	padding: 1em;
}

.socialv-blog-box .gamipress-achievement-congratulations p,
.socialv-blog-box .gamipress-rank-congratulations p {
	margin: 0;
}

.socialv-profile-center .gamipress-buddypress-points {
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 1em;
}

.gamipress-shortcode-error {
    color: var(--color-theme-danger);
    background-color: var(--color-theme-danger-light);
}

@media (min-width:768px) and (max-width:1199px) {

	.gamipress-columns-3>.gamipress-achievement,
	.gamipress-columns-3>.gamipress-points,
	.gamipress-columns-3>.gamipress-points-type,
	.gamipress-columns-3 .gamipress-rank {
		width: 50%;
	}

	.gamipress-columns-4>.gamipress-achievement,
	.gamipress-columns-4>.gamipress-points,
	.gamipress-columns-4>.gamipress-points-type,
	.gamipress-columns-4 .gamipress-rank {
		width: 50%;
	}

	.gamipress-columns-5>.gamipress-achievement,
	.gamipress-columns-5>.gamipress-points,
	.gamipress-columns-5>.gamipress-points-type,
	.gamipress-columns-5 .gamipress-rank {
		width: 50%;
	}
}

@media (min-width: 768px) {

	#gamipress-achievements-container[class*="gamipress-columns"]>.gamipress-achievement,
	.gamipress-user-points[class*="gamipress-columns"]>.gamipress-points,
	.gamipress-points-types[class*="gamipress-columns"]>.gamipress-points-type,
	.gamipress-ranks-container[class*="gamipress-columns"] .gamipress-rank {
		padding: 0 1em;
	}

	.gamipress-achievement:not(.gamipress-layout-none) :nth-last-child(-n+2),
	.gamipress-rank:not(.gamipress-layout-none) :nth-last-child(-n+2),
	.gamipress-user-points:not(.gamipress-layout-none) :nth-last-child(-n+2),
	.single-achievement.achievement-wrap:not(.gamipress-layout-none) :nth-last-child(-n+2),
	.single-rank.rank-wrap:not(.gamipress-layout-none) :nth-last-child(-n+2) {
		margin-bottom: 0;
	}
}

@media (max-width: 767px) {
	[class*=gamipress-columns] {
		margin: 0;
	}

	.badge-box {
		padding: 1em;
	}

	.badge-box .badge-member-info {
		padding: 1em;
	}

	.mfp-arrow {
		margin-top: -32px;
	}

	.mfp-iframe-holder .mfp-close,
	.mfp-image-holder .mfp-close {
		right: 0;
	}

	.mfp-arrow.mfp-arrow-right {
		right: 1em;
	}

	.mfp-arrow.mfp-arrow-left {
		left: 1em;
	}

	.socialv-login-form .socialv-info p {
		padding: 0 1em;
	}
}

/* details page end */
.socialv-tab-container {
	margin: 0 .625em;
	white-space: nowrap;
	overflow-x: auto;
}

.card-inner.post-inner-block {
	padding: 2em;
}

/*page members */
.messages-notices .bp-tooltip:after {
	display: none;
}

.stories.snapgram .story.seen>.item-link>.item-preview {
	background: var(--border-color-light) !important;
}

.mpp-upload-container-active {
	display: block !important;
}

.mpp-upload-container-inactive .mpp-upload-container-close {
	display: none;
}

.clear-both {
	clear: both;
}

#buddypress .dir-search input[type=text]:focus {
	border-color: var(--color-theme-primary);
}

#bbpress-forums .status-pending.even,
#bbpress-forums .status-pending.odd,
#bbpress-forums ul.status-pending a,
#bbpress-forums .status-spam.even,
#bbpress-forums .status-trash.even {
	background: var(--color-theme-white-box);
}

.border-radius-box {
	border-radius: var(--border-radius-box);
	overflow: hidden;
}

div.bbp-template-notice,
div.indicator-hint {
	background-color: var(--color-theme-info-light);
	border-color: var(--color-theme-info-light);
	border-left: .188em solid var(--color-theme-info);
	padding: 1em;
	margin: 2em 0;
}

div.bbp-template-notice ul {
	margin: 0;
	list-style: none;
	padding: 0;
}

#buddypress .dir-search input[type=search],
#buddypress .dir-search input[type=text],
#buddypress .groups-members-search input[type=search],
#buddypress .groups-members-search input[type=text],
#buddypress .standard-form input[type=color],
#buddypress .standard-form input[type=date],
#buddypress .standard-form input[type=datetime-local],
#buddypress .standard-form input[type=datetime],
#buddypress .standard-form input[type=email],
#buddypress .standard-form input[type=month],
#buddypress .standard-form input[type=number],
#buddypress .standard-form input[type=password],
#buddypress .standard-form input[type=range],
#buddypress .standard-form input[type=search],
#buddypress .standard-form input[type=tel],
#buddypress .standard-form input[type=text],
#buddypress .standard-form input[type=time],
#buddypress .standard-form input[type=url],
#buddypress .standard-form input[type=week],
#buddypress .standard-form select,
#buddypress .standard-form textarea {
	border: .0625em solid var(--border-color-light);
	padding-right: 3.125em;
	padding-left: 1em;
	border-radius: var(--border-radius);
	background: var(--global-body-bgcolor);
}

#buddypress .dir-search input[type=search]:focus,
#buddypress .dir-search input[type=text]:focus,
#buddypress .groups-members-search input[type=search]:focus,
#buddypress .groups-members-search input[type=text]:focus,
#buddypress .standard-form input[type=color]:focus,
#buddypress .standard-form input[type=date]:focus,
#buddypress .standard-form input[type=datetime-local]:focus,
#buddypress .standard-form input[type=datetime]:focus,
#buddypress .standard-form input[type=email]:focus,
#buddypress .standard-form input[type=month]:focus,
#buddypress .standard-form input[type=number]:focus,
#buddypress .standard-form input[type=password]:focus,
#buddypress .standard-form input[type=range]:focus,
#buddypress .standard-form input[type=search]:focus,
#buddypress .standard-form input[type=tel]:focus,
#buddypress .standard-form input[type=text]:focus,
#buddypress .standard-form input[type=time]:focus,
#buddypress .standard-form input[type=url]:focus,
#buddypress .standard-form input[type=week]:focus,
#buddypress .standard-form select:focus,
#buddypress .standard-form textarea:focus {
	color: var(--global-font-title);
	border-color: var(--color-theme-primary);
	background: transparent;
	box-shadow: none;
	outline: none;
}

#buddypress .activity-list li.mini .activity-comments {
	font-size: 100%;
}

#buddypress div.dir-search input[type=text],
#buddypress li.groups-members-search input[type=text] {
	padding-right: 3.125em;
	padding-left: 1em;
}

.socialv-bp-searchform .search-input {
	position: relative;
}

.socialv-bp-searchform .search-input .btn-search {
	position: absolute;
	width: 3.1em;
	height: 100%;
	background: var(--color-theme-primary) !important;
	right: 0;
	top: 50%;
	transform: translate(0, -50%);
	border-radius: 0 .3125em .3125em 0;
	padding: 0;
	border: none;
}

#buddypress .socialv-bp-searchform .search-input button.btn-search {
	padding: 0;
}

.socialv-bp-searchform .search-input .btn-search i {
	color: var(--color-theme-white);
}

#buddypress div.dir-search {
	float: inherit;
	margin: 0;
	width: 100%;
}


.card-main.socialv-search-main {
	margin-bottom: 2em;
}

.socialv-member-info {
	display: flex;
	gap: 1em;
	justify-content: space-between;
	align-items: start;
	padding: 1.5em;
	background: var(--global-body-bgcolor);
	margin-bottom: 2em;
	border-radius: var(--border-radius-box);
}
.socialv-members-lists .item-entrycol-12:last-child .socialv-member-info {
    margin-bottom: 0;
}
.socialv-members-lists .item-entry:last-child .socialv-member-info {
	margin-bottom: 0;
}

.socialv-member-info .socialv-member-right .socialv-e-last-activity {
	display: block;
	margin-bottom: 1em;
}

.socialv-member-info .socialv-member-main {
	display: flex;
	align-items: center;
	gap: 1em;
}

.socialv-member-info-top {
	line-height: normal;
}

.socialv-member-info .socialv-member-right {
	text-align: right;
	display: inline-block;
	min-width: 10em;
}

.socialv-bp-main-box .socialv-member-info .socialv-member-right {
	min-width: 4em;
}

.socialv-member-info .socialv-member-main .member-name {
	line-break: anywhere;
}

.socialv-member-info div.friendship-button {
	display: inline-block;
}

.socialv-member-right .message-btn {
	display: inline-block;
	width: 1.5em;
	height: 1.5em;
	line-height: 2.5em;
	vertical-align: top;
	margin-left: 1em;
}

.socialv-member-right .message-btn i {
	font-size: 1.4em;
	color: var(--color-theme-primary);
}

/* #friend-list .request-btn a:first-child.reject,
#friend-list .request-btn a:first-child.accept {
    display: none !important;
} */

#buddypress .request-btn a.loading:hover,
#buddypress .request-btn input.loading:hover {
	color: var(--color-theme-white);
}

.socialv-member-info .member-name .title {
	margin-bottom: .375em;
	margin-top: 0;
}

.socialv-member-info .members-meta.action {
	padding: 0;
}

.socialv-member-info .members-meta.action li {
	list-style: none;
}

.socialv-member-info .members-meta.action li:first-child button {
	margin-right: 0;
}

.socialv-bp-pagination .page-numbers,
#bbpress-forums .bbp-pagination-links a,
#bbpress-forums .bbp-pagination-links span.current {
	position: relative;
	display: inline-block;
	width: 2.813em;
	height: 2.813em;
	text-align: center;
	line-height: 2.813em;
	margin-right: .625em;
	color: var(--global-font-color);
	background-color: var(--color-theme-white-box);
	border: .0625em solid var(--border-color-light);
	border-radius: var(--border-radius);
	opacity: 1;
}

.bbp-pagination-links.socialv-bp-pagination {
	margin-top: 0;
}

.bbp-user-section .bbp-pagination {
	display: flex;
	align-items: center;
	justify-content: space-between;
	flex-wrap: wrap;
	gap: 1em;
}

.bbp-pagination-count {
	font-size: var(--font-size-normal);
}

#bbpress-forums .bbp-pagination-links span.dots,
#bbpress-forums .bbp-pagination-links a {
	font-size: var(--font-size-xs);
}

.socialv-bp-pagination .page-numbers:hover {
	color: var(--color-theme-white);
	text-decoration: none;
	background-color: var(--color-theme-primary);
	border-color: var(--color-theme-primary);
}

.socialv-bp-pagination .page-numbers.current,
#bbpress-forums .bbp-pagination-links a:hover,
#bbpress-forums .bbp-pagination-links span.current,
#bbpress-forums .bbp-topic-pagination a:hover {
	width: 2.813em;
	height: 2.813em;
	z-index: 1;
	color: var(--color-theme-white);
	transition: all .5s ease-out 0s;
	-webkit-transition: all .5s ease-out 0s;
	-moz-transition: all .5s ease-out 0s;
	-ms-transition: all .5s ease-out 0s;
	-o-transition: all .5s ease-out 0s;
	background: var(--color-theme-primary);
	border-color: var(--color-theme-primary);
	border-radius: var(--border-radius);
	opacity: 1;
}

@media (max-width: 767px) {
	.card-main.socialv-search-main {
		margin-bottom: 1em;
	}

	.socialv-member-info {
		flex-direction: column;
	}

	.socialv-member-info .socialv-member-right {
		text-align: left;
	}

	.socialv-member-right {
		margin-top: 1em;
	}

	.socialv-member-info a.friendship-button.add,
	.socialv-member-info a.friendship-button.remove {
		margin-bottom: 0;
	}
}

@media only screen and (max-width: 480px) {
	#buddypress div.dir-search input[type=text] {
		margin-bottom: 0;
		width: 100%;
	}
}

@media (max-width: 479px) {
	.socialv-member-info {
		padding: 1em;
	}
}

/* select2  start*/
.select-two-container .select-two-main {
	width: 10em;
}



.socialv-data-filter-by .select2-container--open .select2-dropdown--above,
.socialv-data-filter-by .select2-container--open .select2-dropdown--below,
.socialv-data-filter-by .select2-container--open .select2-dropdown--below,
.socialv-data-filter-by .select2-container--open .select2-dropdown--above {
	border-color: var(--border-color-light);
	box-shadow: var(--global-box-shadow);
}

.select2-results ul.select2-results__options li.select2-results__option {
	width: 100%;
}

.select-two-container,
.socialv-data-filter-by .item-list-tabs>ul>li.select-two-container {
	justify-content: end;
	align-items: center;
}

.socialv-data-filter-by ul li#members-order-select {
	float: inherit;
	text-align: right;
}

.socialv-data-filter-by ul li.filter span.select2 .selection,
.socialv-data-filter-by ul li.filter span.select2-container .select2-selection--single,
.socialv-data-filter-by ul li.filter span.dropdown-wrapper {
	padding: 0;
}

.socialv-data-filter-by ul li.filter span.select2 {
	padding: 0 0 .25em;
}

.socialv-data-filter-by ul li.filter {
	float: inherit;
	text-align: right;
}

.socialv-data-filter-by ul li.filter span.select2-container .select2-selection--single .select2-selection__rendered {
	padding: 0 1.875em 0 1em;
}

.socialv-data-filter-by ul li.filter span.select2-container {
	text-align: left;
}

.socialv-data-filter-by ul li.filter span.select2.select2-container {
	margin-top: 0;
	max-width: 11.25em;
	display: inline-block;
	text-align: left;
}

.socialv-data-filter-by .select2-container .select2-selection--single {
	height: 2.813em;
	border-color: var(--border-color-light);
	background: transparent;
}

.socialv-data-filter-by .select2-container {
	height: 2.813em;
}

.socialv-data-filter-by .select2-container--default .select2-selection--single .select2-selection__rendered {
	line-height: 2.813em;
	color: var(--global-font-color);
}

.socialv-data-filter-by .select2-container--default .select2-selection--single .select2-selection__arrow {
	height: 2.813em;
	right: .25em;
	width: 1.25em;
}

.socialv-data-filter-by .select2-container--default .select2-search--dropdown .select2-search__field {
	height: 2.188em;
	border-color: var(--border-color-light);
}

.socialv-data-filter-by .select2-results ul li {
	font-size: var(--font-size-normal);
}

.socialv-data-filter-by #subnav {
	margin: 0;
}

.socialv-data-filter-by ul li.last label {
	font-weight: var(--font-weight-medium);
	color: var(--global-font-title);
	font-family: var(--highlight-font-family);
}

/* select2  end*/
body #buddypress div .Members-directory {
	margin-bottom: 2em;
	border-radius: 0;
	border-bottom: 1px solid var(--border-color-light);
}

/* form start */

#bbpress-forums #bbp-search-form #bbp_search {
	width: 100%;
}

#bbpress-forums.bbpress-wrapper {
	line-height: var(--font-line-height-body);
	overflow: visible;
}

#bbpress-forums.bbpress-wrapper>h2 {
	margin-bottom: .5em;
}

div.bbp-template-notice.info {
	background-color: var(--color-theme-info-light);
	border-color: var(--color-theme-info);
	border-radius: 0 var(--border-radius) var(--border-radius) 0;
	padding: 1em;
	border: none;
	border-left: .188em solid var(--color-theme-info);
}

div.bbp-template-notice.info ul {
	list-style: none;
	margin: 0;
	padding: 0;
}

#bbpress-forums div.bbp-topic-tags {
	float: left;
	margin-bottom: 1.5em;
}

div.bbp-template-notice li,
div.bbp-template-notice p {
	padding: .38em .25em;
	color: var(--global-font-color);
}

div.bbp-template-notice a {
	color: var(--global-font-title);
}

div.bbp-template-notice a:hover {
	color: var(--color-theme-primary);
}

div.bbp-template-notice li,
div.bbp-template-notice p {
	font-size: var(--font-size-normal);
	color: var(--color-theme-info);
}


.width-two-column {
	width: 50%;
	display: inline-block;
	box-sizing: border-box;
	float: left;
}

.sv_topic-space {
	margin-bottom: 2em;
	clear: both;
}

.width-two-column.one {
	padding-right: 10px;
}

.width-two-column.two {
	padding-left: 10px;
}

.bbp-the-content-wrapper .wp-editor-area {
	border: .063em solid transparent;
}

.bbp-the-content-wrapper .wp-editor-area:focus {
	border-color: var(--color-theme-primary);
	color: var(--global-font-color);
}

#bbpress-forums div.bbp-the-content-wrapper {
	margin-bottom: 2em;
}

.buddypress .has-text-field #wp-link .query-results {
	top: 240px;
}

#wp-link label input[type=text],
#wp-link label input[type=search] {
	height: 2em;
	line-height: 2em;
}

.buddypress #wp-link-wrap,
.buddypress #link-modal-title,
.buddypress #wp-link .submitbox {
	background: var(--color-theme-white-box);
	border-color: var(--border-color-light);
}

.buddypress #wp-link-cancel button {
	background: var(--color-theme-danger);
	color: var(--color-theme-white);
	border-color: var(--color-theme-danger);
}

.buddypress #wp-link-update .button {
	background: var(--color-theme-info);
	color: var(--color-theme-white);
	border-color: var(--color-theme-info);
}

.buddypress #wp-link .query-notice .query-notice-default,
.buddypress #wp-link .query-notice .query-notice-hint {
	border-color: var(--color-theme-primary);
	color: var(--global-font-color);
}

.buddypress #wp-link .query-notice,
.buddypress #wp-link .query-results {
	background-color: var(--global-body-bgcolor) !important;
	border-color: var(--border-color-light) !important;
}

.buddypress #wp-link li {
	color: var(--global-font-color);
	border-color: var(--border-color-light);
}

.buddypress #wp-link li:hover {
	background: var(--color-theme-white-box);
}

.mce-toolbar .mce-btn-group>div {
	display: flex;
	align-items: center;
	flex-wrap: wrap;
}

/* form start End*/


.socialv-tab-lists ul.socialv-tab-container>li>a {
	color: var(--global-font-color);
}

.socialv-e-member-location,
.socialv-e-last-activity {
	font-size: var(--font-size-normal);
}

.socialv-member-info-top i {
	display: inline-block;
	vertical-align: middle;
	font-size: 1.1em;
}

/* Member page End */

/* activity page start */

.socialv-upload-file .upload-icon i {
	color: var(--dark-icon-color);
	font-size: 1.5em;
	display: inline-block;
	vertical-align: middle;
	transition: all .5s ease-in-out;
	-moz-transition: all .5s ease-in-out;
	-ms-transition: all .5s ease-in-out;
	-o-transition: all .5s ease-in-out;
	-webkit-transition: all .5s ease-in-out;
}

.socialv-upload-file a label.socialv-upload-btn-labels span {
	color: var(--global-font-color);
	font-family: var(--highlight-font-family);
	font-weight: var(--font-weight-medium);
	transition: all .5s ease-in-out;
	-moz-transition: all .5s ease-in-out;
	-ms-transition: all .5s ease-in-out;
	-o-transition: all .5s ease-in-out;
	-webkit-transition: all .5s ease-in-out;
	line-height: normal;
}

#buddypress #whats-new:focus {
	border-color: var(--color-theme-primary);
}

.socialv-upload-file a:hover label.socialv-upload-btn-labels span,
.socialv-upload-file a:hover .upload-icon i {
	color: var(--color-theme-primary);
}

.socialv-upload-file a {
	cursor: pointer;
	margin: .625em 1em .625em 0;
	color: var(--global-font-color);
}

.socialv-upload-file a.bpolls-icon {
	font-weight: var(--font-weight-medium);
}

.bp-giphy-html-container.socialv-upload-file a label {
	display: flex;
	align-items: center;
}

.mpp-upload-buttons {
	padding-right: 0;
}

.socialv-upload-file a:last-child {
	margin: 0;
}

#buddypress div.activity-comments ul li {
	padding: 1.5em 0 0;
}

.socialv-upload-file a:hover {
	color: var(--color-theme-primary);
}

.socialv-upload-file a label {
	cursor: pointer;
}

.socialv-upload-file a.disabled .socialv-upload-btn-labels{
	opacity: .5;
}

.main-upload-detail {
	display: flex;
	gap: 1em;
}

.main-upload-detail .socialv-upload-file {
	display: flex;
	flex-wrap: wrap;
	align-items: center;
	gap: 1em;
}

.mpp-upload-buttons.socialv-upload-file {
	margin-top: 0;
}

.socialv-upload-file .bp-giphy-media-search a {
	margin: 0;
}

.socialv-upload-file .upload-icon {
	margin-right: .275em;
}

#buddypress form#whats-new-form #whats-new-content {
	margin-left: 0;
	clear: both;
	padding: 0;
}

.socialv-whats-new-options .select2.select2-container {
	max-width: 9.25em;
}

.whats-new-post-in-box .select2-container .select2-selection--single {
	height: 2em;
	border-color: var(--border-color-light);
	background: transparent;
}

.whats-new-post-in-box .select2-container--default .select2-selection--single .select2-selection__rendered {
	line-height: 2em;
	color: var(--global-font-color);
	padding: 0 3.875em 0 1em;
}

.whats-new-post-in-box .select2-container--default .select2-selection--single .select2-selection__arrow {
	height: 2em;
	right: .625em;
	width: 1.25em;
	top: -.1875em;
}

[dir="rtl"] .whats-new-post-in-box .select2-container--default .select2-selection--single .select2-selection__arrow {
	right: inherit;
	left: .625em;
}

[dir="rtl"] .whats-new-post-in-box .select2-container--default .select2-selection--single .select2-selection__rendered {
	padding: 0 1em 0 2.875em;
}

.whats-new-post-in-box .select2-container {
	height: 1.5em;
}

.whats-new-post-in-box .select2-container--open .select2-dropdown--below,
.whats-new-post-in-box .select2-container--open .select2-dropdown--above {
	border-color: var(--border-color-light);
	box-shadow: var(--global-box-shadow);
}

.whats-new-post-in-box .select2-container--default .select2-search--dropdown .select2-search__field {
	height: 1.8em;
	border-color: var(--border-color-light);
}

.whats-new-post-in-box .select2-container--default .select2-selection--single .select2-selection__rendered {
	font-size: var(--font-size-small);
	display: block;
	font-weight: var(--font-weight-medium);
	color: var(--global-font-title);
}

.new-post-in-box {
	position: relative;
	display: inline-block;
	width: 9.25em;
	max-width: 100%;
}

.new-post-in-box .select2-results ul li {
	font-size: var(--font-size-small);
}

.mpp-upload-buttons.socialv-upload-file,
#buddypress form#whats-new-form #whats-new-avatar {
	float: inherit;
}

#buddypress form#whats-new-form #whats-new-avatar {
	display: inline-block;
}

.whats-new-content {
	clear: both;
}

.whats-new-content .whats-new-textarea {
	clear: both;
	display: block;
	margin-top: 2em;
}

.socialv-whats-new-options {
	display: flex;
	justify-content: space-between;
	flex-wrap: wrap;
}

.socialv-whats-new-options .whats-new-post-in-box {
	display: flex;
	flex: 1;
	align-items: center;
	padding-top: 1.5em;
	position: relative;
}

.socialv-whats-new-options .whats-new-submit {
	float: inherit;
	margin-top: 0;
	padding-top: 1.5em;
}

.socialv-whats-new-options span.post-lable {
	position: relative;
	padding-right: 1em;
	font-size: var(--font-size-small);
	font-weight: var(--font-weight-medium);
}

.socialv-whats-new-options span.post-lable:after {
	content: "";
	width: .125em;
	height: 1.25em;
	background: var(--border-color-light);
	position: absolute;
	top: .375em;
	right: 0;
}

.socialv-whats-new-options .select2-container--default .select2-selection--single {
	border: none;
}

#buddypress form#whats-new-form #whats-new-submit {
	margin-top: 0;
}

#buddypress ul.socialv-list-post {
	border: none;
	padding-left: 0;
	padding-right: 0;
}

#buddypress #activity-stream {
	margin-top: 0;
}

#buddypress ul.socialv-list-post>li {
	padding: 2em;
	margin-bottom: 2em;
	border-radius: var(--border-radius-box);
	background: var(--color-theme-white-box);
	border-bottom: none;
	overflow: visible;
	box-shadow: var(--global-box-shadow);
	list-style: none;
}

#buddypress ul.socialv-list-post>li.load-more {
	box-shadow: none;
	margin: 0;
	padding: 0;
	text-align: center;
}

.socialv-meta-details {
	display: -webkit-box;
	display: flex;
	-webkit-box-align: center;
	align-items: center;
	-webkit-box-pack: center;
	gap: .3em;
	margin-top: 1.125em;
	flex-wrap: wrap;
	padding-bottom: 1em;
	border-bottom: 1px solid var(--border-color-light);
}

.liked-member {
	display: flex;
	align-items: center;
	justify-content: center;
	flex-wrap: wrap;
}

.socialv-blog-main-list .grid-item .video-wrap video,
.grid-item audio,
.grid-item .video-wrap video {
	width: 100%;
	display: inline-block;
	vertical-align: baseline;
}

.socialv-blog-main-list .grid-item .video-wrap video,
.grid-item .video-wrap video {
	border-radius: var(--border-radius-box);
}

.video-wrap .mejs-mediaelement {
	position: relative;
	padding-top: 60%;
}

.video-wrap .mejs-mediaelement video {
	position: absolute;
	top: 0;
	left: 0;
	object-fit: contain;
	height: 100% !important;
	width: 100% !important;
}

#buddypress ul.socialv-list-post li .liked-member ul li {
	padding: 0;
	margin-bottom: 0;
	display: inline-block;
	overflow: visible;
}

#buddypress ul.socialv-list-post li .liked-member img.avatar {
	height: 1.5em;
	width: 1.5em;
	min-width: 1.5em;
	text-align: center;
	line-height: 1.5em;
	border-radius: 100%;
	border: 2px solid var(--color-theme-white);
	margin: 0;
}

.liked-member ul li a {
	margin-left: -.75em;
	position: relative;
	display: inline-block;
	vertical-align: middle;
	z-index: 0;
	-webkit-transition: all .4s ease-in-out;
	transition: all .4s ease-in-out;
}

.liked-member .total-member,
.socialv-meta-details .comment-info {
	font-size: var(--font-size-normal);
	line-height: normal;
	font-family: var(--highlight-font-family);
}

.socialv-meta-details .comment-info {
	margin-left: .375em;
}

.liked-member .total-member a {
	color: var(--global-font-title);
	text-transform: capitalize;
}

.liked-member .member-thumb-group {
	padding: 0 .3em 0 0;
	line-height: normal;
}

.activity-comments-test-popup .liked-member .member-thumb-group {
    margin-bottom: 0;
}

.card-main.socialv-list-activity {
	margin: 2em 0;
}

#buddypress .activity-list.socialv-list-post li.mini .activity-avatar img.FB_profile_pic,
#buddypress .activity-list.socialv-list-post li.mini .activity-avatar.sv img.avatar,
#buddypress ul.socialv-list-post li img.avatar {

	margin-left: 0;
	margin-right: 0;
}

#buddypress ul.socialv-list-post li .activity-avatar-sv img.avatar,
#buddypress ul.socialv-list-post li .comment-container-main img.avatar {
	border-radius: var(--border-radius);
}

#buddypress ul.socialv-list-post li .activity-avatar-sv>a,
#buddypress ul.socialv-list-post li .comment-container-main .acomment-avatar-sv>a,
.activity-comments-test-popup .activity-avatar-sv>a {
	padding: .125em;
	display: inline-block;
}

#buddypress div.activity-comments div.acomment-meta {
	font-size: 100%;
}

#buddypress .activity-header {
	margin-right: 0;
}

#buddypress .acomment-meta a,
#buddypress .activity-header a,
#buddypress .comment-meta a,
.activity-comments-test-popup .activity-header p a{
	color: var(--global-font-title);
	font-weight: var(--font-weight-medium);
	font-family: var(--highlight-font-family);
}

.activity-subtext {
	font-weight: var(--font-size-normal);
	font-size: var(--font-size-normal);
	font-family: var(--highlight-font-family);
}

#buddypress .acomment-meta a:hover,
#buddypress .activity-header a:hover,
#buddypress .comment-meta a:hover {
	color: var(--color-theme-primary);
}

.activity-content .video-wrap .post-wrap-inner {
	text-align: center;
	background-repeat: no-repeat;
	background-size: cover;
	border-radius: var(--border-radius);
	overflow: hidden;
}

.activity-content .video-wrap .post-wrap-inner>a {
	backdrop-filter: blur(10px);
	-webkit-backdrop-filter: blur(10px);
	-moz-backdrop-filter: blur(10px);
	-o-backdrop-filter: blur(10px);
	-ms-backdrop-filter: blur(10px);
	display: block;
}

.activity-content .video-wrap a img {
	border-radius: var(--border-radius-box);
	max-height: initial;
	min-height: auto;
	object-fit: contain;
	width: 100%;
	aspect-ratio: 5/3;
}

.activity-content .video-wrap .mpp-activity-doc-content img {
	width: auto;
}

.activity-content .swiper-slide .video-wrap a img {
	max-height: 29.5em;
	min-height: 29.5em;
	object-fit: contain;
}

.activity-content .video-wrap .single-post-img a img,
.post-row.column-1 .post-column .post-wrap-inner.single-post-img img {
	width: 100%;
	aspect-ratio: inherit;
}

.swiper .swiper-pagination {
	position: relative;
	top: 0;
	margin-top: .15em;
	display: inline-block;
}

.swiper .swiper-pagination-bullet-active {
	background-color: var(--color-theme-primary);
}

.swiper .swiper-pagination-bullets-dynamic {
	font-size: inherit;
}

.swiper .swiper-pagination-bullet {
	border-radius: .125em;
}

.swiper .swiper-pagination-bullets-dynamic .swiper-pagination-bullet-active-next,
.swiper .swiper-pagination-bullets-dynamic .swiper-pagination-bullet-active-prev,
.swiper-pagination-bullets-dynamic .swiper-pagination-bullet-active-next-next,
.swiper-pagination-bullets-dynamic .swiper-pagination-bullet-active-prev-prev {
	transform: scale(1);
}

.btn-close:focus {
	box-shadow: none;
}

#buddypress ul.socialv-list-post li .has-cover-image img.avatar.profile-photo,
#buddypress ul.socialv-list-post li .socialv-profile-activity img.avatar,
#buddypress ul.socialv-list-post li .socialv-group-activity .bp-group-short-description img,
.header-avatar img {
	display: inline-block;
	background-color: var(--border-color-light);
}

.header-avatar {
	display: inline-block;
	background: var(--color-theme-white-box);
	padding: .25em;
	border-radius: var(--border-radius);
}


#buddypress ul.socialv-list-post li .socialv-group-activity .bp-group-short-description .bp-group-avatar-content>a,
#buddypress ul.socialv-list-post li .socialv-profile-activity .bp-group-avatar-content>a,
#buddypress ul.socialv-list-post li .has-cover-image>a {
	margin-left: 2em;
	display: inline-block;
	background: var(--color-theme-white);
	padding: .25em;
	border-radius: var(--border-radius);
}

.bp-group-avatar-content,
.bp-member-avatar-content,
.bp-group-avatar-content.has-cover-image,
.bp-member-avatar-content.has-cover-image {
	width: auto;
	height: auto;
	font-size: inherit;
}

#buddypress .activity-list.socialv-list-post .activity-content {
	margin-left: 0;
}

ul.activity-list>li>ul.activity-comments {
	padding-left: 0;
}

#buddypress .activity-list.socialv-list-post .activity-content .activity-inner,
.activity-comments-test-popup .activity-content .activity-inner {
	clear: both;
	margin: 1.5em 0 0;
	border: none;
	padding: 0;
	background: transparent;
	position: relative;
}

#buddypress .activity-list.socialv-list-post .activity-content .activity-inner .reshare-activity-overlay {
	position: absolute;
	top: 0;
	bottom: 0;
	left: 0;
	right: 0;
	z-index: 1;
}


.new_blog_post .activity-content .activity-inner img {
	border-radius: var(--border-radius);
	max-height: none;
	min-height: auto;
	-o-object-fit: contain;
	object-fit: contain;
	width: 100%;
	aspect-ratio: 3/2;
}

#buddypress .activity-list.socialv-list-post .activity-content .activity-inner p {
	padding: 0;
}

.socialv-activity-header,
.activity-header-wrapper  {
	display: flex;
	align-items: center;
	gap: 1em;
}

.activity-header-wrapper {
	display: flex;
	justify-content: space-between;
	align-items: baseline;
	width: 100%;
	gap: 4em;
}

.socialv-activity-header-left,
.socialv-activity-header-right {
	display: flex;
	align-items: center;
	gap: 1.125em;
}

.socialv-activity-header-right {
	white-space: nowrap;
}
.socialv-activity-header-right .pinicon{
	color: var(--color-theme-primary);
}
.socialv-activity-header-right .dropdown a.btn-dropdown {
	display: inline-block;
	vertical-align: middle;
	color: var(--global-font-color);
}

.bp-verified-badge {
	width: 1em;
	height: 1em;
	margin-left: .4em;
}

.widget_iqonic_recently_active_members .bp-verified-badge {
	display: none;
}

.bp-verified-badge,
.bp-verified-member:not(.bp-verified-member-badge-loaded) .member-name-item>a:after,
.bp-verified-member:not(.bp-verified-member-badge-loaded) .item-title>a:after,
.bp-verified-member:not(.bp-verified-member-badge-loaded)>.author>a:after,
.bp-verified-member:not(.bp-verified-member-badge-loaded) .member-name>a:after {
	background-size: .5em .5em;
}

.activity .bp-verified-badge,
.activity .bp-verified-member:not(.bp-verified-member-badge-loaded) .member-name-item>a:after,
.activity .bp-verified-member:not(.bp-verified-member-badge-loaded) .item-title>a:after,
.activity .bp-verified-member:not(.bp-verified-member-badge-loaded)>.author>a:after,
.activity .bp-verified-member:not(.bp-verified-member-badge-loaded) .member-name>a:after,
.activity .bp-unverified-badge,
.activity .bp-unverified-member:not(.bp-unverified-member-badge-loaded) .member-name-item>a:after,
.activity .bp-unverified-member:not(.bp-unverified-member-badge-loaded) .item-title>a:after,
.activity .bp-unverified-member:not(.bp-unverified-member-badge-loaded)>.author>a:after,
.activity .bp-unverified-member:not(.bp-unverified-member-badge-loaded) .member-name>a:after {
	margin-right: .3em;
}

#buddypress ul.activity-list ul.dropdown-menu li {
	padding: 0;
}

.activity-header .time-since {
	font-size: var(--font-size-xs);
	font-weight: var(--font-weight-medium);
}

.socialv-activity-header-left .activity-header a {
	font-size: var(--font-size-h6);
	font-weight: var(--font-weight-medium);
	font-family: var(--highlight-font-family);
	color: var(--global-font-title);
}

.bp-group-short-description-title a,
.bp-member-short-description-title a {
	font-weight: var(--font-weight-medium);
}

#buddypress .activity-list.socialv-list-post .activity-content .activity-inner .bp-group-preview-cover,
#buddypress .activity-list.socialv-list-post .activity-content .activity-inner .bp-member-preview-cover {
	min-height: 13.25em;
	overflow: hidden;
	border-radius: var(--border-radius-box) var(--border-radius-box) 0 0;

}


.activity-list.socialv-list-post .activity-content .activity-inner .bp-group-preview-cover img,
.activity-list.socialv-list-post .activity-content .activity-inner .bp-member-preview-cover img {
	min-height: 13.25em;
}

.activity-list.socialv-list-post .bp-group-preview-cover img,
.activity-list.socialv-list-post .bp-member-preview-cover img {
	height: 13.25em;
	object-fit: cover;
}

.activity-list.socialv-list-post .socialv-profile-activity .bp-member-short-description,
.activity-list.socialv-list-post .socialv-group-activity .bp-group-short-description {
	padding-bottom: 1.75em;
	display: flex;
	gap: 1em;
}

.activity-list.socialv-list-post .socialv-group-activity .bp-group-short-description .bp-group-short-description-title {
	flex: 1;
	text-align: left;
}

.socialv-group-activity .activity-group-meta,
.socialv-profile-detail .bp-member-nickname a {
	font-size: var(--font-size-normal);
	color: var(--global-font-color);
	font-family: var(--global-font-family);
	margin-top: .5em;
	font-weight: var(--font-weight-medium);
}

.socialv-group-activity .activity-group-meta i {
	font-size: 1.375em;
}

.socialv-group-activity .activity-group-meta .socialv-group-type span,
.socialv-group-activity .activity-group-meta .socialv-group-members span {
	display: flex;
	align-items: center;
	gap: .3em;
}

.socialv-group-activity .socialv-group-type,
.socialv-group-activity .socialv-group-members {
	margin-right: 1.3em;
	display: inline-block;
	vertical-align: middle;
}

.activity-list.socialv-list-post .socialv-group-activity .bp-group-short-description .bp-group-short-description-title a {
	font-size: var(--font-size-h5);
	line-height: var(--font-line-height-h5);
	letter-spacing: var(--font-letter-spacing-h5);
	font-weight: var(--font-weight-h5);
	font-family: var(--highlight-font-family);
	color: var(--global-font-title);
	display: block;
}

.activity-list.socialv-list-post .socialv-profile-activity,
.activity-list.socialv-list-post .socialv-group-activity {
	background: var(--global-body-bgcolor);
	border-radius: var(--border-radius-box);
}

#buddypress .activity-list .activity-content.activity-sharing .shared-activity .socialv-group-activity,
#buddypress .activity-list .activity-content.activity-sharing .shared-activity .socialv-group-activity {
	background: var(--color-theme-white-box);
}

.bp-group-avatar-content,
.bp-member-avatar-content {
	overflow: visible;
	margin-top: -3em;
}

.activity-list.socialv-list-post .bp-group-avatar-content.has-cover-image,
.activity-list.socialv-list-post .bp-member-avatar-content.has-cover-image {
	margin: -3em 0 0;
	display: inline-block;
}

.bp-group-short-description,
.bp-member-short-description {
	margin-bottom: 0;
}

.activity-list.socialv-list-post .bp-group-short-description,
.activity-list.socialv-list-post .bp-member-short-description {
	text-align: left;
}

.activity-list.socialv-list-post .socialv-profile-detail {
	flex: 1;
	margin-top: 1.6em;
	margin-bottom: 0;
}

/* buddypress share button css start */

#buddypress .socialv-activity-parent .socialv-activity_comment .bp-share-btn .bp-share-button {
	background: unset;
	border: none;
	color: var(--global-font-color);
	padding: 0 0 0 22px;
	text-transform: inherit;
}

#buddypress .socialv-activity-parent .socialv-activity_comment .bp-share-btn .bp-share-button::before {
	content: "\e047";
	font-family: "Iconly" !important;
	font-weight: 400;
	color: var(--global-font-color);
	font-size: 1.2em;
	margin-right: 0.2em;
	position: absolute;
	left: 0;
	top: 50%;
    transform: translateY(-60%);
	line-height: normal;
	vertical-align: text-bottom;
}

#buddypress .socialv-activity-parent .socialv_activity_inner .service-buttons .share_activity-share,
.service-buttons a.bp-share{
	background: var(--color-theme-primary-light);
	padding: 0px;
	margin: 2.5px;
	border-radius: var(--border-radius);
	display: inline-block;
	height: 35px;
    width: 35px;
	text-align: center;
}

#buddypress .socialv-activity-parent .socialv_activity_inner .service-buttons .share_activity-share {
	float: none;
	line-height: inherit;
}

#buddypress .socialv-activity-parent .socialv_activity_inner .service-buttons {
	position: absolute;
	right: 0px;
	background: var(--color-theme-white-box);
	border: 0.063em solid var(--border-color-light);
	border-radius: var(--border-radius);
	z-index: 11;
	text-align: right;
}


#buddypress .socialv-activity-parent .socialv_activity_inner .service-buttons .share_activity-share i{
	width: auto;
    height: auto;
    text-align: center;
    font-size: 1.1rem;
    vertical-align: middle;
}
#buddypress .socialv-activity-parent .socialv_activity_inner .service-buttons .bp-share .dashicons {
	line-height: inherit;
    height: auto;
    width: auto;
	font-size: 1.3rem;
}

.socialv-activity-parent .socialv-activity_comment .bp-share-btn {
	position: relative;
	display: inline-block;
	cursor: pointer;
	float: right;
}

/* buddypress share button css end */

.socialv-activity_comment .socialv-share-post .share-btn i {
	background: transparent;
	color: var(--global-font-color);
	text-transform: capitalize;
	font-size: 1.1em;
	display: inline-block;
	line-height: normal;
	vertical-align: text-bottom;
	margin-right: .2em;
}

.socialv-activity_comment a {
	font-size: 1em;
	color: var(--global-font-color);
}

.socialv-activity_comment a.liked {
	color: var(--color-theme-danger);
}

.socialv-activity_comment a.liked>i.iconly-Heart {
	font-weight: 700;
	color: var(--color-theme-danger);
}

.socialv-activity_comment a.liked>i .socialv-activity_comment a .label-like,
.socialv-activity_comment a .label-comment,
.socialv-activity_comment a .label-share,
.socialv-share-post .share-btn .label-share {
	font-size: var(--font-size-normal);
}

.socialv-share-post .share-btn .label-share {
	margin-left: .2em;
	vertical-align: text-bottom;
}

.socialv-activity_comment a i {
	font-size: 1.3em;
}

.socialv-activity_comment .socialv-share-post a {
	font-size: var(--font-size-normal);
}

.socialv-activity_comment .socialv-share-post a i {
	font-size: 1em;
	margin-right: .5em;
}
.socialv-activity_comment .socialv-share-post a.yahoo-share i{
	vertical-align: text-top;
}
.socialv-activity_comment li:hover a {
	color: var(--color-theme-primary);
}

.socialv-activity_comment a.socialv-user-activity-btn.added,
.socialv-activity_comment a.socialv-user-activity-btn.added:hover {
	color: var(--color-theme-danger);
}

.comment-activity>.socialv-activity_comment>a>i,
.comment-activity>.socialv-activity_comment>a>span {
	display: inline-block;
	vertical-align: text-bottom;
}

.comment-activity>.socialv-activity_comment>a>span {
	font-size: var(--font-size-normal);
	margin-left: .3em;
}

.socialv-activity_comment a i.icon-loader-circle {
	background: var(--gradient-color);
	-webkit-background-clip: text;
	-moz-background-clip: text;
	background-clip: text;
	-webkit-text-fill-color: transparent;

}

.socialv-activity_comment .socialv-share-post .social-share-group i {
	font-size: 1.3em;
	display: inline-block;
	line-height: normal;
	vertical-align: middle;
}

.socialv-activity_comment .socialv-share-post .social-share-group .facebook-share i {
	color: #1877f2;
}

.socialv-activity_comment .socialv-share-post .social-share-group .twitter-share i {
	color: #1da1f2;
}

.socialv-activity_comment .socialv-share-post .social-share-group .linkedin-share i {
	color: #0077b5;
}

.socialv-activity_comment .socialv-share-post .social-share-group .pinterest-share i {
	color: #c8232c;
}

.socialv-activity_comment .socialv-share-post .social-share-group .youtube-share i {
	color: #f9101e;
}

.socialv-activity_comment .socialv-share-post .social-share-group .instagram-share i {
	background: var(--instra-color);
	-webkit-background-clip: text;
	background-clip: text;
	-webkit-text-fill-color: transparent;
}

/*rotation*/
@-webkit-keyframes rotation {
	from {
		-webkit-transform: rotate(0deg);
	}

	to {
		-webkit-transform: rotate(360deg);
	}
}

@-moz-keyframes rotation {
	from {
		-webkit-transform: rotate(0deg);
	}

	to {
		-webkit-transform: rotate(360deg);
	}
}

@keyframes rotation {
	from {
		-webkit-transform: rotate(0deg);
	}

	to {
		-webkit-transform: rotate(360deg);
	}
}

.socialv-activity_comment i.icon-loader-circle {
	-webkit-animation: rotation 1s linear infinite;
	-moz-animation: rotation 1s linear infinite;
	animation: rotation 1s linear infinite;
}


.socialv-share-post .activity-social-share {
	display: inline-block;
	padding: .7em 1em;
	background: var(--global-body-bgcolor);
	border-radius: 2.5em;
	margin-left: .875em;
}

.comment-activity>.socialv-activity_comment>a {
	margin-right: .625em;
}

#buddypress #reply-title small a,
#buddypress a.bp-primary-action {
	font-size: inherit;
}

#buddypress #reply-title small a span,
#buddypress a.bp-primary-action span {
	background: transparent;
	color: var(--global-font-color);
	font-size: var(--font-size-normal);
}

#buddypress #reply-title small a:hover span,
#buddypress a.bp-primary-action:hover span {
	background: transparent;
	color: var(--global-font-color);
}

.socialv-activity_comment .socialv-share-post .activity-social-share span a {
	font-size: .7em;
	margin-right: 1.2em;
	vertical-align: middle;
	transition: all .3s ease-in-out;
	-moz-transition: all .3s ease-in-out;
	-ms-transition: all .3s ease-in-out;
	-o-transition: all .3s ease-in-out;
	-webkit-transition: all .3s ease-in-out;
	display: inline-block;
}

.socialv-activity_comment .socialv-share-post .activity-social-share span a:hover {
	-webkit-transform: translate(0, -4px);
	transform: translate(0, -4px);
}

.socialv-activity_comment .socialv-share-post .activity-social-share span:last-child a {
	margin-right: 0;
}

.socialv-comment-main {
	padding-top: 1em;
}

.socialv-gallery-status.zoom-gallery {
	margin-bottom: .7em;
}

.socialv-share-post {
	position: relative;
	display: inline-block;
	cursor: pointer;
	float: right;
}

#buddypress ul.activity-list .socialv-share-post ul li {
	padding: .5em 0;
}

#buddypress ul.activity-list .socialv-share-post li:first-child {
	padding-top: 0;
}

.sharing-options {
	z-index: 9;
	background: var(--color-theme-white-box);
	border: .063em solid var(--border-color-light);
	list-style: none;
	border-radius: var(--border-radius);
	padding: 1em;
	transform: translateY(10px) scale(.7) !important;
	transform-origin: top right;
	transition: all 500ms cubic-bezier(.19, 1, .22, 1);
	transition-timing-function: cubic-bezier(.19, 1, .22, 1);
	position: absolute;
	top: 100%;
	right: 0;
	opacity: 0;
	visibility: hidden;
	width: 15em;
}

.socialv-share-post .sharing-options.open {
	opacity: 1;
	visibility: visible;
	transform: translateY(0) scale(1) !important;
}

@-webkit-keyframes fadeInLike {
	0% {
		opacity: 0;
	}

	100% {
		opacity: 1;
	}
}

@keyframes fadeInLike {
	0% {
		opacity: 0;
	}

	100% {
		opacity: 1;
	}
}

.socialv-share-post:hover .activity-social-share {
	opacity: 1;
	visibility: visible;
}


#buddypress .activity-list li.mini {
	font-size: inherit;
}

.socialv-comment-main .comment-info {
	font-size: var(--font-size-normal);
	font-weight: var(--font-weight-medium);
}

#buddypress .socialv-activity_comment a:hover {
	background: transparent;
}

/* repost */

#buddypress .activity-list .activity-content.activity-sharing .shared-activity {
	padding: 1em;
	border: .063em solid var(--border-color-light);
	border-radius: var(--border-radius);
	background: var(--global-body-bgcolor);
}

@media(max-width:767px) {
	.activity-content .swiper-slide .video-wrap a img {
		max-height: 19.5em;
		min-height: 19.5em;
	}

	#buddypress ul.activity-list li ul.activity-comments li {
		padding: 1em 0 0;
	}

	#buddypress .activity-list.socialv-list-post .activity-content .activity-inner {
		margin: 1em 0 0;
	}

	.activity-header-wrapper {
		gap: 1em;
	}

	.card-inner.post-inner-block {
		padding: 1em;
	}

	.socialv-activity-header {
		align-items: start;
	}

	#buddypress .activity-list .activity-avatar {
		margin-top: .7em;
	}

	.list-grid-btn-switcher {
		padding-left: 0;
	}

	#buddypress ul.socialv-list-post>li {
		padding: 1em;
		margin-bottom: 1em;
	}

	#buddypress ul.socialv-list-post li .socialv-group-activity .bp-group-short-description .bp-group-avatar-content>a,
	#buddypress ul.socialv-list-post li .socialv-profile-activity .bp-group-avatar-content>a,
	#buddypress ul.socialv-list-post li .has-cover-image>a {
		margin-left: 1em;
	}

	.bp-group-avatar-content,
	.bp-member-avatar-content,
	.activity-list.socialv-list-post .bp-group-avatar-content.has-cover-image,
	.activity-list.socialv-list-post .bp-member-avatar-content.has-cover-image {
		margin-top: -1em;
	}

	.activity-list.socialv-list-post .socialv-profile-detail {
		margin-top: 1em;
	}
}

@media(max-width:479px) {

	.socialv-share-post .activity-social-share {
		top: -3em;
		left: auto;
		right: 0;
	}

	.bp-group-avatar-content,
	.bp-member-avatar-content,
	.activity-list.socialv-list-post .bp-group-avatar-content.has-cover-image,
	.activity-list.socialv-list-post .bp-member-avatar-content.has-cover-image {
		margin-top: -1em;
	}

	.activity-list.socialv-list-post .socialv-profile-detail {
		margin-top: 1em;
	}

	.activity-list.socialv-list-post .socialv-group-activity .bp-group-short-description,
	.activity-list.socialv-list-post .socialv-profile-activity .bp-member-short-description {
		padding-bottom: 1.75em;
	}
}

/* group page css start */
.socialv-group-filter {
	display: flex;
	align-items: center;
	justify-content: end;
}

.list-grid-btn-switcher {
	display: flex;
	align-items: center;
	padding-left: 1em;
}

.list-grid-btn-switcher li {
	font-size: 1.2em;
	background: var(--global-body-bgcolor);
	height: 1.822em;
	width: 1.822em;
	line-height: 1.822em;
	text-align: center;
	margin-left: 1em;
	border-radius: var(--border-radius);
}

.list-grid-btn-switcher li a {
	color: var(--global-font-title);
}

.list-grid-btn-switcher li.active {
	background: var(--color-theme-primary);
}

.list-grid-btn-switcher li.active a {
	color: var(--color-theme-white);
}

.show-all-comments {
	margin-top: 1.25em;
}

.socialv-groups-lists .group-has-avatar .socialv-group-info {
	background: var(--color-theme-white-box);
	box-shadow: var(--global-box-shadow);
	border-radius: var(--border-radius-box);
	margin-bottom: 2em;
	overflow: hidden;
	position: relative;
	border: .063em solid var(--border-color-light);
}

.socialv-groups-lists .group-has-avatar .socialv-group-info .cover-img img {
	height: 8.25em;
	width: 100%;
	object-fit: cover;
}

#buddypress .socialv-groups-lists .group-has-avatar .socialv-group-info .status {
	width: 2em;
	height: 2em;
	line-height: 2em;
	text-align: center;
	position: absolute;
	right: 1em;
	top: 1em;
	background: var(--color-theme-white-box);
	font-size: 1.2em;
	color: var(--global-font-title);
	border-radius: var(--border-radius-full);
	z-index: 1;
}

.list-img-group li {
	list-style: none;
	display: inline-block;
	vertical-align: middle;
	margin: 0 0 .3em !important;
}

.list-img-group li a img {
	background-color: var(--color-default-bg-avatar);
}

.list-img-group li a img,
.list-img-group li a i {
	height: 2.25em;
	width: 2.25em;
	min-width: 2.25em;
	text-align: center;
	line-height: 2em;
	border-radius: 100%;
	border: 2px solid var(--color-theme-white-box);
}

.list-img-group li a i {
	background: var(--color-theme-primary);
	color: var(--color-theme-white);
	font-size: .7em;
	height: 3.25em;
	width: 3.25em;
	min-width: 3.25em;
	line-height: 2.9;
}

.list-img-group li a {
	margin-left: -1.3em;
	position: relative;
	z-index: 0;
	-webkit-transition: all .4s ease-in-out;
	-o-transition: all .4s ease-in-out;
	transition: all .4s ease-in-out;
}

.list-img-group li:first-child a {
	margin-left: 0;
}

.list-img-group li a:hover {
	z-index: 9;
}

.socialv-groups-lists .socialv-group-info .text-center {
	padding: 2em 1.5em;
}

.socialv-groups-lists .socialv-group-info .group-icon {
	margin-top: -5em;
	margin-bottom: 1.75em;
}

.socialv-groups-lists .socialv-group-info .group-icon img {
	border-radius: var(--border-radius);
	border: .25em solid var(--global-body-bgcolor);
	background-color: var(--border-color-light);
}

.socialv-groups-lists .socialv-group-info .group-name {
	margin-bottom: .5em;
}

.socialv-groups-lists .list-img-group {
	padding: 1.5em 0 0;
	margin-bottom: 0;
	border-top: .063em solid var(--border-color-light);
}

.socialv-groups-lists .socialv-group-info .socialv-group-details ul {
	margin-bottom: 0;
	line-height: normal;
}

.socialv-groups-lists .socialv-group-info .socialv-group-details {
	padding-bottom: 1.5em;
}

.group-button.generic-button {
	display: flex;
	align-items: center;
	justify-content: center;
	margin: 1.5em -1em 0;
}

.group-admin-main-button .group-button.generic-button.manage-group-btn {
	display: flex;
}

.group-admin-main-button .group-button.generic-button {
	display: none;
}

#buddypress .socialv-groups-lists .socialv-group-info .group-button a {
	margin: 0 1em;
}

.socialv-groups-lists .socialv-group-info .socialv-group-details ul li {
	margin-right: .6em;
}

.socialv-groups-lists .socialv-group-info .socialv-group-details ul li a {
	color: var(--global-font-color);
	font-weight: var(--font-weight-medium);
}

.socialv-groups-lists .socialv-group-info .socialv-group-details ul li a:hover {
	color: var(--color-theme-primary);
}

.socialv-groups-lists .socialv-group-info .socialv-group-details ul li:last-child {
	margin-right: 0;
}

.socialv-groups-lists .socialv-group-details ul li a {
	display: flex;
	align-items: center;
}

.socialv-groups-lists .socialv-group-details ul li a .member-icon,
.socialv-groups-lists .socialv-group-details ul li a .post-icon {
	font-size: 1.2em;
}

.socialv-groups-lists .socialv-group-details ul li a .item-text,
.socialv-groups-lists .socialv-group-details ul li a .item-number {
	font-size: var(--font-size-normal);
}

.socialv-groups-lists .socialv-group-details ul li a .item-text,
.socialv-groups-lists .socialv-group-details ul li a .item-number {
	margin-left: .375em;
}

#buddypress div.activity-comments {
	margin: 0;
	padding-left: 0;
}

ul.activity-comments {
	list-style: none;
}

#buddypress div.activity-comments-list>ul.activity-comments,
.activity-comments-test-popup .activity-comments-list>ul.activity-comments {
	padding: 0;
}

#buddypress div.activity-comments ul li {
	border: none;
}

.activity-comments .acomment-avatar {
	display: inline-block;
	vertical-align: middle;
}

.activity-comments .acomment-meta {
	display: flex;
	align-items: center;
}

#buddypress div.activity-comments div.acomment-content {
	margin: 1em 0 0;
	font-size: 100%;
}

.acomment-content img {
	border-radius: var(--border-radius);
	margin-top: 1em;
}

#buddypress .acomment-options {
	margin: .5em 0 1em;
}

.comment-container-main {
	position: relative;
	padding: 0;
	background: transparent;
	border-radius: 0;
}

#buddypress div.activity-comments-list>ul.activity-comments .activity-comments {
	padding-left: 1.5em;
}

#buddypress .acomment-options {
	float: inherit;
	margin: 0;
}

.activity-comments .comment-container-main .acomment-meta>a {
	font-size: var(--font-size-normal);
	color: var(--global-font-title);
	font-weight: var(--font-weight-medium);
	font-family: var(--highlight-font-family);
}

.acomment-meta-info {
	line-height: normal;
}

.activity-comments .acomment-content p {
	font-size: var(--font-size-normal);
	word-break: break-word;
}

#buddypress .acomment-options a.socialv-acomment-reply {
	margin-right: .8em;
}

#buddypress .acomment-options a.socialv-acomment-reply,
#buddypress .acomment-options .acomment-delete {
	color: var(--global-font-color);
}

#buddypress .acomment-options a.socialv-acomment-reply:hover {
	color: var(--color-theme-primary);
}

#buddypress .acomment-options .acomment-delete:hover {
	color: var(--color-theme-danger);
}

#buddypress form#whats-new-form textarea {
	background: var(--global-body-bgcolor);
	padding: 1em;
	font-size: 1em;
	min-height: 11.554em;
	color: var(--global-font-color);
}

#buddypress #activity-stream p,
.activity-comments-test-popup .activity-content .activity-inner p {
	margin: .375em 0;
	color: var(--comment-font-color);
}

#buddypress #activity-stream .activity-header p,
.activity-comments-test-popup .activity-header p {
	margin: 0;
	position: relative;
}

#buddypress #activity-stream .socialv-blog-detail p {
	margin: 1em 0;
}

#buddypress .socialv-list-post .socialv-blog-box .bb-post-img-link {
	position: absolute;
	top: 0;
	bottom: 0;
	left: 0;
	right: 0;
	z-index: 1;
}

.new_blog_post .socialv-blog-box {
	border: 1px solid var(--border-color-light);
	padding: 1em;
	border-radius: var(--border-radius);
	background: var(--global-body-bgcolor);
}

.activity-content.activity-sharing .shared-activity .socialv-blog-box {
	margin-bottom: 0;
	background: var(--color-theme-white-box);
}

.new_blog_post .blog-post-image {
	text-align: center;
	background-repeat: no-repeat;
	background-size: cover;
	border-radius: var(--border-radius);
	overflow: hidden;
}

.new_blog_post .blog-post-image-inner {
	backdrop-filter: blur(10px);
	overflow: hidden;
	border-radius: var(--border-radius);
}

#buddypress .comment-container-main .acomment-options {
	position: relative;
	margin-left: 3.5em;
	top: 0;
	right: 0;
}

#buddypress .comment-container-main .acomment-options a,
.comment-container-main .acomment-options a,
#buddypress .comment-container-main .acomment-options a.bp-secondary-action {
	font-size: var(--font-size-xs);
	text-transform: capitalize;
	letter-spacing: var(--letter-spacing-one);
	font-weight: var(--font-weight-medium);
}

#buddypress #activity-stream .comment-container-main p {
	margin: .5em 0 0;
	word-break: break-word;
}

#buddypress #activity-stream .comment-container-main .acomment-content p {
	margin: 0;
}

.activity-comments .comment-container-main .acomment-content,
#buddypress div.activity-comments div.acomment-content,
#buddypress ul.activity-comments .comment-container-main .acomment-content {
	display: inline-block;
	position: relative;
	padding: .75em 1em;
	margin: .3em 0 .3em 3.5em;
	background: var(--global-body-bgcolor);
	border-radius: var(--border-radius-box);
}

@media (min-width: 768px) {

	.grid-view .socialv-groups-lists .group-has-avatar:nth-last-child(-n+2) .socialv-group-info,
	.list-view .socialv-groups-lists .group-has-avatar:last-child .socialv-group-info {
		margin-bottom: 0;
	}

	#buddypress form#whats-new-form textarea {
		min-height: 9.5em;
	}
}

@media (min-width: 567px) {
	.comment-container-main .acomment-options {
		position: absolute;
		top: .2em;
		right: 1em;
	}
}

@media (max-width: 767px) {
	.socialv-groups-lists .group-has-avatar:last-child .socialv-group-info {
		margin-bottom: 0;
	}

	.socialv-group-filter {
		justify-content: space-between;
		border-top: .063em solid var(--border-color-light);
		display: block;
	}

	.socialv-group-filter .socialv-data-filter-by {
		border: none;
	}
}

@media (max-width: 479px) {
	.socialv-group-filter {
		display: block;
	}

	.list-grid-btn-switcher {
		padding: 0 0 1em;
		justify-content: center;
	}

	.width-two-column {
		width: 100%;
	}

	.width-two-column.one {
		padding-right: 0;
	}

	.width-two-column.two {
		padding-left: 0;
	}

	#buddypress div.activity-comments-list>ul.activity-comments .activity-comments {
		padding-left: 1em;
	}

}

@media (max-width: 399px) {
	#buddypress div.activity-comments-list>ul.activity-comments .activity-comments{
		padding-left: .3em;
	}
}

/* activity page End */

/*friend tab */
.socialv-bp-pagination {
	text-align: center;
	margin-top: 2em;
}

ul.socialv-sub-tab-lists {
	white-space: nowrap;
	overflow-x: auto;
}

#bbpress-forums li.bbp-body ul.forum,
#bbpress-forums li.bbp-body ul.topic {
	border-color: var(--border-color-light);
	padding: 2em;
}

#bbpress-forums div.odd,
#bbpress-forums ul.odd,
#bbpress-forums .status-spam.odd,
#bbpress-forums .status-trash.odd,
#bbpress-forums div.even,
#bbpress-forums ul.even {
	background: var(--global-body-bgcolor);
}

#bbpress-forums img.avatar {
	margin-right: 0;
}

#bbpress-forums li.bbp-header {
	background: var(--color-theme-primary);
	padding: 1.5em 2em;
	font-weight: var(--font-weight-regular);
}

.bbp-header ul.forum-titles li {
	color: var(--color-theme-white);
}

/*foram tab*/
#bbpress-forums {
	font-size: inherit;
}

.bbp-topic-title .topic-meta-box .name {
	font-size: var(--font-size-normal);
}

.bbp-topic-title .topic-meta-box i {
	font-size: 1.25em;
}

#bbpress-forums li.bbp-body ul.forum,
#bbpress-forums li.bbp-body ul.topic {
	border: none;
}

#bbpress-forums li.bbp-body ul.topic {
	position: relative;
}

#bbpress-forums li.bbp-body ul.topic::after {
	content: "";
	position: absolute;
	left: 2em;
	right: 2em;
	border: 1px solid var(--border-color-light);
	bottom: 0;
}

#bbpress-forums li.bbp-body:last-child ul.topic::after {
	display: none;
}

#bbpress-forums .bbp-topics-info {
	border-radius: var(--border-radius-box);
	overflow: hidden;
}

tr.status-closed h6,
tr.status-closed a,
tr.status-closed i,
table.forums-table tr.status-closed td a {
	color: var(--disable-color);
}

tr.status-closed .bp-verified-badge {
	background-color: var(--disable-color) !important;
}

table.forums-table {
	border: none;
	border-collapse: collapse;
	border-radius: var(--border-radius-box);
	overflow: hidden;
	margin-bottom: 0;
}

table.forums-table tr th {
	padding: 1.5em 2em;
	background: var(--color-theme-primary);
	border: none;
}

table.forums-table tr th span {
	color: var(--color-theme-white);
}

table.forums-table tr:nth-child(n+1) {
	background: var(--color-theme-white-box);
}

table.forums-table tr:nth-child(n+1) td {
	padding: 2em;

}

#bbpress-forums #favorite-toggle,
#bbpress-forums #subscription-toggle {
	float: left;
	margin: 1.5em 0;
}

.forums-table .bbp-row-actions #subscription-toggle span.is-subscribed a {
	background: transparent;
	border: none;
	font-size: 1.3em;
}

.forums-table .bbp-row-actions #favorite-toggle span.is-favorite a {
	display: inline-block;
	text-align: center;
	width: 1.5em;
	height: 1.5em;
	line-height: 1.5em;
	border-radius: var(--border-radius);
	margin-top: .125em;
	padding: 0;
	vertical-align: middle;
}

.forums-table .bbp-row-actions #favorite-toggle span.is-favorite a i {
	display: inline-block;
	vertical-align: text-bottom;
	font-size: .8em;
	line-height: 1.5em;
}

.voice-count-sv,
.reply-count-sv {
	color: var(--global-font-color);
}

table.forums-table tr td a {
	color: inherit;
}

table.forums-table .topic-meta-box i {
	font-size: 1.1em;
}

table.forums-table .topic-meta-box .name {
	font-size: var(--font-size-normal);
	color: var(--global-font-color);
}

.topic-meta-box .icons-main-meta {
	color: var(--global-font-title);
}

table.forums-table tr td {
	border: none;
}

table.forums-table tr:nth-child(n+1) {
	position: relative;
}

table.forums-table tr:nth-child(1)::after,
table.forums-table tr:last-child::after {
	display: none;
}

table.forums-table tr:nth-child(n+1)::after {
	content: "";
	position: absolute;
	left: 2em;
	right: 2em;
	border: 1px solid var(--border-color-light);
	bottom: 0;
}

table.forums-table .sv-voices,
table.forums-table .sv-post {
	color: var(--global-font-color);
}

@media screen and (min-width: 1200px) and (max-width: 1365px) {

	table.forums-table,
	.table-responsive table {
		overflow-x: auto;
		min-width: 47.75em;
	}

}

@media(max-width:767px) {

	table.forums-table tr:nth-child(n+1) td,
	table.forums-table tr th {
		padding: 1em;
	}

	table.forums-table,
	.table-responsive table {
		overflow-x: auto;
		min-width: 45.75em;
	}
}

/*foram tab end*/

.bbp-pagination {
	padding: 2em 0;
	margin-bottom: 0;
}

img.alignnone.size-full {
	width: 100%;
}

.replies-tab {
	clear: both;
}

#bbpress-forums {
	line-height: normal;
}

.replies-tab .main-bp-details {
	position: relative;
}

.replies-tab-table .main-bp-details p {
	margin-bottom: 0;
	margin-top: .5em;
	font-size: var(--font-size-normal);
}

#bbpress-forums ul,
#bbpress-forums.bbpress-wrapper ul {
	font-size: var(--font-size-normal);
}

.replies-tab .main-bp-details>div>a {
	color: var(--global-font-title);
	font-size: var(--font-size-normal);
	font-weight: var(--font-weight-medium);
	font-family: var(--highlight-font-family);
}

#bbp-cancel-reply-to-link {
	margin-right: 1em;
}

.replies-tab .main-head-replies {
	background: var(--color-theme-primary);
	padding: 1.5em 2em;
}

.replies-tab .main-head-replies .bbp-reply-author,
.replies-tab .main-head-replies .bbp-reply-content {
	color: var(--color-theme-white);
}

.sv-author-role {
	background: var(--color-theme-primary-light);
	font-size: var(--font-size-xs);
	padding: .1em .875em;
	border-radius: var(--border-radius-pill);
	color: var(--color-theme-primary);
}

.replies-tab .replies-tab-table {
	padding: 2em;
	background: var(--color-theme-white-box);
	margin-bottom: 2.5em;
	border-radius: var(--border-radius);
}

.replies-tab .bbp-reply-form {
	margin-top: 2em;
}

.replies-tab ul.bbp-threaded-replies {
	padding-left: 3.75em;
	list-style: none;
}

.replies-tab-table .sv-reply-post-date>span {
	font-size: var(--font-size-normal);
}

.replies-tab-table .bbp-header {
	font-size: var(--font-size-normal);
}

.replies-tab .bp-member-img img {
	border-radius: var(--border-radius);
}

.replies-tab .main-bp_members {
	gap: 1em;
	display: flex;
	padding: 1em;
	background: var(--global-body-bgcolor);
	border-radius: var(--border-radius-box);
}

.sv-reply-post-date {
	padding: 0 0 1em;
	gap: 1em;
	flex-wrap: wrap;
}

@media(max-width:767px) {

	.replies-tab .replies-tab-table {
		padding: 1em;
	}

	.replies-tab .main-head-replies {
		padding: 1.5em;
	}

	.sv-author-role {
		font-size: var(--font-size-small);
		padding: .175em .875em;
	}
}

@media(max-width:479px) {
	.replies-tab .main-bp_members {
		flex-direction: column;
	}

}

/*replied tab end*/

/*dropdown*/

.dropdown ul li a {
	font-size: var(--font-size-normal);
	color: var(--global-font-color);
}

.dropdown .dropdown-item {
	color: var(--global-font-color);
	font-size: var(--font-size-normal);
}

.dropdown .dropdown-item:hover,
.dropdown ul li a:hover,
.dropdown ul li a:focus {
	color: var(--color-theme-primary);
	background: transparent;
}

.dropdown .dropdown-item:focus {
	background: transparent;
}

/*dropdown end*/

/*Comment */

form.ac-form.socialv-comment-form {
	border: 1px solid var(--border-color-light);
	position: relative;
	margin-top: 1.25em;
	padding: .625em;
	position: relative;
	display: none;
}

.activity-comment-form textarea {
	background: transparent;
	min-height: auto;
	padding: 0;
}

form.ac-form.socialv-comment-form .socialv-form-wrapper {
	gap: 1em;
	display: flex;
}

form.ac-form.socialv-comment-form textarea:focus {
	border-color: transparent;
}

form.ac-form.socialv-comment-form .socialv-form-wrapper .ac-reply-avatar>img {
	width: 2em;
	height: 2em;
	min-width: 2em;
	min-height: 2em;
}

#buddypress div.ac-reply-avatar img {
	border: none;
}

form.ac-form.socialv-comment-form .ac-reply-content {
	flex: 1;
}

.activity-comments form textarea,
#buddypress div.activity-comments form textarea {
	min-height: 2em;
	height: 2em;
	border: none;
	padding: 0 3.75em 0 0;
	resize: none;
	background: transparent;
	color: var(--comment-font-color);
}

#buddypress div.activity-comments form div.ac-reply-content {
	margin-left: 0;
}

#buddypress div.activity-comments form .ac-textarea,
.ac-reply-content>.ac-textarea {
	border: none;
	margin-bottom: 0;
	padding: 0;
	background: var(--color-theme-white-box);
	line-height: 0;
}

#buddypress ul.activity-list li ul.activity-comments li .acomment-content>p img {
	display: block;
	border-radius: var(--border-radius);
}

.socialv-form .ac-reply-content .activity-giphy-container {
	margin-left: 0;
	margin-top: 1em;
}

.activity-comments form,
form.ac-form.socialv-comment-form {
	border: .063em solid var(--border-color-light);
	position: relative;
	margin-top: 1.25em;
	padding: .625em;
	border-radius: var(--border-radius-box);
}

#buddypress ul.activity-list li ul.activity-comments li {
	padding: 1.25em 0 0;
	border-bottom: none;
}

#buddypress ul.socialv-list-post li .activity-comments form img.avatar {
	width: 2em;
	height: 2em;
	min-width: 2em;
	min-height: 2em;
	border-radius: var(--border-radius);
}

#buddypress div.activity-comments form div.ac-reply-content {
	padding-left: 0;
}

#buddypress button.send-comment-btn.ac_form_submit {
	padding: 0;
	background: var(--global-body-bgcolor);
	transition: all .3s ease-in-out;
	border: none;
}

#buddypress button.send-comment-btn.ac_form_submit:hover {
	background: var(--color-theme-primary);
}

button.send-comment-btn.ac_form_submit:before {
	content: "\e94c";
	font-family: 'iconly' !important;
	font-weight: 200;
	transition: all .3s ease-in-out;
}

button.send-comment-btn.ac_form_submit:hover:before {
	color: var(--color-theme-white);
}

#buddypress div.activity-comments form input {
	right: 1em;
	top: 1em;
	margin-top: 0;
}

/******Gif****/
.buddypress-giphy-active .ac-reply-content .bp-giphy-html-container {
	position: absolute;
	right: 49px;
	top: 13px;
}

.buddypress-giphy-active #buddypress div.activity-comments form textarea,
.activity-comments form textarea {
	padding: 0 4.75em 0 0;
}

.activity-attached-gif-container .gif-image-container .gif-player img {
	border-radius: var(--border-radius);
}

.bp-acivity-gif-image-remove.bp-acivity-gif-image-overlay {
	top: 10px;
	right: 10px;
	box-shadow: var(--global-box-shadow);
	background: var(--color-theme-white-box);
	color: var(--global-font-title);
	width: 1.3em;
	height: 1.3em;
	position: absolute;
	line-height: 1.3em;
	text-align: center;
	border-radius: var(--border-radius);
}

.bp-giphy-media-search-dropdown.open {
	display: none;
}

.bp-giphy-media-search-dropdown.open.show {
	display: block;
}

.bp-giphy-media-search-dropdown {
	background: var(--color-theme-white-box);
	box-shadow: var(--global-box-shadow);
	border: .0625em solid var(--border-color-light);
	right: 0;
}

.gif-search-query {
	background: transparent;
}

.activity-attached-gif-container {
	background: var(--color-theme-white-box);
	border-radius: var(--border-radius);
}

.bp-giphy-media-search-dropdown:before {
	border-color: var(--color-theme-white-box) var(--color-theme-white-box) transparent transparent;
	box-shadow: var(--global-box-shadow);
	left: auto;
	right: 20px;
	top: .67em;
}

@media (max-width: 479px) {
	.bp-giphy-media-search-dropdown.open {
		width: 270px;
	}

	.bp-giphy-media-search-dropdown {
		right: 0;
		left: 0;
	}
}

/******Gif End****/
.send-comment-btn {
	position: absolute;
	width: 2.5em;
	height: 2.5em;
	top: 9px;
	right: .625em;
	border-radius: var(--border-radius);
	line-height: 2.6em;
	padding: 0;
}

.comment-container-main .acomment-header {
	display: flex;
	align-items: center;
	gap: .5em;
}

.comment-container-main .acomment-header .acomment-avatar-sv img.photo {
	height: 2.5em;
	width: 2.5em;
	min-width: 2.5em;
}

.acomment-header-left {
	display: flex;
	align-items: flex-start;
	gap: 1em;
}

.activity-time-main {
	font-weight: var(--font-weight-regular);
	font-size: var(--font-size-small);
	padding-left: 1em;
}

#buddypress .acomment-meta .activity-time-main a {
	color: var(--global-font-color);
	font-weight: var(--font-weight-regular);
}

/*Comment End*/

/*load-more*/
#buddypress .activity-list li.load-more a,
#buddypress .activity-list li.load-newest a {
	color: var(--color-theme-white);
}

#buddypress .activity-list li.load-more,
#buddypress .activity-list li.load-newest {
	background: transparent;
}

.dropdown-menu {
	background-color: var(--color-theme-white-box);
	color: var(--global-font-color);
	border: .0625em solid var(--border-color-light);
	box-shadow: var(--global-box-shadow);
	z-index: 98;
}

/*load-more End*/


/*breadcrumb */
.bbp-breadcrumb {
	margin-bottom: 1em;
}

.bbp-breadcrumb a {
	font-size: var(--font-size-normal);
	color: var(--global-font-title);
	display: inline-block;
	margin-right: .625em;
}

.bbp-breadcrumb .bbp-breadcrumb-sep {
	vertical-align: text-top;
	color: var(--global-font-title);
	display: inline-block;
	margin-right: .25em;
}

.bbp-breadcrumb i {
	display: inline-block;
	vertical-align: text-bottom;
	line-height: normal;
	margin-right: .625em;
	color: var(--global-font-title);
}

.bbp-breadcrumb-current {
	color: var(--color-theme-primary);
}

div.bbp-breadcrumb,
div.bbp-topic-tags {
	font-size: inherit;
}

div.bbp-template-notice li,
div.bbp-template-notice p {
	line-height: 1.75;
	padding: 0;
	margin: 0 !important;
}


.socialv_bppress_forumb-innerbox .forum-btn-box {
	display: flex;
	align-items: center;
	gap: .8em;
	margin-bottom: 1.5em;
}

.socialv_bppress_forumb-innerbox .bbp-template-notice {
	flex: 1;
}

.socialv_bppress_forumb-innerbox #favorite-toggle a {
	width: 2.875em;
	height: 2.875em;
	line-height: 2.875em;
	text-align: center;
	background: var(--color-theme-orange);
	border-radius: var(--border-radius);
	display: inline-block;
	color: var(--color-theme-white);
}

div.bbp-breadcrumb {
	float: inherit;
	margin-bottom: 0;
}

div.bbp-breadcrumb p {
	margin-bottom: 0;
}

@media(max-width:1199px) {
	.socialv_bppress_forumb-innerbox .bbp-template-notice {
		width: 100%;
	}

	.socialv_bppress_forumb-innerbox {
		flex-direction: column;
	}
}

/*breadcrumb End */

/*Media */
.mpp-item-entry .mpp-item-thumbnail img,
.mpp-item-single .mpp-item-entry img.mpp-large,
.mpp-image {
	padding: 0;
	border-radius: var(--border-radius) var(--border-radius) 0 0;
	box-shadow: none;
	width: 100%;
}

a.mpp-gallery-title,
.socialv-video-single,
.socialv-audio-single,
.socialv-doc-single {
	background-color: var(--global-body-bgcolor);
	line-height: normal;
	display: block;
	border-radius: 0 0 var(--border-radius) var(--border-radius);
	padding: .625em;
	font-size: var(--font-size-normal);
	color: var(--global-font-color);
}

.mpp-video-player {
	max-width: 100%;
}

.socialv-media-inner .socialv-media-container>a:after {
	border-radius: var(--border-radius) var(--border-radius) 0 0;
}

#buddypress .socialv-media-inner .socialv-media-container img {
	border-radius: var(--border-radius) var(--border-radius) 0 0;
}

#buddypress .socialv-media-inner .socialv-media-container:hover {
	-webkit-transform: translateY(0);
	transform: translateY(0);
}

.mpp-g.mpp-item-list.mpp-galleries-list {
	background-color: var(--color-theme-white-box);
	padding: 2em;
	border-radius: var(--border-radius-box);
	min-height: 17em;
}

.socialv-create-gallery .create-gallery-detail {
	position: relative;
	border-radius: var(--border-radius);
	background-color: var(--global-body-bgcolor);
	cursor: pointer;
	width: 100%;
	height: 100%;
}

.mpp-item.mpp-gallery {
	position: relative;
	overflow: hidden;
	padding: .5em;
	margin-bottom: 0;
}

.mpp-item.mpp-gallery .mpp-item-entry.mpp-gallery-entry {
	position: relative;
}

.mpp-item.mpp-gallery .mpp-item-entry.mpp-gallery-entry:after {
	content: "";
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background-color: rgba(0, 0, 0, .8);
	opacity: 0;
	-webkit-transition: all .5s ease;
	transition: all .5s ease;
	border-radius: var(--border-radius) var(--border-radius) 0 0;
}

.mpp-item.mpp-gallery .mpp-delete-gallery {
	position: absolute;
	top: 1.5em;
	right: 1.5em;
	-webkit-transition: all .5s ease;
	transition: all .5s ease;
	opacity: 0;
	z-index: 1;
	color: var(--color-theme-white);
}

.mpp-item.mpp-gallery:hover .mpp-item-entry.mpp-gallery-entry:after,
.mpp-item.mpp-gallery:hover .mpp-delete-gallery {
	opacity: 1;

}

.socialv-create-gallery .text-center {
	position: absolute;
	top: 50%;
	left: 50%;
	margin-right: -50%;
	transform: translate(-50%, -50%);
}

.socialv-create-gallery .text-center i {
	font-size: 2em;
	color: var(--global-font-color);
}

.mpp-item {
	line-height: normal;
}


/*Media  inner section*/
.mfp-arrow-left:after,
.mfp-arrow-right:after {
	color: var(--color-theme-white);
}

button.mfp-arrow {
	border-radius: var(--border-radius);
}

.single-media-header {
	display: flex;
	flex-wrap: wrap;
	justify-content: space-between;
	gap: 1em;
	align-items: center;
	margin-bottom: 2em;
	padding: 0 .5em;
}

.single-media-header .socialv-check.select-media-icon {
	cursor: pointer;
}

.socialv-media-list .mpp-item {
	padding: 0 .5em;
	margin-bottom: 1em;
}

.all-media-action .socialv-check span.select-all-label {
	font-size: var(--font-size-xs);
	display: inline-block;
	vertical-align: middle;
	padding-left: 0;
	color: var(--global-font-color);
	vertical-align: text-bottom;
}

.all-media-action .multi-delete-media-icon label {
	font-size: var(--font-size-xs);
	color: var(--global-font-color);
}

button.btn.btn-light {
	color: var(--global-font-title);
	font-size: var(--font-size-xs);
	background-color: var(--global-body-bgcolor);
	padding: .75em 1.5em;
	transition: all .45s ease-in-out;
}

button.btn.btn-light:hover {
	background-color: var(--color-theme-primary);
	color: var(--color-theme-white);
}

.btn-check:active+.btn-light:focus,
.btn-check:checked+.btn-light:focus,
.btn-light.active:focus,
.btn-light:active:focus,
.show>.btn-light.dropdown-toggle:focus,
.btn-check:focus+.btn-light,
.btn-light:focus {
	box-shadow: none;
}

button.btn.btn-light i {
	font-size: 1.4em;
	display: inline-block;
	vertical-align: baseline;
	margin-right: .1875em;
}

.single-media-inner {
	padding: 2em 1.5em;
	background: var(--color-theme-white-box);
	border-radius: var(--border-radius-box);
}

.single-media-header .socialv-check .checkmark {
	top: .1875em;
}

.single-media-header .socialv-check.select-media-icon .checkmark,
.single-media-header .socialv-check.select-media-icon .checkmark:after {
	border-color: var(--color-theme-primary);
}

.socialv-media-container .socialv-check.select-media-icon {
	position: absolute;
	top: 0;
	left: 1.5em;
}

.all-media-action .socialv-check label {
	cursor: pointer;
}

.all-media-action .socialv-check label input[type=checkbox] {
	position: relative;
	top: 2px;
	cursor: pointer;
}

input[type=checkbox].select-media-checkbox {
	margin-right: .225em;
}

.socialv-delete-media.multi-delete i {
	font-size: 1.3em;
	display: inline-block;
	vertical-align: middle;
}

.single-media-header .socialv-check.select-media-icon .checkmark:after {
	left: .35em;
}

.socialv-check.select-media-icon .checkmark:after {
	content: "";
	position: absolute;
	top: .04em;
	left: .3em;
	display: block;
	border-color: var(--color-theme-white);
	border-width: 0 .125em .125em 0;
}

.socialv-check.select-media-icon .checkmark {
	border-color: var(--color-theme-white);
	border: .125em solid var(--color-theme-white);
	height: 1.25em;
	width: 1.25em;
}

.socialv-check.select-media-icon input:checked~.checkmark:after {
	border-color: var(--color-theme-white);
	top: .125em;
	left: .375em;
}

.socialv-media-container {
	position: relative;
}

.mpp-video-player.socialv-media-container {
	margin: 0;
	padding: 0;
}

.socialv-media-container>a {
	position: relative;
	display: block;
}

.socialv-media-container>a::after {
	content: " ";
	position: absolute;
	background: rgba(0, 0, 0, .8);
	border-radius: 3px;
	width: 100%;
	height: 100%;
	left: 0;
	top: 0;
	visibility: hidden;
	opacity: 0;
	border-radius: var(--border-radius-box);
	transition: .3s all;
}

.socialv-media-container .socialv-check.select-media-icon,
.socialv-media-container .delete-media-icon {
	visibility: hidden;
	opacity: 0;
	transition: .5s all;
}

.socialv-media-container:hover>a::after,
.socialv-media-container.socialv-selected>a::after,
.socialv-media-container.socialv-selected .socialv-check.select-media-icon,
.socialv-media-container.socialv-selected .delete-media-icon,
.socialv-media-container:hover .socialv-check.select-media-icon,
.socialv-media-container:hover .delete-media-icon {
	visibility: visible;
	opacity: 1;
}

.all-media-action .multi-delete-media-icon label {
	display: inline-block;
	vertical-align: text-bottom;
}

.socialv-media-container .delete-media-icon {
	position: absolute;
	top: 1.2em;
	right: 1em;
}

.delete-media-icon i {
	color: var(--color-theme-white);
	font-size: 1.3em;
}

#buddypress .socialv-media-container {
	-webkit-transition: all .3s ease;
	transition: all .3s ease;
	box-shadow: var(--global-box-shadow);
}

#buddypress .socialv-media-container img {
	border-radius: var(--border-radius-box);
	width: 100%;
}

#buddypress .socialv-media-container:hover {
	-webkit-transform: translate(0, -2px);
	-ms-transform: translate(0, -2px);
	transform: translate(0, -2px);
}

span.mpp-remove-file-attachment {
	display: none;
}

/*Media End*/

/*Login form*/
.socialv-forum-topic-card .card-main.socialv-bp-login {
	max-width: 100%;
	margin: 2em auto 0;
}

.socialv-forum-topic-card .card-main.socialv-bp-login .card-inner {
	padding: 0;
}

.socialv-forum-topic-card .card-main.socialv-bp-login .card-inner fieldset.bbp-form-sv {
	padding: 0;
}

.socialv-forum-topic-card fieldset.bbp-forms-sv {
	padding: 0;
}

/*Login form End*/

/* Create Album model*/
.create-album-main .modal-header {
	border: none;
}

.create-album-main .modal-body {
	padding: 1em 2em 4em;
}

/* Create Album model End*/

/* Upload section*/
.mpp-dropzone {
	border: .15em dashed rgba(111, 127, 146, .4);
	padding: 2.5em;
	height: auto;
}

#buddypress #whats-new:focus {
	border-color: var(--color-theme-primary) !important;
}

.mpp-drag-drop-inside {
	width: 100%;
}

.mpp-drag-drop-inside p {
	font-size: inherit;
}

#buddypress .mpp-dropzone input[type=button].button.mpp-button-select-files {
	width: auto;
}

.mpp-drag-drop-inside p.mpp-uploader-allowed-file-type-info,
.mpp-drag-drop-inside p.mpp-uploader-allowed-file-type-info+p {
	font-size: var(--font-size-normal);
}

.mpp-add-remote-media {
	color: var(--color-theme-white);
	font-family: var(--highlight-font-family);
	font-weight: var(--font-weight-semi-bold);
	line-height: normal;
	background-color: var(--color-theme-primary);
	border-radius: var(--border-radius);
	font-size: var(--font-size-xs);
	padding: 1.2em 2.25em;
	border: none;
	text-transform: uppercase;
	display: inline-block;
	letter-spacing: var(--letter-spacing-one);
}

.mpp-remote-add-media-row {
	padding-right: 0;
	margin: 0;
}

.mpp-remote-add-media-row input {
	padding-right: 4em;
}

#buddypress .mpp-remote-add-media-row .mpp-add-remote-media {
	position: absolute;
	left: auto;
	right: 0;
	top: 0;
	height: 97%;
	padding: 0 1.5em;
	margin: .063em;
	background: var(--color-theme-primary-light);
	color: var(--color-theme-primary);
	border-radius: 0 var(--border-radius) var(--border-radius) 0;
}

/* Upload section end*/
/*friend list popup*/

.friend-list-popup .user-who-liked {
	display: flex;
	align-items: center;
	gap: 1em;
}

.friend-list-popup .user-who-liked .like-details,
.friend-list-popup .name-list {
	justify-content: space-between;
	display: flex;
	width: 100%;
	gap: 1em;
	align-items: center;
}

.friend-list-popup ul li {
	list-style: none;
	margin-bottom: 1em;
}

.friend-list-popup ul li:last-child {
	margin-bottom: 0;
}

.friend-list-popup .like-details a p {
	color: var(--global-font-color);
	font-size: var(--font-size-xs);
}

.friend-list-popup .name-list {
	padding-bottom: 1em;
	margin-bottom: 1.25em;
	border-bottom: .0625em solid var(--border-color-light);
}

.friend-list-popup .user-who-liked .like-details .liked i {
	color: var(--color-theme-danger);
}

/*friend list popup end*/


/*forums search table */
#bbpress-forums ul.bbp-search-results {
	font-size: 1em;
	border-radius: var(--border-radius-box);
}

#bbpress-forums li.bbp-header .bbp-search-author {
	font-size: 1em;
	width: auto;
	color: var(--color-theme-white);
}

#bbpress-forums li.bbp-header .bbp-search-content {
	color: var(--color-theme-white);
	font-weight: var(--font-weight-regular);
	font-size: 1em;
}

div.bbp-forum-header,
div.bbp-reply-header,
div.bbp-topic-header,
li.bbp-body div.hentry {
	padding: 1em 2em;
}

#bbpress-forums div.bbp-forum-header,
#bbpress-forums div.bbp-reply-header,
#bbpress-forums div.bbp-topic-header {
	background: var(--global-body-bgcolor);
}

div.bbp-forum-header,
div.bbp-reply-header,
div.bbp-topic-header {
	border-top: 1px solid var(--border-color-light);
}

#bbpress-forums li.bbp-footer {
	border-top: 1px solid var(--border-color-light);
	background: var(--global-body-bgcolor);
}

.forum-search #bbpress-forums div.bbp-forum-content,
.forum-search #bbpress-forums div.bbp-reply-content,
.forum-search #bbpress-forums div.bbp-topic-content {
	margin: 0;
	padding: 0;
}

#bbpress-forums .bbp-topic-content ul.bbp-topic-revision-log {
	border: none;
}

#bbpress-forums .bbp-topic-author {
	position: relative;
	z-index: 1;
}

#bbpress-forums div.bbp-topic-author img.avatar {
	border-radius: 100%;
	max-width: 5em;
}

#bbpress-forums div.bbp-forum-author,
#bbpress-forums div.bbp-reply-author,
#bbpress-forums div.bbp-topic-author {
	width: 8em;
}

@media only screen and (max-width: 320px) {

	#bbpress-forums li.bbp-footer,
	#bbpress-forums li.bbp-header {
		font-size: var(--font-size-normal);
	}
}

.mfp-content img.mfp-img {
	padding: 0;
	border-radius: var(--border-radius);
	max-height: 90vh !important;
}

/*forums search table end */

/* gemipress */
.socialv-profile-left .gamipress-user-points:not(.gamipress-layout-none) {
	margin: 0 0 1em;
	justify-content: center;
	gap: 1em;
}

.socialv-profile-left .gamipress-user-points:not(.gamipress-layout-none) .gamipress-points {
	width: auto;
}

.socialv-profile-left .gamipress-user-points-image img {
	width: 1.5em;
	max-width: 100%;
}

.socialv-profile-left .gamipress-user-points[class*=gamipress-columns]>.gamipress-points {
	padding: 0;
	gap: .5em;
}

.socialv-profile-left .gamipress-user-points-description {
	font-size: 1em;
	font-weight: var(--font-weight-semi-bold);
	color: var(--global-font-title);
	text-transform: capitalize;
}

.socialv-blog-main-list .gamipress-points {
	position: relative;
	margin-bottom: 1.5em !important;
	flex-direction: column;
	text-align: center;
	padding-top: 2em !important;
	padding-bottom: 2em !important;
}

.socialv-blog-main-list .gamipress-points::after {
	position: absolute;
	content: "";
	top: 0;
	bottom: 0;
	left: 1em;
	right: 1em;
	background: var(--color-theme-white-box);
	z-index: -1;
	border-radius: var(--border-radius);
}

.socialv-blog-main-list .gamipress-user-points.gamipress-columns-3 {
	width: auto;
}

.socialv-blog-main-list .gamipress-points .gamipress-user-points-description span {
	display: block;
}

.socialv-blog-main-list .gamipress-points .gamipress-user-points-description .gamipress-user-points-amount {
	font-size: 2em;
	color: var(--global-font-title);
	font-weight: var(--font-weight-semi-bold);
}

.socialv-blog-main-list .gamipress-user-points-description {
	margin-top: 1em;
}

.socialv-blog-main-list .gamipress-points .gamipress-user-points-description .gamipress-user-points-label {
	color: var(--global-font-title);
	font-weight: var(--font-weight-medium);
	text-transform: capitalize;
}

.socialv-blog-main-list .gamipress-user-points-image {
	width: 4em;
	height: 4em;
	line-height: 4em;
}

#buddypress .mce-menubtn.mce-fixed-width button {
	letter-spacing: 0;
	text-transform: capitalize;
}

.replies-tab .bbp-admin-links a:hover {
	color: var(--color-theme-primary);
}

.replies-tab .bbp-admin-links a.bbp-topic-trash-link {
	color: var(--color-theme-danger);
}


/* chat */
.bp-messages-wrap {
	border: none;
	color: var(--global-font-color);
	background: transparent;
}

.bp-messages-threads-wrapper {
	gap: 2em;
}

/* chat left panel */
.bp-messages-wrap .bp-messages-side-threads>*,
.bp-messages-wrap .bp-messages-side-threads {
	width: 360px;
}

.bp-messages-wrap .bp-messages-side-threads>* {
	border-right: none;
}

.bp-messages-wrap .bp-messages-side-threads {
	border: none;
}

.bp-messages-wrap .chat-header.side-header {
	min-height: 3.7em;
	height: 3.7em;
	flex-direction: row-reverse;
	border: none;
	padding: 0 1em;
	margin-bottom: 1em;
	border-radius: var(--border-radius);
	background: var(--color-theme-white-box);
}

.bp-messages-wrap .chat-header.side-header .new-message+.bpbm-search {
	width: calc(100% - 17px);
}

.bp-messages-wrap .chat-header .new-message,
.bp-messages-wrap .chat-footer .new-message {
	height: auto;
	width: auto;
}

.bp-messages-wrap .chat-header .new-message {
	background: var(--color-theme-primary-light);
	color: var(--color-theme-primary);
	padding: 0;
	border-radius: var(--border-radius);
	height: 2.3em;
	width: 2.3em;
	min-width: 2.3em;
	line-height: 2.5em;
	text-align: center;
	display: inline-block;
	font-weight: 200;
	margin: 0 0 0 .5em;
}

.bp-messages-wrap .chat-header .bpbm-search form {
	margin: 0 !important;
	height: 2.3em;
	line-height: 2.3em;
}

.bp-messages-wrap .chat-header .bpbm-search form input[type=text] {
	background: var(--global-body-bgcolor) !important;
	border: .0625em solid var(--border-color-light) !important;
	color: var(--global-font-color) !important;
	font-size: var(--global-font-size);
	min-height: 2.273em !important;
	max-height: 2.273em !important;
	height: 2.273em;
	line-height: 3.1em;
}

.bp-messages-wrap .chat-header .bpbm-search form input[type=text]:focus {
	border-color: var(--color-theme-primary) !important;
}

.bp-messages-wrap .chat-header .new-message svg {
	width: 1em;
	position: static;
}

.bp-messages-wrap .bm-search-results .bm-search-results-section .bm-search-results-header {
	color: var(--global-font-title);
	background: transparent;
	border: none;
	padding: 1em 0 0;
}

.bp-messages-wrap .bm-search-results .bm-search-results-section+.bm-search-results-section .bm-search-results-header {
	border: none;
}

.bp-messages-wrap .threads-list .thread .bm-info .name .bm-thread-icon svg {
	color: var(--global-font-color);
}

/* tabs */
.bp-messages-wrap .bm-side-tabs {
	background: var(--color-theme-white-box);
	height: 50px;
	line-height: 50px;
	height: auto;
	line-height: normal;
	padding: 1em;
	width: auto;
	-webkit-box-pack: justify;
	-ms-flex-pack: justify;
	justify-content: space-between;
	border-radius: var(--border-radius);
	margin-bottom: 0 !important;
	border: none;
}

.bp-messages-wrap .bm-side-tabs>div {
	border: none;
	padding: 1em;
	border-radius: var(--border-radius);
	width: 100%;
}

.bp-messages-wrap .bm-side-tabs>div.active {
	background: var(--color-theme-primary);
	color: var(--color-theme-white);
	border: none;
}

.bp-messages-wrap .bm-side-tabs.bm-side-tabs-border-bottom>div,
.bp-messages-wrap .bm-side-tabs.bm-side-tabs-border-bottom>div.active {
	border: none;
}

.bp-messages-wrap .bm-side-tabs>div>svg {
	font-size: 1.3em;
	margin-right: .2em;
}

.bp-messages-wrap .bpbm-search-in-list>input {
	border: none !important;
	background: var(--color-theme-white-box);
	color: var(--global-font-color) !important;
	font-size: var(--global-font-size);
}

.bp-messages-wrap .bpbm-search-in-list>input::placeholder {
	color: var(--global-font-color);
	font-weight: var(--font-weight-regular);
}

.bp-messages-user-list,
.bp-messages-group-list {
	background: transparent;
}

.bp-messages-wrap .empty {
	background: var(--color-theme-white-box);
	padding: 1em;
	border-radius: var(--border-radius);
	color: var(--global-font-color);
	margin: 1em 0;
}

.bp-messages-wrap .empty .empty {
	padding: 0;
	margin: 0;
}

.bp-messages-wrap .threads-list.empty {
	padding-bottom: 1em;
	display: flex;
	flex-direction: column;
	justify-content: center;
	height: 100%;
}

.bp-messages-wrap .bpbm-empty-icon {
	color: var(--global-font-title);
}

.bp-messages-wrap .bpbm-empty-text {
	color: var(--global-font-color);
	margin: 1em 0;
}

/* chat box */
.bp-messages-wrap .threads-list .thread {
	border-radius: var(--border-radius);
	padding: 1em;
	position: relative;
	cursor: pointer !important;
}

.bp-messages-wrap:not(.bp-messages-mobile) .threads-list .thread:hover>* {
	background: var(--color-theme-white-box) !important;
}

.bp-messages-wrap .threads-list .thread>* {
	background: var(--color-theme-white-box) !important;
	border-top: 1em solid var(--global-body-bgcolor);
	vertical-align: middle;
	border-bottom: none;
}

.bp-messages-wrap.bp-messages-mobile .threads-list .thread .time .time-wrapper {
	font-size: var(--font-size-small);
}

.bp-messages-wrap .threads-list .thread:last-child>* {
	border-top: 1em solid var(--global-body-bgcolor);
}

.bp-messages-wrap .bp-messages-side-threads .threads-list .thread .pic {
	height: 50px;
	width: 50px;
	vertical-align: middle;
}

.bp-messages-wrap .bp-messages-side-threads .threads-list .thread .pic.group-thread::after {
	position: absolute;
	content: "\e95b";
	font-weight: 200;
	font-family: iconly !important;
	background: var(--global-body-bgcolor);
	color: var(--global-font-color);
	height: 1.5em;
	width: 1.5em;
	line-height: 1.5em;
	font-size: .8em;
	text-align: center;
	border: .063em solid var(--border-color-light);
	border-radius: var(--border-radius-full);
	top: 1em;
	left: auto;
	right: 1em;
}

.bp-messages-wrap .bp-messages-side-threads .threads-list .thread .pic img {
	height: 50px;
	width: 50px;
	border-radius: var(--border-radius-full) !important;
}

.bbpm-avatar::before {
	outline: .125em solid var(--color-theme-white-box);
	height: .375em;
	width: .375em;
	min-width: .375em;
	border-radius: 50%;
	border: none;
	top: auto;
	bottom: .3em;
	right: .1em;
	left: auto;
}

.bbpm-avatar.online::before {
	color: var(--color-theme-online);
}

.bp-messages-wrap .threads-list .thread .info h4,
.bp-messages-wrap .threads-list .thread .info .name {
	font-size: var(--font-size-normal);
	line-height: 16px;
	color: var(--global-font-title);
	font-weight: var(--font-weight-medium);
}

.bp-messages-wrap .threads-list .thread .info .name+h4 {
	color: var(--global-font-color);
}

.bp-messages-wrap .threads-list .thread .info .last-message {
	overflow: hidden;
	text-overflow: ellipsis;
	display: -webkit-box;
	-webkit-box-orient: vertical;
	-webkit-line-clamp: 1;
	margin-top: .5em;
	color: var(--global-font-color);
	display: flex;
}

.bp-messages-wrap .bp-messages-side-threads .threads-list .thread .time .time-wrapper,
.bp-messages-wrap .threads-list .thread .time .time-wrapper {
	font-weight: var(--font-weight-semi-bold);
	font-size: var(--font-size-small);
	top: 0;
	vertical-align: bottom;
}

.bp-messages-wrap .threads-list .thread .actions .delete {
	color: var(--color-theme-danger);
}

.bp-messages-wrap .threads-list .thread .time {
	vertical-align: top;
	font-size: inherit;
}

.bp-messages-wrap .threads-list .thread .actions {
	vertical-align: top;
	font-size: inherit;
	padding-top: 1.2em;
}
#buddypress button.recycle {
    background: var(--color-theme-primary-light);
	border-color: var(--color-theme-primary-light);
    color: var(--color-theme-primary);
}
#buddypress button.delete {
    background: var(--color-theme-danger-light);
	border-color: var(--color-theme-danger-light);
    color: var(--color-theme-danger);
}
.bp-messages-wrap .bp-messages-side-threads .threads-list .thread .pic.group,
.bp-messages-wrap .threads-list .thread .pic.group {
	height: 50px;
	width: 50px;
	min-width: 50px;
	position: relative;
}

.bp-messages-wrap .bp-messages-side-threads .threads-list .thread .pic.group>span,
.bp-messages-wrap .threads-list .thread .pic.group>span {
	display: none;
}

.bp-messages-wrap .bp-messages-side-threads .threads-list .thread .pic.group>* .avatar,
.bp-messages-wrap .threads-list .thread .pic.group>* .avatar,
.bp-messages-wrap .threads-list .thread .pic.group>* {
	width: 35px !important;
	height: 35px !important;
	min-width: 35px !important;
}

.bp-messages-wrap .bp-messages-side-threads .threads-list .thread .pic.group>span:nth-last-child(2),
.bp-messages-wrap .threads-list .thread .pic.group>span:nth-last-child(2) {
	display: block;
	margin-left: auto !important;
	margin-right: 0 !important;
}

.bp-messages-wrap .bp-messages-side-threads .threads-list .thread .pic.group>span:last-child,
.bp-messages-wrap .threads-list .thread .pic.group>span:last-child {
	display: block;
	margin-left: 0 !important;
	margin-right: auto !important;
	margin-top: -20px !important;
}

.bp-messages-wrap .bp-messages-side-threads .threads-list .thread .pic.group>* {
	width: 35px !important;
	height: 35px !important;
	min-width: 35px !important;
}

.bp-messages-wrap .threads-list .thread .unread-count {
	background: var(--color-theme-orange);
	color: var(--color-theme-white);
	border-radius: 100%;
	padding: 0;
	height: 1.7em;
	width: 1.7em;
	min-width: 1.7em;
	line-height: 1.7em;
	font-size: .6em;
}

.bp-messages-wrap .threads-list .thread .time .bpbm-counter-row {
	justify-content: end;
}

.bm-name-verified {
	color: var(--color-theme-primary);
	line-height: 0;
}

/* tab chat box */
.bp-messages-user-list .user,
.bp-messages-group-list .group {
	background: var(--color-theme-white-box) !important;
	border-radius: var(--border-radius);
	padding: 1em;
	position: relative;
	margin-top: 1em;
}

.bp-messages-wrap .threads-list .thread .bm-info .name,
.bp-messages-wrap .threads-list .thread .bm-info h4 {
	font-size: var(--font-size-normal);
	line-height: 16px;
	color: var(--global-font-title);
	font-weight: var(--font-weight-medium);
	margin: 0;
}

.bp-messages-wrap .threads-list .thread .bm-info .last-message {
	font-size: var(--font-size-small);
	color: var(--global-font-color);
	margin-top: .5em;
}

.bp-messages-wrap .threads-list .thread .bm-info .last-message .bm-last-message-content {
	overflow: hidden;
	text-overflow: ellipsis;
	display: -webkit-box;
	-webkit-box-orient: vertical;
	-webkit-line-clamp: 1;
}

.bp-messages-user-list .user {
	font-size: 14px;
	color: var(--global-font-title);
	font-weight: var(--font-weight-medium);
}

.bp-messages-group-list .group {
	font-size: 1.05em;
}

.bp-messages-user-list .user .user {
	margin-top: 0;
}

.bp-messages-group-list .group:hover,
.bp-messages-user-list .user:not(.not-clickable):hover {
	background: var(--color-theme-white-box) !important;
}

.bp-messages-user-list .user>* {
	padding-top: 0;
	padding-bottom: 0;
}

.bp-messages-user-list .user .pic,
.bp-messages-group-list .group .pic {
	height: 35px;
	width: 35px;
	min-width: 35px;
}

.bp-messages-user-list .user .pic img,
.bp-messages-group-list .group .pic img {
	border-radius: var(--border-radius-full) !important;
	height: 35px;
	width: 35px;
	min-width: 35px;
}

.bp-messages-user-list .user .actions>a>svg,
.bp-messages-user-list .user .actions>span>svg {
	font-size: 1.1em;
	color: var(--global-font-color);
}

.bp-messages-user-list .user .actions>a>svg:hover,
.bp-messages-user-list .user .actions>span>svg:hover {
	color: var(--color-theme-primary);
}

.bp-messages-group-list .group .actions>a>svg,
.bp-messages-group-list .group .actions>span>svg {
	font-size: 1.3em;
}

.bp-messages-wrap .bpbm-search-in-list {
	border-top: 1em solid var(--global-body-bgcolor);
}

.bp-messages-wrap .bpbm-search-in-list>input {
	border: none !important;
}

.bp-messages-group-list .group .name {
	font-size: var(--font-size-normal);
	line-height: 16px;
	color: var(--global-font-title);
	font-weight: var(--font-weight-medium);
}

/* footer */
.bp-messages-wrap .chat-footer {
	flex-direction: row-reverse;
	justify-content: space-between;
	background: var(--color-theme-white-box);
	border: none;
	border-radius: var(--border-radius);
	margin-top: 1em;
	height: 50px;
	line-height: 50px;
}

.bp-messages-wrap .chat-footer .bpbm-user-me {
	margin-left: initial;
	padding-left: 1em;
}

.bp-messages-wrap .chat-footer .bpbm-user-me.bpbm-open,
.bp-messages-wrap .chat-footer .bpbm-user-me:hover {
	background: transparent;
}

.bp-messages-wrap .chat-footer>a>svg {
	stroke: var(--color-theme-primary);
}

.bp-messages-wrap .chat-footer .bpbm-user-me .bpbm-user-me-popup {
	box-shadow: var(--global-box-shadow);
	border-color: var(--border-color-light);
	border-radius: var(--border-radius);
	left: 0;
	right: auto;
}

.bp-messages-wrap .chat-header>a,
.bp-messages-wrap .chat-header>span {
	color: var(--color-theme-primary) !important;
	;
}

.bp-messages-wrap #bm-new-thread-title {
	color: var(--global-font-title) !important;
}

.bp-messages-wrap a {
	color: var(--global-font-color) !important;
}

.bp-messages-wrap a:not(.bm-no-link-style) {
	color: var(--global-font-color) !important;
}

.bp-better-messages-list .tabs>div[data-tab=bpbm-close] svg {
	margin: 0;
	padding: 0;
}

.bp-messages-wrap .chat-footer .settings {
	height: 50px;
	line-height: 50px;
}

.bp-messages-wrap .chat-footer .bpbm-user-me .bpbm-user-me-popup .bpbm-user-me-popup-list {
	background: var(--global-body-bgcolor);
	box-shadow: var(--global-box-shadow);
}

/* new chat */
.bm_user_selector .bm_user_selector__menu .bm_user_selector__menu-list .bm_user_selector__option {
	padding: .8em 1em;
}

.bm_user_selector,
.bp-messages-wrap .bm-alert.bm-info,
.bm_user_selector .bm_user_selector__control,
.bm_user_selector .bm_user_selector__menu {
	color: var(--global-font-color);
	background: var(--color-theme-white-box) !important;
	border-color: var(--border-color-light) !important;
}

.bm_user_selector .bm_user_selector__multi-value {
	background: var(--global-body-bgcolor);
	color: var(--global-font-color);
}

.bm_user_selector .bm_user_selector__menu .bm_user_selector__menu-list .bm_user_selector__option.bm_user_selector__option_focused {
	background: var(--global-body-bgcolor);
	color: var(--global-font-color);
}

.bm_user_selector input,
.bm_user_selector .bm_user_selector__placeholder {
	color: var(--global-font-color);
}

.css-1gtu0rj-indicatorContainer {
	color: var(--global-font-title) !important;
}

/* chat right panel */

.bp-messages-column {
	width: calc(100% - 360px);
}

.bp-messages-wrap .thread-not-selected.empty,
.bp-messages-wrap .thread-not-selected.empty .empty {
	background: transparent;
	padding: 0;
	height: 100%;
}

.bp-messages-wrap .thread-not-selected .empty .bpbm-empty-icon {
	margin: 0 auto;
	line-height: normal;
}

.bp-messages-wrap .thread-not-selected.empty .empty {
	margin: auto 0;
	display: flex;
	flex-direction: column;
	justify-content: center;
}

.bp-messages-wrap.bp-messages-mobile .chat-header .back,
.bp-messages-wrap.bp-messages-mobile .chat-header .bpbm-maximize,
.bp-messages-wrap.bp-messages-mobile .chat-header .bpbm-minimize,
.bp-messages-wrap.bp-messages-mobile .chat-header .bpbm-search a,
.bp-messages-wrap.bp-messages-mobile .chat-header .expandingButtons,
.bp-messages-wrap.bp-messages-mobile .chat-header .mass-message,
.bp-messages-wrap.bp-messages-mobile .chat-header .starred-messages,
.bp-messages-wrap .chat-header>a {
	margin: 0;
}

.bpbm-empty-link a {
	color: var(--color-theme-white) !important;
	line-height: var(--font-line-height-body);
	background-color: var(--color-theme-primary);
	border-radius: var(--border-radius);
	font-size: var(--font-size-normal);
	font-family: var(--highlight-font-family);
	letter-spacing: var(--letter-spacing-one);
	font-weight: var(--font-weight-semi-bold);
	padding: .813em 2em;
	display: inline-block;
	vertical-align: top;
	text-transform: uppercase;
	transition: all .45s ease-in-out;
	-moz-transition: all .45s ease-in-out;
	-ms-transition: all .45s ease-in-out;
	-o-transition: all .45s ease-in-out;
	-webkit-transition: all .45s ease-in-out;
}

.bp-messages-wrap .bpbm-empty-link a:not(.bm-no-link-style) {
	color: var(--color-theme-white) !important;
}

.bp-messages-wrap .chat-header {
	height: 3.7em;
	min-height: 3.7em;
	line-height: normal;
	padding: 0 .5em;
	background: var(--color-theme-white-box);
	border: none;
	border-radius: var(--border-radius);
	margin-bottom: 1em;
}

.bp-messages-wrap .chat-header>a:first-child {
	margin-left: 0;
}

.bp-messages-wrap .chat-header .thread-info .avatar {
	height: 2.5em;
	width: 2.5em;
	min-width: 2.5em;
	border-radius: var(--border-radius-full);
}

.bp-messages-wrap .chat-header .thread-info .thread-info-data .name a {
	color: var(--global-font-title) !important;
}

.bp-messages-wrap .chat-header .thread-info .avatar-group>span {
	display: none;
}

.bp-messages-wrap .chat-header .thread-info .avatar-group>span:last-child {
	display: block;
	margin-left: 0 !important;
	margin-right: auto !important;
	margin-top: -20px !important;
}

.bp-messages-wrap .chat-header .thread-info .thread-info-data {
	color: var(--global-font-title);
	white-space: nowrap;
	text-overflow: ellipsis;
	overflow: hidden;
	-webkit-line-clamp: 1;
	line-height: normal;
	font-size: 1.15em;
}

.bp-messages-wrap .chat-header .thread-info .thread-info-data .name strong {
	font-weight: var(--font-weight-regular);
	font-size: 1em;
	color: var(--global-font-title);
}

.bp-messages-wrap .chat-header .thread-info .thread-info-data .sub-name {
	font-size: 12px;
	color: var(--global-font-color);
	margin-top: .3em;
}

.bp-messages-wrap .chat-header .thread-info .avatar-group {
	width: 50px;
	height: 50px;
	display: block;
}

.bp-messages-wrap .chat-header .thread-info .avatar-group .avatar {
	height: 35px;
	width: 35px;
	min-width: 35px;
}

.bp-messages-wrap .chat-header .thread-info .avatar-group>span:nth-last-child(2) {
	display: block;
	margin-left: auto !important;
	margin-right: 0 !important;
}

.bp-messages-wrap .chat-header .thread-actions>a,
.bp-messages-wrap .chat-header .thread-actions>span,
.bp-messages-wrap .chat-header .thread-actions>div.expandingButtons {
	color: var(--color-theme-primary) !important;
}

.bp-messages-wrap .bpbm-chat-content .bpbm-chat-main {
	background: var(--color-theme-white-box);
	border-radius: var(--border-radius);
}

.bp-messages-wrap img.avatar,
.bp-messages-wrap .threads-list .thread .pic img,
.socialv .bp-messages-wrap img.avatar {
	border-radius: var(--border-radius-full) !important;
}

.bp-messages-wrap .bm-messages-list .bm-list .bm-messages-stack.bm-right-side .bm-content .bm-messages-listing .bm-message .bm-message-content:not(.bm-hide-background) {
	background: var(--color-theme-primary) !important;
	border-radius: var(--border-radius) !important;
}

.bp-messages-wrap .bm-messages-list .bm-list .bm-messages-stack.bm-right-side .bm-content .bm-messages-listing .bm-message .bm-replied-message,
.bp-messages-wrap .bm-messages-list .bm-list .bm-messages-stack.bm-right-side .bm-content .bm-messages-listing .bm-message .bpbm-replied-message {
	background: var(--color-theme-primary-dark) !important;
	border-color: var(--border-color-light);
}

.bp-messages-wrap .bm-messages-list .bm-list .bm-messages-stack.bm-left-side .bm-content .bm-messages-listing .bm-message .bm-message-content:not(.bm-hide-background) {
	background: var(--global-body-bgcolor) !important;
	color: var(--global-font-color) !important;
	border-radius: var(--border-radius) !important;
}

.bp-messages-wrap .bm-messages-list .bm-list .bm-messages-stack.bm-left-side .bm-content .bm-messages-listing .bm-message .bm-replied-message,
.bp-messages-wrap .bm-messages-list .bm-list .bm-messages-stack.bm-left-side .bm-content .bm-messages-listing .bm-message .bpbm-replied-message {
	background: var(--color-theme-white-box) !important;
	border-color: var(--border-color-light) !important;
	border-radius: var(--border-radius) !important;
}

.bp-messages-wrap .bm-messages-list .bm-list .bm-messages-stack .bm-content .bm-info .name {
	display: none;
}

.bp-messages-wrap .bm-messages-list .bm-list .bm-messages-stack.bm-left-side .bm-content .bm-messages-listing .bm-message .bm-message-content:not(.bm-hide-background) * {
	color: var(--global-font-color) !important;
}

.bp-messages-wrap .bm-messages-list .bm-list .bm-messages-stack.bm-left-side .bm-content .bm-messages-listing .bm-message .bm-message-content:not(.bm-hide-background) .bm-message-info .bm-time {
	font-weight: var(--font-weight-semi-bold);
	font-size: 11px;
}

.bp-messages-wrap .bm-reply,
.bp-messages-wrap .bm-reply>* {
	background: var(--color-theme-white-box);
	border-color: var(--border-color-light);
}

.bp-messages-wrap:not(.bp-better-messages-mini) .bm-reply .bm-send-message {
	background: var(--color-theme-primary);
	border-radius: var(--border-radius);
	min-width: 40px;
	width: 40px;
	height: 40px;
	margin: auto .5em;
}

.bp-messages-wrap:not(.bp-better-messages-mini) .bm-reply .bm-attachment,
.bp-messages-wrap:not(.bp-better-messages-mini) .bm-reply .bm-emojies {
	min-width: 50px;
	width: 50px;
	height: 40px;
	margin: auto 0;
}

.bp-messages-wrap .bm-reply.bm-attachments .bm-editor {
	cursor: text;
}

.bp-messages-wrap .bm-reply .bm-editor .bm-editor-content {
	background: var(--color-theme-white-box) !important;
	color: var(--global-font-color) !important;
}

.bp-messages-wrap .bm-reply .bm-editor .bm-editor-content:empty::before,
.bp-messages-wrap .chat-header .bpbm-search form input[type=text]::placeholder {
	color: var(--global-font-color) !important;
	font-size: var(--font-size-normal) !important;
	font-weight: var(--font-weight-medium) !important;
}

.bp-messages-wrap .bm-reply {
	background: var(--color-theme-white-box);
	border-color: var(--border-color-light);
}

.bp-messages-wrap .bm-reply .bm-attachment.bm-attachment-progress {
	color: var(--global-font-title);
}

.bp-messages-wrap:not(.bp-better-messages-mini) .bm-reply .bm-send-message svg {
	fill: var(--color-theme-white);
	width: .8em;
	left: 0;
	right: 0;
	top: 45%;
}

.bp-messages-wrap:not(.bp-better-messages-mini) .bm-reply .bm-attachment svg,
.bp-messages-wrap:not(.bp-better-messages-mini) .bm-reply .bm-emojies svg {
	color: var(--global-font-color);
	bottom: 8px;
}

.bp-messages-wrap .bm-date-stack {
	position: relative;
}

.bp-messages-wrap .bm-messages-list .bm-list .bm-sticky-date {
	background: var(--color-theme-primary-light);
	color: var(--color-theme-primary);
	padding: .5em 1em;
	border-radius: var(--border-radius) !important;
	font-weight: var(--font-weight-semi-bold);
	margin: 1em 0;
}

.bp-messages-wrap .bm-messages-list .bm-list .bm-sticky-date::before {
	position: absolute;
	content: "";
	border-top: .063em solid var(--color-theme-primary-light);
	top: 23px;
	left: 0;
	right: 0;
	z-index: -1;
}

.bp-messages-wrap .bm-messages-list:not(.not-interacted) .bm-list.bm-just-scrolled .bm-sticky-date {
	z-index: inherit;
	position: static;
}

.bp-messages-wrap .bm-messages-list .bm-list .bm-messages-stack .bm-content .bm-messages-listing .bm-message .message-controls>span.favorite {
	color: var(--color-theme-ratting) !important;
}

.bp-messages-wrap .bm-messages-list .bm-list .bm-messages-stack .bm-content .bm-messages-listing .bm-message .message-controls>span {
	color: var(--global-font-title);
}

.contexify {
	background: var(--color-theme-white-box);
	color: var(--global-font-color);
	border: .0625em solid var(--border-color-light);
	border-radius: var(--border-radius);
	box-shadow: var(--global-box-shadow);
	margin: .3em;
}

.contexify_item:not(.contexify_item-disabled)>.contexify_itemContent,
.contexify_item:not(.contexify_item-disabled)>.contexify_itemContent {
	color: var(--global-font-color);
}

.contexify_item:not(.contexify_item-disabled):hover>.contexify_itemContent,
.contexify_item:not(.contexify_item-disabled):focus>.contexify_itemContent {
	color: var(--color-theme-primary);
	background: transparent;
}

.bp-messages-wrap .bm-messages-list .bpbm-scroll-down {
	background: var(--color-theme-primary);
	border-color: var(--border-color-light);
	color: var(--color-theme-white);
}

.bp-messages-wrap .empty-thread {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	margin: auto 0;
}

/* bulk message */
.bp-messages-wrap .bulk-message form>div label.bm-bold {
	color: var(--global-font-title);
	margin-bottom: 1em;
	font-size: 1em !important;
	line-height: 28px;
}

.bp-messages-wrap .bulk-message .box {
	background: var(--color-theme-white-box);
	border-color: var(--border-color-light);
	padding: 1em;
	border-color: var(--border-color-light);
	border-radius: var(--border-radius);
}

.bp-messages-wrap .bulk-message ul p {
	margin: 0 0 0 22px;
	font-size: 11px;
}

.bp-messages-wrap button {
	background-color: var(--color-theme-primary) !important;
	color: var(--color-theme-white) !important;
	padding: .813em 2em !important;
	border-radius: var(--border-radius) !important;
}

.bp-messages-wrap .bulk-message .reports h3 {
	margin-bottom: 1em;
}

table.reports-list,
table.reports-list tr,
table.reports-list td {
	border: none;
}

.bp-messages-wrap .bulk-message .reports .reports-list td,
.bp-messages-wrap .bulk-message .reports .reports-list th {
	padding: 1em;
	vertical-align: middle;
	border: none;
	font-size: 1.2em;
	color: var(--global-font-title);
}

table.reports-list thead tr {
	background: transparent;
}

table.reports-list tr {
	background: var(--color-theme-white-box);
	border-bottom: .625em solid var(--global-body-bgcolor);
}

.bp-messages-wrap .bulk-message .reports .reports-list tbody td {
	color: var(--global-font-color);
}

.bp-messages-wrap .bulk-message .reports .reports-list .delete {
	color: var(--color-theme-danger);
}

.bpbm-checkbox:checked+label::before,
.bpbm-radio:checked+label::before {
	background-color: var(--color-theme-primary);
	border-color: var(--color-theme-primary);
}

.bp-messages-wrap .bulk-message form>div:nth-child(3) {
	background: var(--color-theme-white-box);
	padding: 1em;
	border-radius: var(--border-radius);
}

.bp-messages-wrap .bulk-message .progress {
	background: var(--color-theme-white-box);
	border-color: var(--color-theme-white-box);
	color: var(--global-font-title);
}

.bp-messages-wrap .bulk-message .progress-value {
	background-color: var(--color-theme-success);
}

.bp-messages-wrap .bulk-message form>div label {
	font-size: 14px !important;
}

.bp-messages-wrap .bulk-message form>div .bm-editor {
	border-color: var(--border-color-light) !important;
}

/* favrite msg */
.bp-messages-wrap .bm-messages-list {
	background: var(--color-theme-white-box);
	border-radius: var(--border-radius);
}

/* setting  */
.bp-messages-wrap .threads-list .thread .info .name .bpbm-thread-muted {
	color: var(--global-font-title);
}

.bm-modal-window.bm-modal-add-participant .bm-modal-window-inner {
	background-color: var(--color-theme-white-box);
	border-radius: var(--border-radius);
	box-shadow: var(--global-box-shadow);
}

.bm-modal-window::after {
	position: absolute;
	content: "";
	background: var(--global-body-bgcolor);
	top: 0;
	bottom: 0;
	left: 0;
	right: 0;
	z-index: -1;
	opacity: .5;
}

.bm-modal-window .bm-modal-window-header {
	padding: 1em;
}

.bm-modal-window .bm-modal-window-header h3,
.bm-modal-window .bm-modal-window-header .bm-modal-window-close {
	color: var(--global-font-title);
}

.bm-modal-window .bm-modal-window-content {
	padding: 0 1em 1em;
}

.bm-modal-window.bm-modal-add-participant .bm-modal-window-inner .bm-action-buttons {
	padding: 1em;
}

.bm_user_selector .bm_user_selector__menu-loading,
.bm_user_selector .bm_user_selector__menu-notice--no-options {
	color: var(--global-font-color);
}

.bm-modal-window.bm-modal-add-participant .bm-modal-window-inner .bm-action-buttons button.bm-button {
	background-color: var(--color-theme-success) !important;
	color: var(--color-theme-white) !important;
	padding: 1em 1.5em !important;
	border-radius: var(--border-radius) !important;
}

.bm_user_selector .bm_user_selector__control input.bm_user_selector__input[type=text] {
	color: var(--global-font-color);
}

.css-ackcql,
.bm_user_selector .bm_user_selector__control input.bm_user_selector__input[type=text] {
	color: var(--global-font-color) !important;
}

.bp-messages-wrap .bm-thread-settings-container {
	background: var(--color-theme-white-box);
}

.bp-messages-wrap .bpbm-thread-options .bpbm-thread-option-toggle input[type=text] {
	border-color: var(--border-color-light) !important;
}

.bp-messages-wrap label {
	color: var(--global-font-title);
}

.bp-messages-wrap .bpbm-thread-options .bpbm-thread-option-description {
	color: var(--global-font-color);
}

.bp-messages-wrap .participants-panel .bp-messages-user-list .user {
	background: var(--global-body-bgcolor) !important;
	padding: .3em;
}

.bp-messages-user-list .user .actions>a,
.bp-messages-user-list .user .actions>span {
	color: var(--global-font-title);
}

.bp-messages-user-list .user .actions>span.remove-from-thread {
	color: var(--color-theme-danger);
}

.bp-messages-user-list .user .actions>span.bpbm-block-user>svg,
.bp-messages-user-list .user .actions>span.remove-from-thread>svg {
	font-size: 1em;
}

.bp-messages-wrap .participants-panel h4,
.bp-messages-wrap .bm-thread-settings h4 {
	margin-bottom: 1em !important;
	padding: 0 !important;
}

.bp-messages-wrap .participants-panel {
	background: var(--color-theme-white-box);
	padding: 1em;
}

.bp-messages-wrap .bm-thread-settings {
	background: var(--color-theme-white-box);
	padding: 1em 1em 0;
}

.bp-messages-wrap .bpbm-thread-options {
	padding: 0;
}

.bp-messages-wrap .bpbm-thread-options .bpbm-thread-option-toggle {
	margin-bottom: .5em;
}

.bp-messages-user-list .user .name {
	font-size: var(--global-font-title);
}

.bp-messages-wrap .participants-panel .bp-messages-user-list .user .user {
	margin: 0;
	color: var(--global-font-title) !important;
	font-size: inherit;
}

.bp-messages-wrap .bpbm-user-options {
	background: var(--color-theme-white-box);
	border-radius: var(--border-radius);
}

.bp-messages-wrap .bpbm-user-option-title {
	margin-bottom: 1em !important;
	font-size: var(--font-size-h4);
}

.bp-messages-wrap .bpbm-user-options .bpbm-user-blacklist table tr {
	background: var(--global-body-bgcolor);
	border-bottom: .625em solid var(--color-theme-white-box);
}

.bp-messages-wrap .bpbm-user-options .bpbm-user-blacklist table td {
	padding: 1em;
	vertical-align: middle;
	border: none;
	font-size: 1em;
}

.bp-messages-wrap .bpbm-user-options .bpbm-user-blacklist table td:last-child {
	text-align: right;
	width: 10em;
}

.bp-messages-wrap .bpbm-user-blacklist a.bpbm-unblock-user {
	color: var(--color-theme-white) !important;
	line-height: var(--font-line-height-body);
	background-color: var(--color-theme-primary);
	border-radius: var(--border-radius);
	padding: .3em .5em;
	font-size: var(--font-size-small);
	font-family: var(--highlight-font-family);
	letter-spacing: var(--letter-spacing-one);
	font-weight: var(--font-weight-semi-bold);
}

.bp-messages-wrap .bpbm-user-blacklist a {
	color: var(--global-font-title) !important;
}

.bp-messages-wrap .bpbm-user-options .bpbm-user-option-description {
	color: var(--global-font-color);
}

.bp-messages-wrap .bpbm-user-options .bpbm-user-blacklist {
	margin: 2em 0 0;
}

.bm-toasts.Toastify__toast--info {
	background: var(--color-theme-black);
	color: var(--color-theme-white);
}

.Toastify__toast-theme--colored.Toastify__toast--success {
	background: var(--color-theme-success);
	color: var(--color-theme-white);
}

/* message notificatio */
.header-messages .bp-messages-wrap .threads-list .thread>* {
	border: none;
}

.header-messages .bp-messages-wrap .threads-list .thread .pic {
	padding-left: 2em;
}

.header-messages .bp-messages-wrap .threads-list .thread .actions {
	padding-right: 1.5em;
}

.header-messages .bp-messages-wrap:not(.bp-messages-mobile) .threads-list .thread:hover>* {
	background: var(--global-body-bgcolor) !important;
}

.bp-messages-wrap .threads-list .thread .actions .bm-more-actions {
	color: var(--global-font-title);
}

.header-messages .bp-messages-wrap .threads-list .thread .time .bpbm-counter-row {
	margin: 0;
}

.header-messages .bp-messages-wrap .threads-list .thread .bm-info .name {
	margin-bottom: .2em;
	margin-top: .3em;
}

.bp-messages-wrap .threads-list .thread .bm-info .name+h4 {
	margin-bottom: .1em;
	color: var(--global-font-color);
}

.header-messages .bp-messages-wrap .threads-list .thread .pic.group-thread::after {
	position: absolute;
	content: "\e95b";
	font-weight: 200;
	font-family: iconly !important;
	background: var(--global-body-bgcolor);
	color: var(--global-font-color);
	height: 1.5em;
	width: 1.5em;
	line-height: 1.5em;
	font-size: .8em;
	text-align: center;
	border: .063em solid var(--border-color-light);
	border-radius: var(--border-radius-full);
	top: 1em;
	left: auto;
	right: 1em;
}

.header-messages .bp-messages-wrap .threads-list .thread .bm-info .last-message {
	font-size: var(--font-size-normal);
	display: flex;
}

.header-messages .bp-messages-wrap .threads-list .thread .bm-info .last-message .bm-last-message-avatar {
	margin-top: 2px;
}

.header-messages .bp-messages-wrap .threads-list .thread .bm-info .last-message .bm-last-message-content {
	overflow: hidden;
	text-overflow: ellipsis;
	display: -webkit-box;
	-webkit-box-orient: vertical;
	-webkit-line-clamp: 2;
	line-height: normal
}

.header-messages .bp-messages-wrap .bp-messages-side-threads .threads-list .thread .time .time-wrapper,
.header-messages .bp-messages-wrap .threads-list .thread .time .time-wrapper {
	font-weight: var(--font-weight-regular);
}

.header-messages .bp-messages-wrap .threads-list .thread>* {
	vertical-align: top;
}

.header-messages .bp-messages-wrap .threads-list .thread .actions .delete {
	display: none;
}

.header-messages .bp-messages-wrap .threads-list .thread .pic.group>span {
	display: none;
}

.header-messages .bp-messages-wrap .threads-list .thread .pic.group {
	height: 50px;
	width: 50px;
	min-width: 50px;
	position: relative;
}

.bp-messages-wrap.bm-search-popup>.bm-search-results .threads-list .thread .pic img {
	width: 50px;
	height: 50px;
	min-width: 50px;
}

.bp-messages-wrap.bm-search-popup>.bm-search-results .threads-list .thread .pic {
	width: 50px;
	height: 50px;
	min-width: 50px;
}

.bp-messages-wrap.bm-search-popup {
	background: var(--global-body-bgcolor);
	padding: 1em;
	max-width: 100%;
}

.header-messages .bp-messages-wrap .threads-list .thread .pic.group>span:last-child {
	display: block;
	margin-left: 0 !important;
	margin-right: auto !important;
	margin-top: -20px !important;
}

.header-messages .bp-messages-wrap .threads-list .thread .pic.group>span:nth-last-child(2) {
	display: block;
	margin-left: auto !important;
	margin-right: 0 !important;
}

.header-messages .bp-messages-wrap .threads-list .thread .pic.group>*,
.header-messages .bp-messages-wrap .threads-list .thread .pic.group>* .avatar {
	height: 35px !important;
	width: 35px !important;
}

.header-messages .bp-messages-wrap .threads-list .thread .info .name,
.header-messages .bp-messages-wrap .threads-list .thread .info h4 {
	margin-bottom: 0;
	font-size: 1em;
}

.header-messages .bp-messages-wrap .threads-list .thread .info .last-message .bm-last-message-avatar {
	margin-top: 4px;
}

.header-messages .bp-messages-wrap .threads-list .thread .info .last-message {
	font-size: var(--font-size-normal);
	margin-top: .375em;
	color: var(--global-font-color);
	display: flex;
}

.header-messages .bp-messages-wrap .threads-list .thread .info .last-message .bm-last-message-content {
	overflow: hidden;
	text-overflow: ellipsis;
	display: -webkit-box;
	-webkit-box-orient: vertical;
	-webkit-line-clamp: 2;
	line-height: normal;
}

.header-messages .bp-messages-wrap .threads-list .thread .time {
	color: var(--global-font-color);
	line-height: 1.7em;
	font-weight: var(--font-weight-regular);
	font-family: var(--highlight-font-family);
}

.header-messages .bp-messages-wrap.bm-threads-list {
	height: auto !important;
}

.header-messages .bp-messages-wrap .threads-list.empty {
	display: inherit;
	height: auto;
	margin: 0;
}

.header-messages .bp-messages-wrap .threads-list .empty .bpbm-empty-message {
	margin: 0;
	text-align: left;
	padding: 0 1em;
}

.header-messages .bp-messages-wrap .threads-list.empty .bpbm-empty-icon {
	display: none;
}

/* loader */
.bm-loading {
	padding: 1em 0;
}

.bm-wait-abit>div,
.bm-loading .bm-loading-text {
	display: none;
}

.bm-loading .bm-loading-icon {
	font-size: inherit;
}

.bp-messages-wrap .bm-wait-abit {
	position: relative;
	width: 1.563em;
	height: 1.563em;
	margin: 0 auto;
	border-radius: 50%;
	background: linear-gradient(45deg, transparent, transparent 40%, var(--color-theme-primary));
	animation: animate 2s linear infinite;
}

.bm-wait-abit:before {
	content: "";
	position: absolute;
	top: .125em;
	left: .125em;
	right: .125em;
	bottom: .125em;
	background: var(--global-body-bgcolor);
	border-radius: 50%;
	z-index: 999;
}

/* skeleton */
.bp-messages-wrap .bm-animated-background {
	background: var(--color-theme-skeleton);
}

.bp-messages-wrap .bm-placeholder {
	background: var(--color-theme-skeleton);
}

/* sendbox */
.bpbm-preview-message {
	background: var(--color-theme-white-box);
	border-color: var(--border-color-light);
}

.bpbm-preview-message .bpbm-preview-message-cancel {
	color: var(--global-font-title);
}

.bpbm-preview-message .bpbm-preview-message-content .bpbm-preview-message-name {
	color: var(--global-font-color);
}

.uppy-Dashboard--modal .uppy-Dashboard-inner {
	z-index: 9999;
}

.uppy-Dashboard--modal .uppy-Dashboard-overlay {
	z-index: 9999;
}

.uppy-Dashboard-inner {
	background: var(--color-theme-white-box);
}

.uppy-Dashboard--modal .uppy-Dashboard-inner {
	box-shadow: var(--global-box-shadow);
}

.uppy-Dashboard-close {
	top: 0;
	color: var(--global-font-title) !important;
}

.uppy-Dashboard-Item-action:hover {
	color: var(--color-theme-primary);
}

.uppy-Dashboard-close {
	top: 3px;
}

[dir=ltr] .uppy-Dashboard-close {
	right: 10px;
}

.uppy-Dashboard--modal .uppy-Dashboard-AddFiles {
	border-color: var(--border-color-light);
}

.uppy-Dashboard-AddFiles-title {
	color: var(--global-font-title);
}

.uppy-Dashboard-browse {
	color: var(--color-theme-primary);
}

.uppy-DashboardTab-btn {
	color: var(--global-font-color);
}

.uppy-DashboardTab-btn:hover {
	background-color: var(--global-body-bgcolor);
	color: var(--global-font-color);
}

.uppy-DashboardContent-panel {
	background: var(--color-theme-white-box);
}

.uppy-DashboardContent-bar {
	background: var(--color-theme-white-box);
	border-color: var(--border-color-light);
}

.uppy-Dashboard-inner {
	color: var(--global-font-title);
}

.uppy-Webcam-permissonsIcon svg {
	fill: var(--color-theme-primary);
}

.uppy-Webcam-title {
	color: var(--global-font-title);
}

.uppy-Webcam-permissons p {
	color: var(--global-font-color);
}

.uppy-DashboardContent-back,
.uppy-DashboardContent-save {
	background: transparent;
	color: var(--color-theme-primary);
}

.uppy-DashboardContent-back:focus,
.uppy-DashboardContent-save:focus {
	background: var(--global-body-bgcolor);
}

.uppy-StatusBar:not([aria-hidden=true]).is-waiting {
	border-color: var(--border-color-light);
}

.uppy-StatusBar {
	background: var(--color-theme-white-box);
	color: var(--global-font-color);
}

.uppy-StatusBar.is-waiting .uppy-StatusBar-actionBtn--upload {
	background-color: var(--color-theme-success);
	color: var(--color-theme-white);
}

.uppy-StatusBar.is-waiting .uppy-StatusBar-actionBtn--upload:hover {
	background-color: var(--color-theme-success-dark);
	color: var(--color-theme-white);
}

.uppy-c-btn-primary:focus {
	box-shadow: none;
}

.uppy-StatusBar:before {
	background: var(--border-color-light);
}

[dir=ltr] .uppy-size--md .uppy-DashboardContent-addMore {
	margin-right: 25px;
}

.uppy-Dashboard--modal .uppy-Dashboard-AddFiles,
.uppy-Dashboard-AddFilesPanel {
	background: var(--color-theme-white-box);
}

.uppy-Dashboard-Item-status {
	color: var(--global-font-color);
}

.uppy-Dashboard-FileCard-preview {
	border-color: var(--border-color-light);
}

.uppy-Dashboard-FileCard,
.uppy-Dashboard-FileCard-actions {
	background: var(--color-theme-white-box);
	border-color: var(--border-color-light);
}

.uppy-Dashboard-FileCard-label {
	color: var(--global-font-title);
}

.uppy-Dashboard-Item {
	border-color: var(--border-color-light);
}

.bp-messages-wrap .bm-mentions .bm-mention.bm-mention-active {
	background: var(--color-theme-white-box);
}

[dir=ltr] .uppy-DashboardContent-addMore {
	margin-right: 15px;
}

.uppy-size--md .uppy-DashboardContent-title {
	font-size: var(--font-size-normal);
	color: var(--global-font-color);
	font-weight: 400;
}

.bp-messages-wrap .chat-footer .bpbm-user-me .bpbm-user-me-popup .bpbm-user-me-popup-list .bpbm-user-me-popup-list-item {
	color: var(--global-font-title) !important;
}

.uppy-c-btn-primary {
	background: var(--color-theme-primary);
	color: var(--color-theme-white);
}

.uppy-c-btn-link {
	background: var(--color-theme-danger);
	color: var(--color-theme-white);
}

.uppy-DashboardTab-btn .uppy-DashboardTab-name, .uppy-DashboardTab-btn:hover .uppy-DashboardTab-name {
	color: var(--global-font-color);
  }
/* emoji-picker */

body.bm-messages-light .bm-emoji-picker-container {
	--rgb-background: var(--color-theme-white-box);
	background-color: var(--rgb-background);
	box-shadow: var(--global-box-shadow);
}

body.bm-messages-light .bm-emoji-picker-container {
	--rgb-color: var(--global-font-title);
	--rgb-accent: var(--global-font-color);
	--color-b: var(--global-font-title);
}

body.bm-messages-light .bm-emoji-picker-container {
	--color-border: var(--border-color-light);
	--rgb-input: var(--global-font-color);
}

.category button .background {
	--em-color-border: var(--global-body-bgcolor);
	background-color: var(--em-color-border);
}

#nav[data-position=top]:before,
#preview[data-position=top]:before {
	--em-color-border: var(--border-color-light);
	background: linear-gradient(to bottom, var(--em-color-border), transparent);
}

/* full screen */
.bp-messages-wrap.bp-messages-full-screen {
	background: var(--global-body-bgcolor);
	padding: 1em;
}

body.bp-messages-full-screen .bm-emoji-picker-container em-emoji-picker,
body.bp-messages-mobile .bm-emoji-picker-container em-emoji-picker {
	margin: 0 0 0 auto;
}

/* popup message */
.Toastify__toast-theme--colored.Toastify__toast--default {
	background: var(--color-theme-white-box) !important;
	color: var(--global-font-color) !important;
}

.Toastify__close-button {
	color: var(--global-font-title) !important;
}

.bm-toast-site-message .bm-toast-site-message-container .bm-toast-site-message-info .bm-toast-site-message-title {
	color: var(--global-font-title);
	font-weight: var(--font-weight-regular);
}

.bbpm-avatar img,
.bm-toast-site-message-avatar img {
	border-radius: var(--border-radius-full) !important;
}

.Toastify__toast {
	box-shadow: var(--global-box-shadow);
}


/* mini chat */
body:not(.wp-admin) .bp-better-messages-list {
	z-index: 999;
}

.bp-messages-wrap.bp-better-messages-list {
	background: var(--color-theme-white-box);
	border-color: var(--border-color-light);
	box-shadow: 0 12px 28px 0 rgb(0 0 0 / 20%), 0 2px 4px 0 rgb(0 0 0 / 10%);
	border-radius: var(--border-radius) var(--border-radius) 0 0 !important;
}

[data-mode=dark] .bp-messages-wrap.bp-better-messages-list {
	box-shadow: 0 12px 28px 0 rgb(4 5 10), 0 2px 4px 0 rgb(17 19 24);
}

.bp-better-messages-list .tabs {
	background: var(--color-theme-primary);
	color: var(--color-theme-white);
	border: none;
	line-height: 40px;
	height: 40px;
}

.bp-better-messages-list .tabs>div.active {
	background: var(--color-theme-primary-dark);
	color: var(--color-theme-white);
}

.bp-messages-wrap.bp-better-messages-list .tabs-content {
	padding: 0 1em;
}

.bp-better-messages-list .tabs>div {
	border-radius: var(--border-radius);
}

.bp-better-messages-list .tabs>div>svg {
	padding-left: .3em;
	font-size: 1.5em;
}

.bp-better-messages-list .tabs>div[data-tab=bpbm-close] svg {
	font-size: 2.5em;
}

.bp-better-messages-list .tabs>div.active {
	background: var(--color-theme-primary-dark);
	color: var(--color-theme-white);
	border-radius: 0;
}

.bp-messages-wrap.bp-better-messages-list .bp-messages-group-list .group,
.bp-messages-wrap.bp-better-messages-list .bp-messages-user-list .user,
.bp-messages-wrap.bp-better-messages-list .bp-messages-wrap .threads-list .thread {
	background: var(--global-body-bgcolor) !important;
	padding: .5em;
}

.bp-messages-user-list div.user:not(.not-clickable):hover{
	background: var(--global-body-bgcolor) !important;
}

.bp-messages-wrap.bp-better-messages-list .chat-footer {
	background: var(--global-body-bgcolor);
	padding: .5em 1em;
}

.bp-messages-wrap.bp-better-messages-list .chat-footer .new-message {
	line-height: 3;
}

.bp-messages-wrap.bp-better-messages-list .bpbm-search-in-list {
	padding: 1em 0 0;
}

.bp-messages-wrap.bp-better-messages-list .chat-footer .bpbm-user-me .bpbm-user-me-avatar .avatar {
	height: 30px;
	width: 30px;
	min-width: 30px;
}

.bp-messages-wrap.bp-better-messages-list .bpbm-search-in-list>input {
	background: var(--global-body-bgcolor);
	border-color: var(--border-color-light);
}

.bp-messages-wrap.bp-better-messages-list .bpbm-search-in-list {
	border: none;
}

.bp-messages-wrap .chat-footer .new-message {
	color: var(--color-theme-primary);
}

.bp-messages-wrap.bp-better-messages-list .chat-footer .new-message {
	width: 50px;
	height: auto;
}

/* emoji */

.bm-emoji-picker-container {
	border-radius: var(--border-radius);
	border-color: var(--border-color-light);
}

#root {
	--em-rgb-color: var(--global-font-title);
	color: var(--em-rgb-color);
}

.bp-emojionearea-picker .bp-emojionearea-filters .bp-emojionearea-filter.active,
.bp-emojionearea-picker .bp-emojionearea-filters,
.bp-emojionearea-picker .bp-emojionearea-scroll-area .bp-emojionearea-category-title {
	background: var(--color-theme-white-box);
	color: var(--global-font-color);
}

/* chat responsive */
@media screen and (max-width: 800px) {
	#bp-better-messages-mini-mobile-open {
		background: var(--color-theme-primary);
		border-radius: var(--border-radius-full);
		z-index: 999;
	}
}

body.wp-admin #bp-better-messages-mini-mobile-open {
	z-index: 0;
}

body.bm-mobile-device #bp-better-messages-mini-mobile-open {
	background: var(--color-theme-primary);
	border-radius: var(--border-radius-full);
}

.bp-messages-wrap.bp-messages-mobile .bm-reply .bm-emojies {
	display: none;
}

.bp-messages-wrap.bp-messages-mobile .chat-header .mobileClose {
	width: 25px;
}

.bp-messages-wrap.bp-messages-mobile input,
.bp-messages-wrap.bp-messages-mobile textarea,
.bp-messages-wrap.bp-messages-mobile .new-message form>div input,
.bp-messages-wrap.bp-messages-mobile .new-message form>div .taggle_input[type=text] {
	font-size: var(--font-size-normal);
}

.bp-messages-wrap.bp-messages-mobile .chat-header {
	font-size: inherit;
}

.bp-messages-wrap-main.bp-messages-mobile,
.bp-messages-wrap-group.bp-messages-mobile,
.bp-messages-chat-wrap.bp-messages-mobile {
	background: var(--global-body-bgcolor);
}

#bp-better-messages-mini-mobile-container,
#bp-better-messages-mobile-view-container {
	background: var(--global-body-bgcolor);
}

.bp-messages-wrap.bp-messages-mobile .chat-header .mobileClose {
	color: var(--color-theme-primary);
}

.bp-messages-wrap.mobile-ready:not(.bp-messages-mobile) .bp-messages-mobile-tap {
	color: var(--color-theme-primary);
}

[data-mode="dark"] .bp-messages-wrap.mobile-ready:not(.bp-messages-mobile) .bp-messages-mobile-tap {
	background: rgb(14 14 14 / 62%);
}

.bp-messages-wrap .chat-tabs {
	background: var(--color-theme-white-box);
	padding: .5em;
	border-radius: var(--border-radius);
	margin-top: 1em !important;
	box-sizing: border-box !important;
}

.bp-messages-wrap .chat-tabs>div {
	border: none;
	border-radius: var(--border-radius);
}

.bp-messages-wrap .chat-tabs>div.active {
	background: var(--color-theme-primary);
	color: var(--color-theme-white);
}

.bp-messages-wrap.bp-messages-mobile .bp-messages-wrap.bm-search-popup {
	background: var(--global-body-bgcolor);
	padding: 1em;
	max-width: 100%;
}

.form-control.socialv-password-field{
	padding-right: 3.4375em;
}

.toggle-password{
    position:absolute;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
	z-index: 99;
	height: 3.125em;
	width: 3.125em;
	line-height: 3.125em;
	text-align: center;
	cursor: pointer;
}

@media screen and (max-width: 767px) {
	.bp-messages-wrap .chat-header .bpbm-search form input[type=text] {
		height: 30px !important;
		max-height: 30px !important;
		min-height: 30px !important;
	}

	.bp-messages-wrap:not(.bp-messages-mobile) .bp-messages-threads-wrapper {
		-webkit-transform: none !important;
		transform: none !important;
	}
}

@media screen and (max-width: 479px) {

	.bp-messages-wrap .chat-header .thread-actions>a,
	.bp-messages-wrap .chat-header .thread-actions>span,
	.bp-messages-wrap .chat-header .thread-actions>div.expandingButtons {
		width: 30px;
	}

	.bp-messages-wrap .chat-header .back {
		width: 30px;
		min-width: 30px;
	}

	.bp-messages-wrap .bm-reply .bm-editor [data-slate-placeholder=true] {
		font-size: 12px;
	}

	.bm-editor-content.notranslate.wp-exclude-emoji {
		font-size: 14px;
	}

	.bp-messages-wrap .bpbm-user-options .bpbm-user-blacklist table td {
		padding: .5em;
	}
}


/* mini chat soket */

.bp-messages-wrap.bp-better-messages-mini .chats .chat .head {
	background: var(--color-theme-primary);
	border-color: var(--border-color-light);
	color: var(--color-theme-white);
}

.bp-messages-wrap.bp-better-messages-mini .chats .chat {
	border-radius: var(--border-radius);
	background: var(--color-theme-white-box);
	box-shadow: 0 12px 28px 0 rgb(0 0 0 / 20%), 0 2px 4px 0 rgb(0 0 0 / 10%);
	border-color: var(--border-color-light);
}

[data-mode=dark] .bp-messages-wrap.bp-better-messages-mini .chats .chat {
	box-shadow: 0 12px 28px 0 rgb(4 5 10), 0 2px 4px 0 rgb(17 19 24);
}

.bp-messages-wrap.bp-better-messages-mini .bm-messages-list .bm-list .bm-messages-stack .bm-pic {
	min-width: 20px;
}

.bp-messages-wrap.bp-better-messages-mini .bm-reply .bm-send-message svg {
	fill: var(--global-font-color);
}

.bp-messages-wrap.bp-better-messages-mini .bm-pic img {
	height: 20px;
	width: 20px;
	min-width: 20px;
}

.bp-messages-wrap.bp-better-messages-mini .bbpm-avatar:before {
	bottom: .1em;
	right: 0;
	width: .2em;
	height: .2em;
	min-width: .2em;
}

.bp-messages-wrap.bp-better-messages-mini .bm-reply svg {
	color: var(--global-font-color);
}

/* social Login */
.mo-openid-app-icons {
	text-align: center;
}

.mo-openid-app-icons+.register-link {
	margin-top: 5em;
}

.mo-openid-app-icons>p {
	display: none;
}

.socialv-login-form .social-login-label {
	background-color: var(--color-theme-primary-light);
	color: var(--global-font-title);
	font-size: var(--font-size-normal);
	font-weight: var(--font-weight-semi-bold);
	border-bottom: 0;
	border-radius: var(--border-radius-full);
	height: 2.25em;
	width: 2.25em;
	line-height: 2em;
	margin: .5em auto 1.5em;
}

.login-submit .socialv-button:disabled {
	opacity: .5;
	cursor: no-drop;
}

.mo-openid-app-icons .login-button {
	padding: 0 .5em;
}

/*====================
level-box
===========================*/
.socialv-level-box {
	margin-bottom: 2em;
}

.socialv-level-requirements .requirements-title {
	display: flex;
	justify-content: space-between;
	align-items: center;
	flex-wrap: wrap;
}

.gamipress-rank-requirements-heading {
	font-size: unset;
	font-weight: unset;
}

.gamipress-rank-requirements {
	list-style: none;
	padding: 0;
	margin: .625em 0 0;
	text-align: initial;
}

.gamipress-rank-requirements li:not(:last-child) {
	margin-bottom: .3125em;
}

.gamipress-rank-requirements li {
	display: inline-block;
	width: 100%;
	position: relative;
	font-size: var(--font-size-normal);
	padding-left: 1.2em;
	color: var(--global-font-color);
}

.gamipress-rank-requirements li::before {
	content: "";
	background-color: var(--global-font-color);
	height: .125em;
	width: .625em;
	position: absolute;
	top: 50%;
	left: 0;
	transform: translateY(-50%);
}

.gamipress-rank-unlock-with-points {
	margin-top: 1em;
}

.single-rank.gamipress-layout-left .gamipress-rank-image {
	text-align: center;
}

/* privacy */
.socialv-locked-profile>i {
	font-size: 3em;
	color: var(--global-font-title);
}

.socialv-locked-profile p {
	margin: .5em 0 0;
	font-size: 1em;
}

/* editor */

#buddypress div.mce-toolbar-grp {
	background: var(--global-body-bgcolor);
	border-color: var(--border-color-light);
}

#buddypress button.wp-switch-editor {
	background: var(--global-body-bgcolor);
	border-color: var(--border-color-light);
	border-bottom-color: transparent;
	text-transform: capitalize;
	font-weight: var(--font-weight-medium);
	letter-spacing: var(--letter-spacing-one);
}

#buddypress .wp-editor-container {
	border-color: var(--border-color-light);
}

#buddypress .mce-toolbar .mce-btn-group .mce-btn.mce-listbox {
	background: var(--global-body-bgcolor);
	border-color: var(--border-color-light);
}

#buddypress .mce-toolbar .mce-btn-group .mce-btn.mce-active,
#buddypress .mce-toolbar .mce-btn-group .mce-btn:active,
#buddypress .qt-dfw.active {
	background: var(--global-body-bgcolor);
	border-color: var(--border-color-light);
	box-shadow: var(--global-box-shadow);
}

#buddypress .wp-switch-editor {
	background: var(--color-theme-white-box);
	color: var(--global-font-color);
}

#buddypress .html-active button.switch-html {
	background: var(--global-body-bgcolor);
	color: var(--global-font-color);
}

#buddypress .comment-reply-link,
#buddypress .generic-button a,
#buddypress .standard-form button,
#buddypress a.button,
#buddypress ul.button-nav li a,
a.bp-title-button {
	background: var(--color-theme-white-box);
	color: var(--global-font-color);
	border-color: var(--color-theme-white-box);
}

#buddypress a.button:focus,
#buddypress a.button:hover {
	background: var(--color-theme-white-box);
	color: var(--global-font-color);
	border-color: var(--color-theme-white-box);
}

#buddypress ul.socialv-list-post>li.modal.show{
    background:transparent;
}

.post-row .post-column .iq-pdf-preview{
	border-radius: 10px;
	border: 1px solid;
	border-color: transparent;
	transition: all .3s ease;
}
.post-row:hover .post-column .iq-pdf-preview{
	border-color: var(--color-input-border);
}
.post-row .post-column .iq-pdf-preview .iq-description-wrap{
	position: relative;
	margin: 1rem;
}
.post-row .post-column .iq-description-wrap .iq-title{
    display: block;
    font-size: 1rem;
    line-height: 1.3;
    font-weight: 500;
	color: var(--global-font-title);
}
.post-row .post-column .iq-pdf-preview .document-action-wrap{
    position: absolute;
    bottom: 4%;
    right: 3%;
    transform: translateY(-50%);
	opacity:0;
	visibility: hidden;
	transition: all .3s ease;
}
.post-row:hover .post-column .iq-pdf-preview .document-action-wrap{
	opacity:1;
	visibility: visible;
}
.post-row.column-1 .post-column .mpp-doc-content .doc-previews-link{
	background-color: var(--color-input-border);
	border-radius: 10px 10px 0 0;
}
.post-row .post-column .iq-pdf-preview .iq-description-wrap .iq-extension-type{
	opacity: 1;
    visibility: visible; 
	text-transform: uppercase;
	transition: all .3s ease;
}
.post-row:hover .post-column .iq-pdf-preview .iq-description-wrap .iq-extension-type{
	visibility: hidden;
    opacity: 0;
    width: 0;
    height: 0;
    overflow: hidden;
}
.post-row .post-column .iq-pdf-preview .iq-description-wrap .show_file_preview{
	cursor:pointer;
}
.post-row .post-column .iq-doc-preview .iq-description-wrap .show_file_preview{
	pointer-events: none;
}
.post-row .post-column .iq-pdf-preview .iq-description-wrap .iq-helper-text {
    position: relative;
    top: -10px;
    left: -34px;
    opacity: 0;
    visibility: hidden;
	transition: all .3s ease;
}
.post-row:hover .post-column .iq-pdf-preview .iq-description-wrap .iq-helper-text{
	top: 0;
    opacity: 1;
    visibility: visible;    
}

.post-row .post-column .iq-pdf-preview .iq-description-wrap .show_file_preview svg{
    height: 3.125em;
    width: 3.125em;
}

.post-row .post-column .iq-doc-preview .iq-description-wrap .iq-helper-text {
	display: none;
}
.post-row .post-column .iq-doc-preview {
	display: flex;
    justify-content: space-between;
    align-items: center;
	border: 1px solid var(--border-color-light);
    padding: 1em;
    border-radius: var(--border-radius);
    background: var(--global-body-bgcolor);
}

/* post image grid */
.post-row {
	margin-left: -3px;
	margin-right: -3px;
}

.post-row .post-column {
	padding-left: 3px;
	padding-right: 3px;
	margin-bottom: 6px;
	position: relative;
}

.post-row .post-column:last-child {
	margin-bottom: 0;
}

.post-row:not(.column-1) .post-column .post-wrap-inner {
	width: 100%;
	position: relative;
	cursor: pointer;
	background-repeat: no-repeat;
	background-size: cover;
	background-position: top center;
	background-image: none !important;
}


/* two images */
.post-row.column-2 .post-column.col-6 .post-wrap-inner img {
	height: 300px;
}

/* three images */
.post-row.column-3 .post-column.col-6 .post-wrap-inner img {
	height: 240px;
}

.post-row.column-3 .post-column.col-12 .post-wrap-inner img {
	height: 450px;
}


/* four images */
.post-row.column-4 .post-column.col-6 .post-wrap-inner img {
	height: 300px;
}


/* five images */
.post-row.column-5 .post-row.two .post-wrap-inner img {
	height: 378px;
}

.post-row.column-5 .post-row.three .post-wrap-inner img {
	height: 250px;
}


/* images */
.post-row.column-4 .post-column.col-6 .post-wrap-inner a {
	display: block;
}

.post-row .post-column .post-wrap-inner img {
	border-radius: var(--border-radius-box);
	max-height: none;
	min-height: auto;
	object-fit: cover;
	width: 100%;
}

.socialv-media-total {
	position: absolute;
	top: 0;
	bottom: 0;
	left: 0;
	right: 0;
	background: rgba(0, 0, 0, 0.5);
	margin: 0 3px;
	border-radius: var(--border-radius);
	text-align: center;
	display: flex;
	align-items: center;
	justify-content: center;
	color: var(--color-theme-white);
	font-weight: var(--font-weight-semi-bold);
	pointer-events: none;
	z-index: 1;
}

.post-row .post-column .post-wrap-inner .wp-video {
	margin: 0 auto;
	width: 100% !important;
	border-radius: var(--border-radius);
	height: 100% !important;
}

.post-row .post-column .post-wrap-inner .wp-video .mejs-container {
	border-radius: var(--border-radius);
}

/* one image */
.post-row.column-1 .post-column .post-wrap-inner {
	width: 100%;
	position: relative;
	cursor: pointer;
	background-repeat: no-repeat;
	background-size: cover;
	background-position: top center;
	border-radius: var(--border-radius);
}

.post-row.column-1 .post-column .post-wrap-inner.single-post-img {
	background: none !important;
}

.post-row.column-1 .post-column .post-wrap-inner:not(.mpp-activity-video-player, .mpp-activity-audio-player)>a {
	display: block;
	backdrop-filter: blur(10px);
}

.post-row.column-1 .post-column .post-wrap-inner img {
	border-radius: var(--border-radius-box);
	max-height: none;
	min-height: auto;
	object-fit: contain;
	width: 100%;
	aspect-ratio: 5/3;
}

.post-row.column-1 .post-column .post-wrap-inner .mpp-activity-media-doc img {
	width: auto;
}

/* video */

.post-row.column-1 .mejs-layers .mejs-overlay-button {
	height: 80px;
	width: 80px;
	background-position: 0 -39px;
	background-size: auto;
}

.post-row.zoom-gallery iframe {
	border-radius: var(--border-radius);
}

.post-row.column-2.zoom-gallery iframe,
.post-row.column-2 .post-column .mejs-container,
.post-row.column-2 .post-column .mejs-container Video {
	height: 224px !important;
}

.post-row.column-3 .post-column.col-12 .mejs-container,
.post-row.column-3 .post-column.col-12 .mejs-container Video,
.post-row.column-3.zoom-gallery .col-12 iframe {
	height: 340px !important;
}

.post-row.column-3.zoom-gallery iframe,
.post-row.column-3 .post-column .mejs-container,
.post-row.column-3 .post-column .mejs-container Video {
	height: 230px !important;
}

.post-row.column-4.zoom-gallery iframe,
.post-row.column-4 .post-column .mejs-container,
.post-row.column-4 .post-column .mejs-container Video {
	height: 224px !important;
}

.post-row.column-5.zoom-gallery .post-column .post-row.two iframe,
.post-row.column-5 .post-column .post-row.two .mejs-container,
.post-row.column-5 .post-column .post-row.two .mejs-container Video {
	height: 340px !important;
}

.post-row.column-5.zoom-gallery .post-column .post-row.three iframe,
.post-row.column-5 .post-column .post-row.three .mejs-container,
.post-row.column-5 .post-column .post-row.three .mejs-container Video {
	height: 225px !important;
}

.post-row .post-wrap-inner a.mpp-activity-item-title {
	bottom: 0;
	position: absolute;
	left: 0;
	right: 0;
	top: 0;
	z-index: 1;
	font-size: 0;
}

.post-row .mejs-layers .mejs-overlay-button {
	background-position: 0px -20px;
	height: 40px;
	width: 40px;
	background-size: 200px;
	background-repeat: no-repeat;
}

.mfp-wrap.mfp-gallery .mejs-video {
	width: 100% !important;
	min-height: 140px;
}

.mfp-wrap.mfp-gallery .wp-video {
	width: 100% !important;
}

/* audio */
.mpp-lightbox-content {
	display: flex;
	max-width: 80%;
	margin: 0 auto;
	align-items: center;
	justify-content: center;
}

.mpp-activity-audio-player+.socialv-media-total {
	border-radius: 50px;
}

.mpp-activity-audio-player audio {
	border-radius: 50px;
}

.mpp-activity-audio-player {
	line-height: 0;
}

@media (max-width:767px) {
	.post-row.column-5 .post-row.three .post-wrap-inner img {
		height: 200px;
	}

	.post-row.column-5 .post-row.two .post-wrap-inner img {
		height: 303px;
	}

	.post-row.column-2 .post-column .mejs-container,
	.post-row.column-2 .post-column .mejs-container Video,
	.post-row.column-2.zoom-gallery iframe {
		height: 175px !important;
	}

	.post-row.column-3 .post-column.col-12 .mejs-container,
	.post-row.column-3 .post-column.col-12 .mejs-container Video,
	.post-row.column-3.zoom-gallery .col-12 iframe {
		height: 300px !important;
	}

	.post-row.column-3.zoom-gallery iframe,
	.post-row.column-3 .post-column .mejs-container,
	.post-row.column-3 .post-column .mejs-container Video {
		height: 140px !important;
	}

	.post-row.column-4 .post-column .mejs-container,
	.post-row.column-4 .post-column .mejs-container Video,
	.post-row.column-4.zoom-gallery iframe {
		height: 140px !important;
	}

	.post-row.column-5.zoom-gallery .post-column .post-row.two iframe,
	.post-row.column-5 .post-column .post-row.two .mejs-container Video,
	.post-row.column-5 .post-column .post-row.two .mejs-container {
		height: 284px !important;
	}

	.post-row.column-5.zoom-gallery .post-column .post-row.three iframe,
	.post-row.column-5 .post-column .post-row.three .mejs-container,
	.post-row.column-5 .post-column .post-row.three .mejs-container Video {
		height: 187px !important;
	}

	.post-row .post-column .iq-pdf-preview .iq-description-wrap svg{
		height: 35px;
		width: 35px;
   }

   .post-row .post-column .iq-description-wrap .iq-title{
	   font-size: .875rem
   }

   .post-row .post-column .iq-description-wrap span{
	   font-size: .75rem;
   }
}

@media (max-width: 575px) {
	.post-row.column-2 .post-column.col-6 .post-wrap-inner img {
		height: 250px;
	}

	.post-row.column-4 .post-column.col-6 .post-wrap-inner img {
		height: 250px;
	}

	.post-row.column-3 .post-column.col-12 .post-wrap-inner img {
		height: 350px;
	}

	.post-row.column-5 .post-row.three .post-wrap-inner img {
		height: 180px;
	}

	.post-row.column-5 .post-row.two .post-wrap-inner img {
		height: 273px;
	}

	.post-row.column-4 .post-column .mejs-container,
	.post-row.column-4 .post-column .mejs-container Video,
	.post-row.column-4.zoom-gallery iframe {
		height: 115px !important;
	}

	.post-row.column-5.zoom-gallery .post-column .post-row.two iframe,
	.post-row.column-5 .post-column .post-row.two .mejs-container,
	.post-row.column-5 .post-column .post-row.two .mejs-container Video {
		height: 200px !important;
	}

	.post-row.column-5.zoom-gallery .post-column .post-row.three iframe,
	.post-row.column-5 .post-column .post-row.three .mejs-container,
	.post-row.column-5 .post-column .post-row.three .mejs-container Video {
		height: 131px !important;
	}
}

@media (max-width: 480px) {
	.post-row.column-2 .post-column.col-6 .post-wrap-inner img {
		height: 150px;
	}

	.post-row.column-4 .post-column.col-6 .post-wrap-inner img {
		height: 150px;
	}

	.post-row.column-3 .post-column.col-12 .post-wrap-inner img {
		height: 200px;
	}

	.post-row.column-3 .post-column.col-6 .post-wrap-inner img {
		height: 150px;
	}

	.post-row.column-5 .post-row.three .post-wrap-inner img {
		height: 120px;
	}

	.post-row.column-5 .post-row.two .post-wrap-inner img {
		height: 183px;
	}

	.post-row.column-2 .post-column .mejs-container,
	.post-row.column-2 .post-column .mejs-container Video,
	.post-row.column-2.zoom-gallery iframe {
		height: 100px !important;
	}

	.post-row.column-3 .post-column.col-12 .mejs-container,
	.post-row.column-3 .post-column.col-12 .mejs-container Video,
	.post-row.column-3.zoom-gallery .col-12 iframe {
		height: 174px !important;
	}

	.post-row.column-3.zoom-gallery iframe,
	.post-row.column-3 .post-column .mejs-container,
	.post-row.column-3 .post-column .mejs-container Video {
		height: 70px !important;
	}

	.post-row.column-4 .post-column .mejs-container,
	.post-row.column-4 .post-column .mejs-container Video,
	.post-row.column-4.zoom-gallery iframe {
		height: 70px !important;
	}

	.post-row.column-5.zoom-gallery .post-column .post-row.two iframe,
	.post-row.column-5 .post-column .post-row.two .mejs-container,
	.post-row.column-5 .post-column .post-row.two .mejs-container Video {
		height: 150px !important;
	}

	.post-row.column-5.zoom-gallery .post-column .post-row.three iframe,
	.post-row.column-5 .post-column .post-row.three .mejs-container,
	.post-row.column-5 .post-column .post-row.three .mejs-container Video {
		height: 98px !important;
	}
}


/* socket */
.bp-messages-wrap .chat-footer .bpbm-user-me .bpbm-status .current-status svg {
	font-size: .6em;
}

.bp-messages-wrap.bp-better-messages-list .threads-list .thread .pic.group>* {
	width: 35px !important;
	height: 35px !important;
}

.bp-messages-wrap.bp-better-messages-list .threads-list .thread .pic.group>* .avatar {
	width: 35px !important;
	height: 35px !important;
	min-width: 35px !important;
}

.bp-messages-wrap.bp-better-messages-list .threads-list .thread .pic img {
	width: 50px;
	height: 50px;
}

.bp-messages-wrap.bp-better-messages-list .threads-list .thread>*,
.bp-messages-wrap.bp-better-messages-list:not(.bp-messages-mobile) .threads-list .thread:hover>* {
	border-top-color: var(--color-theme-white-box);
	background: var(--global-body-bgcolor) !important;
}

.bp-messages-wrap.bp-better-messages-list .threads-list .thread .pic.group {
	min-width: 50px;
	width: 50px;
}

.bp-messages-wrap.bp-better-messages-list .threads-list .thread .pic {
	height: 50px;
	width: 50px;
}

.bp-messages-wrap.bp-better-messages-mini .chats .chat .head .thread-info .thread-info-data .group-online.online-now,
.bp-messages-wrap.bp-better-messages-mini .chats .chat .head .thread-info .thread-info-data .last-online.online-now {
	color: var(--color-theme-white);
}

.bp-messages-wrap.bp-better-messages-mini .chats .chat .head .thread-info .thread-info-data .name a {
	color: var(--color-theme-white) !important;
}

.mpp-paginator.no-ajax {
	margin-top: 2em;
}

.mpp-paginator li {
	padding: 0;
}

.mpp-previous,
.mpp-next {
	background: var(--color-theme-primary);
	color: var(--color-theme-white);
	font-size: var(--font-size-normal);
	padding: 10px 20px;
	border-radius: var(--border-radius);
	transition: all .45s ease 0s;
}

.mpp-previous:hover,
.mpp-next:hover {
	background: var(--color-theme-primary-dark);
}

.mpp-previous a,
.mpp-next a {
	color: inherit;
}

@media (max-width: 479px) {
	.mpp-single-media-prev-next {
		text-align: center;
	}

	.mpp-single-media-prev-next span {
		display: inline-block;
		float: none;
	}

	.mpp-single-media-prev-next span.mpp-next {
		margin-top: 15px;
	}
}

/* Poll Css */
#buddypress .bpolls-polls-option-html {
	border-color: var(--border-color-light);
}

#buddypress .bpolls-sortable-handle {
	background: var(--color-theme-primary-light) !important;
	border-color: var(--color-theme-primary-light) !important;
	color: var(--color-theme-primary) !important;
}

#buddypress .bpolls-option-delete {
	background: var(--color-theme-danger-light) !important;
	border-color: var(--color-theme-danger-light) !important;
	color: var(--color-theme-danger) !important;
}

#buddypress .bpolls-input {
	background-color: var(--global-body-bgcolor) !important;
	border: 0.0625em solid var(--border-color-light) !important;
}

#buddypress .bpolls-check-radio-wrap .bpolls-option-lbl {
	color: var(--global-font-title);
}

#buddypress .bpolls-add-option.button,
input#bpolls-datetimepicker {
	background: var(--color-theme-primary-light);
	border-color: var(--color-theme-primary-light);
	color: var(--color-theme-primary);
	height: auto;
}

.bpolls-vote-submit {
	background: transparent;
}

#buddypress .bpolls-item {
	margin-bottom: 1.5em;
}

#buddypress .bpolls-checkbox label {
	color: var(--global-font-color);
	font-size: var(--global-font-size);
}

.bpolls-check-radio-div {
	background: var(--global-body-bgcolor);
}

#buddypress .bpolls-percent {
	color: var(--global-font-color);
	margin-top: 6px;
}

#buddypress .bpolls-item .bpolls-result-votes .bpolls-post-voted img,
#buddypress .bpolls-item .bpolls-result-votes .bpolls-post-voted .bp-polls-view-all {
	height: 25px;
	width: 25px;
	min-width: 25px;
	outline: 3px solid var(--color-theme-white-box);
}

#buddypress #bpolls-attach-image {
	padding: 0;
	background: var(--color-theme-primary);
}

.bpolls-add-user-item .bpoll-add-option {
	height: auto;
}

.bpolls-vote-submit:hover,
.bpolls-vote-submit:focus {
	box-shadow: none !important;
	transform: none;
}

#buddypress p.bpolls-add-option-error {
	border: none;
	border-left: .1875em solid var(--color-theme-danger);
	background-color: var(--color-theme-danger-light);
	color: var(--color-theme-danger) !important;
	margin: 0;
	padding: 1em !important;
	border-radius: 0 var(--border-radius) var(--border-radius) 0;
}

#buddypress .comment-reply-link,
#buddypress .generic-button a,
#buddypress .standard-form button,
#buddypress a.button,
#buddypress input[type=button],
#buddypress input[type=reset],
#buddypress input[type=submit],
#buddypress ul.button-nav li a,
a.bp-title-button {
	background: var(--color-theme-primary);
	border-color: var(--color-theme-primary);
	color: var(--color-theme-white);
}

#buddypress .comment-reply-link:hover,
#buddypress .standard-form button:hover,
#buddypress a.button:focus,
#buddypress a.button:hover,
#buddypress div.generic-button a:hover,
#buddypress input[type=button]:hover,
#buddypress input[type=reset]:hover,
#buddypress input[type=submit]:hover,
#buddypress ul.button-nav li a:hover,
#buddypress ul.button-nav li.current a {
	background: var(--color-theme-primary-dark);
	border-color: var(--color-theme-primary-dark);
	color: var(--color-theme-white);
}

a.bpolls-delete-user-option {
	background-color: var(--color-theme-danger-light);
	color: var(--color-theme-danger);
}

.bpolls-image-container {
	margin-bottom: 2em;
}

.bpolls-check-radio-wrap input {
	min-width: 16px;
}

a.bpolls-cancel {
	border-color: var(--color-theme-danger);
	color: var(--color-theme-danger);
}

a.bpolls-cancel:hover {
	border-color: var(--color-theme-danger-dark);
	color: var(--color-theme-danger-dark);
}

h5.item-title p,
h6.item-title p {
	margin: 0;
}

.search-content-data:not(:last-child) {
	margin-bottom: 2em;
}

.search-content-data .title {
	margin-bottom: 1em;
}

.search-pagination {
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 1em;
	margin-top: 1.5em;
}

.search-pagination .page-numbers {
	display: inline-block;
	position: relative;
	width: 2.813em;
	height: 2.813em;
	text-align: center;
	line-height: 2.813em;
	color: var(--global-font-color);
	background-color: var(--color-theme-white-box);
	border: .0625em solid var(--border-color-light);
	border-radius: var(--border-radius);
}

.search-pagination .page-numbers.current,
.search-pagination .page-numbers:hover {
	color: var(--color-theme-white);
	background: var(--color-theme-primary);
	border-color: var(--color-theme-primary);
}

#buddypress div.pagination {
	margin: 1em 0 0;
}

.learnpress-widget-wrapper .lp-widget-course__description {
	word-wrap: break-word;
}

.close-header-cover-image .card-main.socialv-profile-box {
    margin-top: 9em;
}

.socialv-groups-lists .socialv-group-info .close-groupcover-list .group-icon {
    margin-top: 0;
}

.group-close-cover-image {
    margin-top: 4em;
}

form#mpp-whats-new-form #mpp-whats-new-content{
	margin-left: 3.4375em;
	padding: 0 0 1.25em 1.25em;
}

form#mpp-whats-new-form p.activity-greeting{
	margin-left: 4.4375em;
	margin-bottom: 1em;
}

div.mpp-activity-comments{
	margin: 0 0 0 2.8125em;
}

div.mpp-activity-comments ul li>ul{
	margin-left: 2.8125em;
	padding-left: 0;
}

#mpp-whats-new:focus,
div.mpp-activity-comments form textarea{
	border-color: var(--color-theme-primary) !important;
}

.mpp-media-activity #mpp-aw-whats-new-submit{
	line-height: normal;
}

.mpp-activity-avatar .avatar,
.mpp-acomment-avatar .avatar,
#mpp-whats-new-avatar .avatar,
.mpp-ac-reply-avatar .avatar{
	border-radius: 50%;
}

ul.mpp-activity-list li{
	border: none;
}

.mpp-acomment-options a,
#buddypress .mpp-activity-meta a.button{
	background-color: transparent;
	padding: 0;
	font-family: var(--global-font-family);
	font-size: var(--font-size-normal);
	font-weight: var(--font-weight-regular);
	text-transform: capitalize;
	letter-spacing: var(--letter-spacing-one);
	color: var(--global-font-color);
	border: none;
}

.mpp-activity-list .mpp-activity-content .mpp-activity-inner,
.mpp-activity-list .mpp-activity-content blockquote{
	margin: 0 0 .6em 0;
}

a.mpp-bp-primary-action span,
#mpp-reply-title small a span{
	background: var(--color-theme-primary);
	color: var(--color-theme-white);
	padding: 0;
	border-radius: var(--border-radius);
	margin-left: 0.125em;
	height: 13px;
	line-height: 13px;
	width: 13px;
	display: inline-block;
}

a.mpp-bp-primary-action:hover span,
#mpp-reply-title small a:hover span{
	background: var(--color-theme-primary-dark);
	color: var(--color-theme-white);
}

.mpp-acomment-options a:hover,
#buddypress .mpp-activity-meta a.button:hover{
	color: var(--color-theme-primary);
}

.mpp-acomment-options a.delete:hover,
#buddypress .mpp-activity-meta a.button.delete-activity:hover{
	color: var(--color-theme-danger);
}

.mpp-activity-header{
	margin-right: 0;
}

#mpp-activity-stream p{
	margin: 0;
	padding: .75em 1em;
	background: var(--global-body-bgcolor);
	border-radius: var(--border-radius-box);
}

div.mpp-activity-comments div.mpp-acomment-avatar img{
	height: 3.125em;
	width: 3.125em;
}

div.mpp-activity-comments div.mpp-acomment-content{
	font-size: var(--font-size-normal);
	margin: 1em 0 .5em 4.2em;
}

.mpp-ac-reply-content input[type=submit]{
	width: auto;
	line-height: normal;
}

.mpp-ac-reply-content .mpp-ac-reply-cancel{
	margin-left: .125em;
}

.mpp-acomment-options{
	margin: 0 0 1em 4em;
}

.mpp-activity-comments li form.mpp-ac-form{
	margin-top: 3.125em;
}

div.mpp-activity-comments form .mpp-ac-textarea{
	background: transparent;
	border: none;
	padding: 0;
	margin: 0;
}

#buddypress ul.item-list li,
div.mpp-activity-comments ul li,
#buddypress ul.item-list{
	border-color: var(--border-color-light);
}

#buddypress ul.item-list li img.avatar {
	margin: 0 0.625em 0 0;
}

div.mpp-activity-comments form div.mpp-ac-reply-content{
	margin-left: 3.125em;
	padding-left: .9375em;
}

.mpp-activity-list .mpp-activity-content{
	margin: 0 0 0 3.75em;
}

.mpp-activity-list a.mpp-bp-secondary-action,
.mpp-activity-list span.highlight,
a.mpp-bp-primary-action,
#mpp-reply-title small a{
	margin-right: 0.3125em;
}

div.mpp-activity-comments>ul {
	padding: 0 0 0 0.625em;
}

@media (max-width: 767px) {
	.close-header-cover-image .card-main.socialv-profile-box {
		margin-top: 7em;
	}
	
	.socialv_video_height iframe,
	.wp-video video.wp-video-shortcode {
		height: 250px;
	}
}

@media (max-width: 575.98px){
	div.mpp-activity-comments{
		margin: 0 0 0 .5em;
	}

	div.mpp-activity-comments>ul{
		padding: 0;
	}

	div.mpp-activity-comments li form.mpp-ac-form{
		margin-right: 0;
	}

	div.mpp-activity-comments ul li>ul{
		margin-left: .5em;
	}
}

.buddypress .activity-comments-test-popup {
    z-index: 1000;
    position: absolute;
    width: 50%;
    background: var(--color-theme-white-box);
    padding: 2em;
    border-radius: var(--border-radius-box);
    top: 50vh;
    left: 50%;
    transform: translateX(-50%);
    box-shadow: var( --global-box-shadow);
}

/*======================================
Activity-Popup
===========================================*/
.activitypopup .modal-dialog,
.shareactivitypopup .modal-dialog{
	max-width: 43.75em;
}

.activitypopup .modal-content,
.shareactivitypopup .modal-content {
	display: inherit;
	height: 43.75em;
}

.activitypopup .btn-close,
.shareactivitypopup .btn-close {
	padding: 0 !important;
	line-height: normal;
	color: var(--global-font-color);
	background: transparent;
	margin: 0;
}

.activitypopup .activity-list {
	list-style: none;
	padding: 0;
}

.activitypopup ul.activity-list li ul.activity-comments li {
	overflow: visible;
}

#buddypress ul.socialv-list-post .activitypopup .modal-header,
#buddypress ul.socialv-list-post .shareactivitypopup .modal-header{
	gap: 1em;
}

#buddypress ul.socialv-list-post .activitypopup .modal-footer,
#buddypress ul.socialv-list-post .activitypopup .modal-header,
#buddypress ul.socialv-list-post .shareactivitypopup .modal-header,
#buddypress ul.socialv-list-post .shareactivitypopup .modal-footer{
	border-color: var(--global-body-bgcolor);
}

#buddypress ul.socialv-list-post .activitypopup .modal-footer,
#buddypress ul.socialv-list-post .shareactivitypopup .modal-footer{
	display: inline-block;
}

#buddypress ul.socialv-list-post>li.activitypopup,
#buddypress ul.socialv-list-post>li.shareactivitypopup,
#buddypress ul.socialv-list-post>li.showfilepreview{
	padding: 0 !important;
	background: transparent;
	margin-bottom: 0;
	border-radius: 0;
	box-shadow: none;
}

#buddypress ul.socialv-list-post>li.activitypopup .btn-close,
#buddypress ul.socialv-list-post>li.shareactivitypopup .btn-close{
	line-height: 1em;
}

#buddypress ul.socialv-list-post .activitypopup ul.socialv-list-post>li{
	padding: 0;
	margin-bottom: 0;
	border-radius: 0;
	background: transparent;
	box-shadow: none;
}

#buddypress ul.socialv-list-post .activitypopup .modal-footer .socialv-comment-form{
	margin-top: 0;
}

.activitypopup .comment-container-main .acomment-options .socialv-acomment-reply,
.activitypopup .comment-container-main .acomment-options .bp-secondary-action {
	margin-right: .5em;
}

.activitypopup .socialv-comment-main .comment-activity .socialv-acomment-reply{
	display: none;
}

.shareactivitypopup .share_activity-content{
	padding: 1em;
	background: var(--global-body-bgcolor);
	min-height: 100%;
	border: 0.063em solid var(--border-color-light);
}

.shareactivitypopup .socialv-blog-box{
	border: 0.063em solid var(--border-color-light);
	padding: 1em;
	border-radius: var(--border-radius);
	background: var(--color-theme-white-box);
}

.activity-list.socialv-list-post .shareactivitypopup .socialv-group-activity,
.activity-list.socialv-list-post .shareactivitypopup .socialv-profile-activity{
	background: var(--color-theme-white-box);
}

.loading-popup {
	position: relative;
}

.loading-popup::before{
	content: "";
	position: absolute;
	top: 0;
	left: 0;
	height: 100%;
	width: 100%;
	background-color: var(--global-body-bgcolor);
}

.loading-popup::after{
	content: "";
	width: 3.125em;
	aspect-ratio: 1;
	border-radius: 50%;
	background:
		radial-gradient(farthest-side, var(--bs-primary) 94%, #0000) top/.5em .5em no-repeat,
		conic-gradient(#0000 30%, var(--bs-primary));
	-webkit-mask: radial-gradient(farthest-side, #0000 calc(100% - .5em), var(--color-theme-black) 0);
	animation: loader-popup .8s infinite linear;
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
}

.showfilepreview .modal-content{
    height: 780px;
}

.showfilepreview .loading-popup{
	height: 100%;
}

@keyframes loader-popup {
	to {
		transform: translate(-50%, -50%)rotate(1turn);
	}
}

@media (max-width: 991px){
	.admin-bar .activitypopup .modal-content {
		height: calc(100% - 3.5em);
	}

	.activitypopup .modal-content {
		height: 100%;
	}
}

@media (max-width: 719.98px) {
	.activitypopup .modal-dialog {
		max-width: calc(100% - 1em);
		width: calc(100% - 1em)
	}
}

@media (max-width: 600.98px) {
	.admin-bar .activitypopup .modal-content {
		height: calc(100% - 6em);
	}
}

.mpp-activity-list li.mini{
    font-size: inherit;
}

.mpp-activity-list li.mini .mpp-activity-avatar img.avatar, .mpp-activity-list li.mini .mpp-activity-avatar img.FB_profile_pic{
    height: 3.125em;
    width: 3.125em;
    min-width: 3.125em;
}


@media(max-width: 767.98px){
	.post-row .post-column .iq-pdf-preview .iq-description-wrap .show_file_preview svg{
        height: 30px;
        width: 30px;
    }

    .showfilepreview .modal-content{
         height: 500px;
    }

	/*====================
	wpstory-premium
	=========================*/
	.wpstory-modal-content .tui-image-editor-container .tui-image-editor-help-menu.top {
		height: auto;
		min-height: 40px;
		white-space: wrap;
		width: 100%;
	}

	.wpstory-modal-content .tui-image-editor-container .tui-image-editor-help-menu.top {
		top: 4em;
	}

	.wpstory-modal-content .tui-image-editor-container .tui-image-editor-header{
		top: 150px;
		right: 0;
	}
}