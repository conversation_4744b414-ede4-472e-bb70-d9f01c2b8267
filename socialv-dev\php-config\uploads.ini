; PHP Upload Configuration for SocialV Development
; Increase file upload limits for theme and plugin development

; Maximum allowed size for uploaded files
upload_max_filesize = 128M

; Maximum allowed size for POST data
post_max_size = 128M

; Maximum number of files that can be uploaded via a single request
max_file_uploads = 20

; Maximum execution time of each script, in seconds
max_execution_time = 300

; Maximum amount of time each script may spend parsing request data
max_input_time = 300

; Maximum amount of memory a script may consume
memory_limit = 512M

; Whether to allow HTTP file uploads
file_uploads = On

; Temporary directory for HTTP uploaded files
; upload_tmp_dir = /tmp
