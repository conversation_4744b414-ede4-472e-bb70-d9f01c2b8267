=== SocialV ===

Contributors: automattic
Tags: four-columns,left-sidebar,right-sidebar,custom-background,custom-colors,custom-header,custom-logo,custom-menu

Requires at least: 5.0
Tested up to: 6.0
Requires PHP: 5.6+
Stable tag: 1.0.0
License: GNU General Public License v2 or later
License URI: LICENSE

A starter theme called SocialV.

== Description ==

Description

== Installation ==

1. In your admin panel, go to Appearance > Themes and click the Add New button.
2. Click Upload Theme and Choose File, then select the theme's .zip file. Click Install Now.
3. Click Activate to use your new theme right away.

== Frequently Asked Questions ==

= Does this theme support any plugins? =

SocialV includes support for Elementor.

== Changelog ==

= 2.1.2 - July 28, 2025 =
- [Updated] Updated WooCommerce template files to latest version for compatibility with WooCommerce 10.0.4
- [Updated] - Compatibility updates for LearnPress (v *******.4 )

= 2.1.0 - April 1, 2025 =
- [New] - Category Restriction Options.
- [New] - Advanced Google reCAPTCHA plugin compatible for security.
- [Updated] - Compatibility updates for Wordpress (v 6.8.1 )
- [Updated] - Compatibility updates for WooCommerce (v 9.8.2 )
- [Updated] - Compatibility updates for LearnPress (v ******* )
- [FIXED] - Blog slider issue solved.
- [FIXED] - Addressed minor bugs.


= 2.0.17 - March 21, 2025 =
- [Updated] - Compatibility updates for WooCommerce (v 9.7.1 )
- [Updated] - Compatibility updates for LearnPress (v ******* )
- [FIXED] - Addressed minor bugs.

= 2.0.16 - Jan 30, 2025 =
- [Updated] - Compatibility updates for WooCommerce (v9.6.0)
- [FIXED] - Resolved security-related issues.
- [FIXED] - Addressed minor bugs.

= 2.0.15 - Jan 06, 2025 =
- [Updated] - Compatibility updates for: WooCommerce (v9.5.1) , Elementor (v3.26.3)
- [FIXED] - Addressed minor bugs.

[Updated] - Compatibility updates for:
   - Wordpress (v6.7)
   - WooCommerce (v9.4.1)

= 2.0.13 - November 12, 2024 =
[New] - Added option to stop activity action for "Group Action".
[Updated] - Compatibility updates for:
   - BuddyPress (v14.2.1)
   - WooCommerce (v9.3.3)
   - Paid Memberships Pro (v3.3.1)
   - BBPress (v2.6.7)
[Fixed] - Resolved minor bugs.


= 2.0.12 - Oct 22 2024 =
[Updated] - Updated outdated WooCommerce templates.
[Fixed] - Minor bugs.
[Compatibility] - Compatible with BuddyPress latest version.
[Fixed] - Escapation issue.

= 2.0.11 - Sep 27 2024 =
[Fixed] Updated WooCommerce templates.
[Fixed] Updated LearnPress templates to the latest version.
[FIXED] Improved design of activity comments.
[FIXED] Addressed minor bugs.

= 2.0.10 - July 13 2024 =
[New] - Reshare activity, also allowing you to add your own content
[Improvement] - Activity Posts open in popup with loading skeleton.
[Compatibility] - Supports new Group Type feature of BuddyPress.
[Updated] - compatible with the latest versions of WooCommerce (9.1.2), Paid Memberships Pro (3.0.6) and BBpress (2.6.7) .
[Updated] Upgraded FontAwesome library.
[Fixed] - Minor bugs.

= 2.0.9 - May 20 2024 =
[Compatibility] - Compatible with BuddyPress 12.5.0.
[Fixed] - Escapation issue.
[Fixed] - Minor bugs.

= 2.0.8 - Apr 18 2024 =
[New] - Installation Wizard for Enhanced and Expedited Onboarding Experience.
[New] - Option to modify the color of the comment form the dashboard.
[New] - Copying Activity URLs within the platform.
[Update] - Enhanced user interface for displaying comments, now view additional comments in a pop-up.
[Update] - Improved reaction design for better visual appeal and user engagement.
[Update] - Optimized Code for Improved Platform Efficiency and Performance.
[Fixed] - Streamlined reaction list retrieval upon plugin activation.
[Fixed] - Resolve sidebar panel issue in mobile view.
[Fixed] - Fixed breadcrumb inconsistencies between Gamipress and forum sections.
[Fixed] - Rectified friend suggestion functionality for smoother user interactions.

= 2.0.7 - Feb 10 2024 =
[New]  Introducing a new button for reading all notifications.
[New] - Stop Activity Action ['Register New user','Change User Avatar / Cover Image'].
[New] - Button for re-sending the verification email key.
[Update] - Separate Pin  Functionality ("Activity , Group, Profile Actions").
[Compatibility] - WordPress Latest Version (6.4.3).
[Compatibility] - BuddyPress Latest Version (12.2.0).
[Compatibility] - LearnPress Latest Version ( 4.2.6).
[Fixed] -  Minor bug fixes.

= 2.0.6 - Jan 18 2024 =
[Update] - Buddypress new version (12.1.1) compatible.
[Fixed] - Some bug fixed.

= 2.0.5 - Jan 10 2024 = 
[Update] Buddypress new version (12.0.0) compatible.
[Update] Better messages new version (2.4.6) compatible.
[Update] WP premiume story new version (3.5.0) compatible.
[Update] LearnPress new version (******* ) compatible.
[New] Logo text color "dark/light" option.
[New] Registration method on default registeration page : Auto / Menully / Verification Key.
[New] share activity on telegrame option.
[New] Show Password icone.
[fixed] Sidebar in mobile.
[fixed] Other bug fixed.

= 2.0.4 - Oct 27 2023 = 
[Improvement] - Code Optimization
[New] - Added support for the Wbcom Designs BuddyPress Activity Social Share plugin.
[New] - Groups Page: Added the option to switch between grid and list view with add option in the Redux panel.
[New] - Search: Added the option to select all Post fields in the Redux Panel.
[Added] - Header Search: Included data for forums, replies, and topic content in AJAX search.
[Added] - Search Page: Added tabs for forums, replies, and topic content with pagination.
[Added] - Social Sharing Icons: Added the ability to include multiple icons and an add/remove option.
[Fixed] - Live Style Customizer Option: Resolved color option refresh issue.
[Update] - Ensured compatibility with the latest versions of LearnPress and WooCommerce.
[Fixed] - Mobile Sidebar Menu: Fixed the collapsed menu issue.
[Fixed] - Miscellaneous bug fixes.

= 2.0.3 - Sept 14 2023 = 
[New] - Integrated Wbcom Designs for BuddyPress Polls Plugin compatibility.
[New] - Header Search: Added all post activity content data with AJAX search UI improvement.
[New] - Search Page: Added all posts content-wise showing tabs data with pagination and UI improvement.
[New] - Search Page: Added to Pagination Option in Redux Panel.
[New] - LearnPress Tag widget added.
[Added] Group Administrator Widget (Added option to set the order as the highest active user)
[Fixed] - Header: Custom notification read/unread issue fixed.
[Update] - LearnPress: Latest version compatibility.
[Upgrade] - LearnPress Instructor Page UI improvement.
[Fixed] - Live Style Customizer Option: Color option save issue fixed. 
[Fixed] - Minor bug fixes.

= 2.0.2 - Aug 25 2023 = 
[New] - WPML Language Supported.
[New] - WPML: Added Language Switches Dropdown in Header.
[New] - Recaptcha Compatibility:
   - Supported: Wordfence Security Plugin.
   - Supported: Advanced Google Recaptcha Plugin.
[New] - Two-Factor Authentication:
   - Supported: MiniOrange's Google Authenticator Plugin.
[New] - Live Style Customizer Option: Added option to remove LTR/RTL direction option in Redux panel.
[Upgrade] - Additional Post Types: Added to Unrestricted Select Post Type Option in Redux Panel.
[Upgrade] - Group List Box: Total Members Count Links Updated.
[Update] - LearnPress: Latest Version Compatible.
[Fixed] - RTL Mode Direction Issue Fixed.
[Fixed] - Global Typography Option Issue Fixed.
[Fixed] - Redux Setting RTL UI Design.

= 2.0.1 - Aug 03 2023 = 
[New] - Added an External link option to User & Group Profile Gallery Page Media Upload.
[New] - Integrated Wbcom Designs - Activity Link Preview for BuddyPress Plugin compatibility.
[New] - Added an On/off option in Redux Panel to show all users or only friends on the Wp Story Activity page.
[Fixed] - Resolved the issue of showing all pages without login.
[Fixed] - Clicking anywhere else on the header remove the search area in the search, resolving the issue.

= 2.0.0 - July 31 2023 = 
[New] - Paid Membership Pro Supported.
[New] - PMP Pricing Plan Page.
[New] - Elementor Widget PMP Pricing Plan.
[New] - Profile Page Forum Tab On/Off option add in Redux Panel.
[New] - Activity post comments order Option add in Redux Panel.
[Upgrade] - Header login button icon + text option add in desktop view.
[Update] - WooCommerce latest version 7.9 compatible.
[Update] - LearnPress latest version 4.2 compatible.
[Fixed] - Minor bug fixes.

= 1.9.0 - Jun 23 2023 = 
[New] - Added Activity Post Grid Style.
[New] - Added Activity Post Blur / Full Image Option in the Redux panel.
[New] - Added Hide / Undo Post option in activity posts.
[New] - Added Forum Create Topic and Reply notification.
[New] - Ajax for Activity Post deletion.
[New] - Added confirmation message for Activity Post deletion with success message.
[New] - Added verification badge icon for User Profile friends list.
[New] - Added Non-Restricted Post type and Custom URL options in the Redux panel.
[New] - Added Share Post Button On/Off option in the Redux panel.
[New] - Added description field in Register form and Additional details.
[New] - Added Header Sticky Background Option in the Redux Panel.
[Update] - WooCommerce latest version 7.8 compatible.
[Update] - Redesigned the Socket.
[Upgrade] - Updated Header Friend Request Button Ajax Count Value Without Refresh Update.
[Upgrade] - Implemented Redirect URL using Filter.
[Upgrade] - Optimized the code for better performance.
[Fixed] - Resolved the issue with Notification Comment Post link redirect.
[Fixed] - Fixed the WPML Language switches redirect issue.
[Fixed] - Resolved the issue with Blog Post visibility (private/public/protected).
[Fixed] - Minor bug fixes.

= 1.8.5 - May 19 2023 = 
[New] - Reaction Plugin: added API
[New] - Share activity post: added notification
[New] - Login button: set icon in mobile view, added Redux option
[New] - Sharing activity post, User Profile & Group Administrator widget, Reaction user like modal: added verification badge icon
[New] - Default avatar/cover image change option in Redux panel
[New] - Hidden Group, Private Group with moderation
[Update] - Better Messages: latest version 2.1.8 compatible
[Update] - LearnPress: latest version 4.2.2.4 compatible
[Upgrade] - Breadcrumb Title Text: added new hook to detect Text
[Upgrade] - User Profile Menu: added code using Filter hook
[Fixed] - User Group via add post disabled in Activity Feed
[Fixed] - Logo set size mini sidebar
[Fixed] - Group Create User Post and Update profile image content doesn't show in Activity Post
[Fixed] - Online/Offline status Label and WooCommerce Some of text : translate issue
[Fixed] - WooCommerce Shop Page: all Redux options not working
[Fixed] - Favorites post: it doesn't show in "User profile" Post
[Fixed] - Minor bug fixed.

= 1.8.2 - Apr 03 2023 = 
[Fixed] - Forget PassWord Issue Solved

= 1.8.1 - Mar 25 2023 = 
[Fixed] - Notification Warning
[Fixed] - Moderation Warning
[Fixed] - Minor Bug Solve

= 1.8.0 - Mar 17 2023 = 
[New] - Reaction feature added on Comments & Posts
[New] - Privacy Settings for Profile Private/Public
[New] - User Profile Picture Show on Click
[New] - User Profile Cover Picture Show on Click
[New] - GamiPress Levels Unlock info Page.
[New] - User Profile Widget added On/Off option for without login
[New] - Full logo option add in redux settings
[New] - Redux Setting UI Update
[Update] - Woo-Commerce Latest Version Compatibility
[Update] - LearnPress Latest Version Compatibility
[Update] - LearnPress Courses Review Latest Version Compatibility
[Fix] - Restriction pages issue solve
[Fix] - Responsive Side bar menu issue solve
[Fix] - Minor Bug solve

= 1.7.2 - Mar 17 2023 = 
[Fixed] - Minor Bug Fixed.

= 1.7.1 - Jan 19 2023 = 
[Fixed] - Minor Bug Fixed.

= 1.7.0 - Jan 13 2023 = 
New : Login with other social media.
New : Blog Post Option in activity feed.
New : Share Activity Post features (post in post).
New : Wbcom Designs - BuddyPress Giphy plugin Compatible.
New : User Profile Personal information visibility settings.
New : Friend suggestion refuse feature.
New : More menu item redux option.
New : Redux On/Off Option for User profile courses tab.
Update: Social Share Ui/UX Optimize.
Update : Wp Story Maker Latest version ******* Compatible & UI/UX Improvement.
Update : BuddyPress Latest Version 11.0.0 Compatible.
Update : WooCommerce Latest Version 7.3.0 Compatible.
Update : LearnPress Latest Version 4.2.0 Compatible.
Update : Elementor Latest Version 3.10.0 Compatible.
Update : GamiPress Latest Version 2.5.1 Compatible.
Fixed : Current user active status remove from recent active sidebar.

= 1.6.3 - Dec 14 2022 =
[update] - Better Messages Latest version Compatible.
[update] - Wp Story Maker Latest version Compatible.
 - Added: New Facebook Style.
 - Added: Reporting system.
 - Added: Story deleting feature.
 - Added: Story pause/unpause buttons.
 - Added: Video mute/unmute buttons.
[Fixed] - Minor Bug Fixed.

= 1.6.2 - Nov 21 2022 =
[New] Verify Member Add Badge Notification
[New] Verify Member Invitation Request Badge Notification Button Add In User profile
[New] Online User Status Add In Header Notification 
[New] Add Show/Less Group Description In Group Detail Page
[New] Better Messages – Live Chat Socket Compatibility
[Fix] Live Style Customizer Admin Save Setting Issue Fixed
[Fix] Redux Light/Dark Mode Color Issue Fixed

= 1.6.1 - Nov 04 2022 =
[New] Tested with WordPress 6.1.
[New] Tested with WooCommerece 7.0.1.
[New] Using Ajax Based Showing Courses (LMS)

= 1.6.0 - Nov 02 2022 =
[New] Better Messages – Live Chat integration
[New] Live Chat Notification Bell.
[New] Profile now includes more social media. eg: ( Skype, dribbal, )
[New] Dark - Light Mode Logo Options
[New] BP xProfile Location plugin supported.
[New] Gamipress - you can display only selected points.
[Fix] Emails sent to forgotten passwords have been fixed.
[Fix] redirect issue for new users registering for active accounts.
[Fix] Video preview for ios fixed.

= 1.5.0 - Sep 30 2022 =
[New] User can Block & Report the posts, groups, peoples.
[New] photos, videos can be upload with external link while posting.
[Fix] Comment box on click opening issue fixed.

= 1.4.0 - Sep 23 2022 =
[New] LearnPress - LMS Integration.
[New] LearnPress -  Courses Module added in Buddypress User Profile.
[New] LearnPress -  Course page options in Redux .
[New] LearnPress - Courses Category Widget Add.
[New] LearnPress -  Recent / Features Courses List Widget Add
[New] LearnPress with RTL & Dark Mode Supported.
[New] BuddyPress Login Form Redirect Using Ajax and Login link added on registration form.
[New] Add Non-Restricted Multiple Post selection (Post , Page, Courses, Forums, Topic)

= 1.3.1 - Aug 20 2022 =
[New] After Login Set Previous Page Option Added.

= 1.3.0 - Aug 10 2022 =
[New] WooCommerce Supported and Shop Page Added.
[New] Redux WooCommerce options added.
[New] Redux Buddypress New options.
[New] WooCommerce with RTL & Dark Mode Supported.
[New] Message Option Set In User Profile.
[Fix] Notification Unread Count Issue Resolve.
[Fix] Responsive Activity Sidebar Issue Resolve. 

= 1.2.0 - Aug 01 2022 =
[New] Add New LTR/RTL Switch
[New] WPML Language Supported
[New] Live Customizer Page Options ON/OFF in Redux
[New] Iqonic Before/After image ShortCode add in Elementor
[New] Iqonic IconBox , Iqonic ImageBox ShortCode add new Style
[New] Without Friend To Show All Activity Post to Add Option in Redux

= 1.1.0 - Jul 21 2022 =
[New] GamiPress Points / Ranks Tab / Point Tab show In User Profile
[New] Live Customizer Option
[New] Add New System Mode In Dark Like Mode
[New] Add New Panel To Show User Notification, Message, Request And Search Icon In Mobile View

= 1.0.2 - Jul 11 2022 =
* [Add] Resticted - Non Restricted Page Options
* [Add] Dark - Light Mode Color Customization Options

= 1.0.1 - Jul 9 2022 =
* [Add] New Feature Dark - Light Mode
* [Add] Child Theme
* [Change] Set Common Logo Functionality

= 1.0.0 - Jul 8 2022 =
* Initial release
