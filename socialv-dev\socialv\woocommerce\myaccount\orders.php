<?php

/**
 * Orders
 *
 * Shows orders on the account page.
 *
 * This template can be overridden by copying it to yourtheme/woocommerce/myaccount/orders.php.
 *
 * HOWEVER, on occasion WooCommerce will need to update template files and you
 * (the theme developer) will need to copy the new files to your theme to
 * maintain compatibility. We try to do this as little as possible, but it does
 * happen. When this occurs the version of the template file will be bumped and
 * the readme will list any important changes.
 *
 * @see https://docs.woocommerce.com/document/template-structure/
 * @package WooCommerce\Templates
 * @version 9.5.0
 */

defined('ABSPATH') || exit;

do_action('woocommerce_before_account_orders', $has_orders); ?>

<?php if ($has_orders) : ?>

    <table class="woocommerce-orders-table woocommerce-MyAccount-orders shop_table shop_table_responsive my_account_orders account-orders-table">
        <thead>
            <tr>
                <?php foreach (wc_get_account_orders_columns() as $column_id => $column_name) : ?>
                    <th class="woocommerce-orders-table__header woocommerce-orders-table__header-<?php echo esc_attr($column_id); ?>">
                        <span class="nobr"><?php echo esc_html($column_name); ?></span>
                    </th>
                <?php endforeach; ?>
            </tr>
        </thead>
        <tbody>
            <?php
            foreach ($customer_orders->orders as $customer_order) {
                $order      = wc_get_order($customer_order); 
                $item_count = $order->get_item_count() - $order->get_item_count_refunded();
            ?>
                <tr class="woocommerce-orders-table__row woocommerce-orders-table__row--status-<?php echo esc_attr($order->get_status()); ?> order">
                    <?php foreach (wc_get_account_orders_columns() as $column_id => $column_name) : ?>
                        <td class="woocommerce-orders-table__cell woocommerce-orders-table__cell-<?php echo esc_attr($column_id); ?>" data-title="<?php echo esc_attr($column_name); ?>">
                            <?php if (has_action('woocommerce_my_account_my_orders_column_' . $column_id)) : ?>
                                <?php do_action('woocommerce_my_account_my_orders_column_' . $column_id, $order); ?>

                            <?php elseif ('order-number' === $column_id) : ?>
                                <a href="<?php echo esc_url($order->get_view_order_url()); ?>">
                                    <?php echo esc_html(_x('#', 'hash before order number', 'socialv') . $order->get_order_number()); ?>
                                </a>

                            <?php elseif ('order-date' === $column_id) : ?>
                                <time datetime="<?php echo esc_attr($order->get_date_created()->date('c')); ?>"><?php echo esc_html(wc_format_datetime($order->get_date_created())); ?></time>

                            <?php elseif ('order-status' === $column_id) : ?>
                            <?php echo esc_html(wc_get_order_status_name($order->get_status()));
                            elseif ('order-total' === $column_id) :

                                /* translators: 1: formatted order total 2: total order items */
                                echo wp_kses_post(sprintf(_n('%1$s for %2$s item', '%1$s for %2$s items', $item_count, 'socialv'), $order->get_formatted_order_total(), $item_count));

                            elseif ('order-actions' === $column_id) :
                                $actions = wc_get_account_orders_actions($order);

                                if (!empty($actions)) {
                                    foreach ($actions as $key => $action) { // phpcs:ignore WordPress.WP.GlobalVariablesOverride.Prohibited
                                        echo '<a href="' . esc_url($action['url']) . '" class="woocommerce-button ' . esc_attr( $wp_button_class ) . ' button ' . sanitize_html_class( $key ) . '"> ' . esc_html($action['name']) . '</a>';
                                    }
                                }
                            endif; ?>
                        </td>
                    <?php endforeach; ?>
                </tr>
            <?php
            }
            ?>
        </tbody>
    </table>

    <?php do_action('woocommerce_before_account_orders_pagination'); ?>

    <?php if (1 < $customer_orders->max_num_pages) : ?>
        <div class="woocommerce-pagination woocommerce-pagination--without-numbers woocommerce-Pagination">
            <?php if (1 !== $current_page) : ?>
                <a class="woocommerce-button woocommerce-button--previous woocommerce-Button woocommerce-Button--previous socialv-box-shadow socialv-morden-btn" href="<?php echo esc_url(wc_get_endpoint_url('orders', $current_page - 1)); ?>">
                    <span class="socialv-main-btn">
                        <span class="text-btn"><?php esc_html_e('Previous', 'socialv'); ?></span>
                    </span>
                </a>
            <?php endif; ?>

            <?php if (intval($customer_orders->max_num_pages) !== $current_page) : ?>
                <a class="woocommerce-button woocommerce-button--next woocommerce-Button woocommerce-Button--next socialv-box-shadow socialv-morden-btn" href="<?php echo esc_url(wc_get_endpoint_url('orders', $current_page + 1)); ?>">
                    <span class="socialv-main-btn">
                        <span class="text-btn"> <?php esc_html_e('Next', 'socialv'); ?></span>
                    </span>
                </a>
            <?php endif; ?>
        </div>
    <?php endif; ?>

<?php else : ?>
    <div class="woocommerce-message woocommerce-message--info woocommerce-Message woocommerce-Message--info woocommerce-info">
        <div class="socialv-woocomerce-download-product">
            <a class="woocommerce-Button" href="<?php echo esc_url(apply_filters('woocommerce_return_to_shop_redirect', wc_get_page_permalink('shop'))); ?>">
                <?php esc_html_e('Browse products', 'socialv'); ?>
            </a>
            <p class="socialv-order-msg">
                <?php esc_html_e('No order has been made yet.', 'socialv'); ?>
            </p>
        </div>

    </div>
<?php endif; ?>

<?php do_action('woocommerce_after_account_orders', $has_orders); ?>