#!/bin/bash

# 检查 PHP 配置脚本
# 使用方法: ./scripts/check-php-config.sh

echo "🔍 检查 SocialV 开发环境 PHP 配置..."
echo

# 检查容器是否运行
if ! docker-compose ps | grep -q "wordpress.*Up"; then
    echo "❌ WordPress 容器未运行，请先启动: docker-compose up -d"
    exit 1
fi

echo "📊 当前 PHP 配置:"
echo "----------------------------------------"

# 获取关键配置信息
docker-compose exec -T wordpress php -r "
echo 'Upload Max Filesize: ' . ini_get('upload_max_filesize') . PHP_EOL;
echo 'Post Max Size: ' . ini_get('post_max_size') . PHP_EOL;
echo 'Memory Limit: ' . ini_get('memory_limit') . PHP_EOL;
echo 'Max Execution Time: ' . ini_get('max_execution_time') . ' seconds' . PHP_EOL;
echo 'Max Input Time: ' . ini_get('max_input_time') . ' seconds' . PHP_EOL;
echo 'Max File Uploads: ' . ini_get('max_file_uploads') . PHP_EOL;
echo 'File Uploads Enabled: ' . (ini_get('file_uploads') ? 'Yes' : 'No') . PHP_EOL;
"

echo "----------------------------------------"
echo

# 检查 WordPress 上传限制
echo "📁 WordPress 上传限制:"
docker-compose exec -T wordpress wp eval "
\$upload_size = wp_max_upload_size();
echo 'WordPress Max Upload Size: ' . size_format(\$upload_size) . PHP_EOL;
" --allow-root 2>/dev/null || echo "WordPress CLI 不可用或未安装"

echo
echo "✅ 配置检查完成！"
echo
echo "💡 提示:"
echo "   - 如需修改配置，编辑 php-config/uploads.ini 文件"
echo "   - 修改后运行: docker-compose restart wordpress"
echo "   - 当前配置支持最大 128MB 文件上传"
echo
