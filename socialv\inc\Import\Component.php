<?php

/**
 * SocialV\Utility\Import\Component class
 *
 * @package socialv
 */

namespace SocialV\Utility\Import;

use DOMDocument;
use SocialV\Utility\Component_Interface;
use WP_Query;

class Component implements Component_Interface
{
	/**
	 * Gets the unique identifier for the theme component.
	 *
	 * @return string Component slug.
	 */

	public function get_slug(): string
	{
		return 'import';
	}

	/**
	 * Adds the action and filter hooks to integrate with WordPress.
	 */

	public function initialize()
	{

		add_filter('merlin_generate_child_functions_php', 	array($this, 'socialv_generate_child_functions_php'), 10, 2);
		add_filter('merlin_generate_child_style_css', 		array($this, 'socialv_generate_child_style_css'), 10, 5);
		add_action('import_start', 							array($this, 'socialv_before_import_files'));
		add_filter('merlin_import_files', 					array($this, 'socialv_import_files'));
		add_action('merlin_after_all_import', 				array($this, 'socialv_after_import_setup'));
		add_filter('merlin_generate_child_screenshot', 		array($this, 'socialv_generate_child_screenshot'));
	}

	function socialv_generate_child_functions_php($output, $slug)
	{

		$slug_no_hyphens = strtolower(preg_replace('#[^a-zA-Z]#', '', $slug));

		$output = "
		<?php
		/**
		 * Theme functions and definitions.
		 */
		add_action( 'wp_enqueue_scripts', '{$slug_no_hyphens}_enqueue_styles' ,99);

		function {$slug_no_hyphens}_enqueue_styles() {
				
			wp_enqueue_style( 'parent-style', get_stylesheet_directory_uri() . '/style.css'); 
			wp_enqueue_style( 'child-style',get_stylesheet_directory_uri() . '/style.css');
		}

		/**
		 * Set up My Child Theme's textdomain.
		*
		* Declare textdomain for this child theme.
		* Translations can be added to the /languages/ directory.
		*/
		function {$slug_no_hyphens}_child_theme_setup() {
			load_child_theme_textdomain( 'socialv', get_stylesheet_directory() . '/languages' );
		}
		add_action( 'after_setup_theme', '{$slug_no_hyphens}_child_theme_setup' );
	";

		// Let's remove the tabs so that it displays nicely.
		$output = trim(preg_replace('/\t+/', '', $output));

		// Filterable return.
		return $output;
	}

	function socialv_generate_child_screenshot()
	{
		return trailingslashit(get_template_directory()) . 'screenshot.png';
	}

	public function socialv_generate_child_style_css($output, $slug, $author, $parent, $version)
	{

		$output = "
			/**
			* Theme Name: {$parent} Child
			* Description: This is a child theme of {$parent}, generated by iQonic Design.
			* Author: {$author}
			* Template: {$slug}
			* Version: {$version}
			*/\n
		";

		// Let's remove the tabs so that it displays nicely.
		$output = trim(preg_replace('/\t+/', '', $output));

		return  $output;
	}

	function socialv_before_import_files()
	{
		// Activate BuddyPress components
		$active_components = bp_get_option('bp-active-components');
		$all_components = bp_core_admin_get_components();
		$active_components = array_merge($active_components, $all_components);
		update_option('bp-active-components', $active_components);
		if (! function_exists('dbDelta')) {
			require_once ABSPATH . 'wp-admin/includes/upgrade.php';
		}
		require_once buddypress()->plugin_dir . 'bp-core/admin/bp-core-admin-schema.php';
		bp_core_install();

		// Buddypress Template "legacy"
		update_option('_bp_theme_package_id', 'legacy');
	}

	function socialv_import_files()
	{

		$import_datas = array(
			'require_import_page_file'     						=> trailingslashit(get_template_directory()) . '/inc/Import/Demo/required/socialv-page.xml',
			'require_local_import_post_file'      				=> trailingslashit(get_template_directory()) . '/inc/Import/Demo/required/socialv-post.xml',
			'require_local_import_post_type_file'     			=> trailingslashit(get_template_directory()) . '/inc/Import/Demo/required/socialv-post-types.xml',
			'require_local_import_reports_file'  				=> trailingslashit(get_template_directory()) . '/inc/Import/Demo/required/socialv-reports.xml',
			'require_local_import_templates_file'     			=> trailingslashit(get_template_directory()) . '/inc/Import/Demo/required/socialv-templates.xml',
			'require_local_import_texonomies_file'     			=> trailingslashit(get_template_directory()) . '/inc/Import/Demo/required/socialv-texonomies.xml',
			'require_local_import_mediapress_file'       		=> trailingslashit(get_template_directory()) . '/inc/Import/Demo/required/socialv-mediapress.xml',
			'require_local_import_landing_page_file'     		=> trailingslashit(get_template_directory()) . '/inc/Import/Demo/required/socialv-landing-page.xml',
			'require_local_import_fields_file'     				=> trailingslashit(get_template_directory()) . '/inc/Import/Demo/required/socialv-fields.xml',
			'require_local_import_fields_group_file'  			=> trailingslashit(get_template_directory()) . '/inc/Import/Demo/required/socialv-field-groups.xml',
			'require_local_import_buddypress_directory_file'    => trailingslashit(get_template_directory()) . '/inc/Import/Demo/required/socialv-buddypress-directory.xml',
			'require_local_import_buddypress_emails_file'       => trailingslashit(get_template_directory()) . '/inc/Import/Demo/required/socialv-buddypress-emails.xml'
		);
		$import_images = array(
			'require_local_import_image_file'       => trailingslashit(get_template_directory()) . '/inc/Import/Demo/required/socialv-featured-images.xml',
		);

		if (class_exists('WooCommerce')) {
			$import_datas += array(
				'wc_local_import_product_file'     => trailingslashit(get_template_directory()) . '/inc/Import/Demo/wocommerce/socialv-products.xml',
				'wc_local_import_orders_file'      => trailingslashit(get_template_directory()) . '/inc/Import/Demo/wocommerce/socialv-orders.xml',
				'wc_local_import_refunds_file'     => trailingslashit(get_template_directory()) . '/inc/Import/Demo/wocommerce/socialv-refunds.xml',
				'wc_local_import_variations_file'  => trailingslashit(get_template_directory()) . '/inc/Import/Demo/wocommerce/socialv-variations.xml',
				'wc_local_import_coupons_file'     => trailingslashit(get_template_directory()) . '/inc/Import/Demo/wocommerce/socialv-coupons.xml'
			);
			$import_images += array(
				'wc_local_import_image_file'       => trailingslashit(get_template_directory()) . '/inc/Import/Demo/wocommerce/socialv-wocommerce-image.xml',
			);
		}

		if (class_exists('Learnpress')) {
			$import_datas += array(
				'lp_local_import_courses_file'          => trailingslashit(get_template_directory()) . '/inc/Import/Demo/learnpress/socialv-courses.xml',
				'lp_local_import_lessons_file'          => trailingslashit(get_template_directory()) . '/inc/Import/Demo/learnpress/socialv-lessons.xml',
				'lp_local_import_orders_file'           => trailingslashit(get_template_directory()) . '/inc/Import/Demo/learnpress/socialv-orders.xml',
				'lp_local_import_questions_bank_file'   => trailingslashit(get_template_directory()) . '/inc/Import/Demo/learnpress/socialv-question-bank.xml',
				'lp_local_import_quizzes_file'          => trailingslashit(get_template_directory()) . '/inc/Import/Demo/learnpress/socialv-quizzes.xml'
			);
			$import_images += array(
				'lp_local_import_image_file'            => trailingslashit(get_template_directory()) . '/inc/Import/Demo/learnpress/socialv-courses-images.xml',
			);
		}

		if (class_exists('GamiPress')) {
			$import_datas += array(
				'gp_local_import_point-type_file'         => trailingslashit(get_template_directory()) . '/inc/Import/Demo/gamipress/socialv-point-types.xml',
				'gp_local_import_achievments_file'         => trailingslashit(get_template_directory()) . '/inc/Import/Demo/gamipress/socialv-achievments.xml',
				'gp_local_import_awards_file'              => trailingslashit(get_template_directory()) . '/inc/Import/Demo/gamipress/socialv-awards.xml',
				'gp_local_import_badges_file'              => trailingslashit(get_template_directory()) . '/inc/Import/Demo/gamipress/socialv-badges.xml',
				'gp_local_import_deducations_file'         => trailingslashit(get_template_directory()) . '/inc/Import/Demo/gamipress/socialv-deducations.xml',
				'gp_local_import_levels_file'              => trailingslashit(get_template_directory()) . '/inc/Import/Demo/gamipress/socialv-levels.xml',
				'gp_local_import_rank-requirments_file'    => trailingslashit(get_template_directory()) . '/inc/Import/Demo/gamipress/socialv-rank-requirments.xml',
				'gp_local_import_rank-types_file'          => trailingslashit(get_template_directory()) . '/inc/Import/Demo/gamipress/socialv-rank-types.xml',
				'gp_local_import_steps_file'               => trailingslashit(get_template_directory()) . '/inc/Import/Demo/gamipress/socialv-steps.xml'
			);
			$import_images += array(
				'gp_local_import_image_file'               => trailingslashit(get_template_directory()) . '/inc/Import/Demo/gamipress/socialv-gamipress-images.xml',
			);
		}

		if (class_exists('bbPress')) {
			$import_datas += array(
				'bbp_local_import_forums_file'     => trailingslashit(get_template_directory()) . '/inc/Import/Demo/bbpress/socialv-forums.xml',
				'bbp_local_import_replies_file'    => trailingslashit(get_template_directory()) . '/inc/Import/Demo/bbpress/socialv-replies.xml',
				'bbp_local_import_topics_file'     => trailingslashit(get_template_directory()) . '/inc/Import/Demo/bbpress/socialv-topics.xml'
			);
			$import_images += array(
				'bbp_local_import_image_file'      => trailingslashit(get_template_directory()) . '/inc/Import/Demo/bbpress/socialv-bbpress-images.xml',
			);
		}

		if (class_exists('Better_Messages_BuddyPress')) {
			$import_datas += array(
				'bm_local_import_chat_file'    		 => trailingslashit(get_template_directory()) . '/inc/Import/Demo/others/socialv-chat.xml'
			);
			$import_images += array(
				'bm_local_import_chat_image_file'    => trailingslashit(get_template_directory()) . '/inc/Import/Demo/others/socialv-chat-images.xml',
			);
		}

		if (defined('WPCF7_PLUGIN_BASENAME')) {
			$import_datas += array(
				'wpcf7_local_import_contact_form_file'     		=> trailingslashit(get_template_directory()) . '/inc/Import/Demo/others/socialv-contact-forms.xml'
			);
			$import_images += array(
				'wpcf7_local_import_contact_form_image_file'    => trailingslashit(get_template_directory()) . '/inc/Import/Demo/others/socialv-contact-images.xml',
			);
		}

		if (class_exists('MC4WP_MailChimp')) {
			$import_datas += array(
				'MC4WP_local_import_mailchimp_data_file'     => trailingslashit(get_template_directory()) . '/inc/Import/Demo/others/socialv-mailchimp.xml'
			);
			$import_images += array(
				'MC4WP_local_import_mailchimp_image_file'    => trailingslashit(get_template_directory()) . '/inc/Import/Demo/others/socialv-mailchimp-images.xml',
			);
		}

		if (class_exists('Wpstory_Premium')) {
			$import_datas += array(
				'story_local_import_public_story_file'       => trailingslashit(get_template_directory()) . '/inc/Import/Demo/others/socialv-public-stories.xml',
				'story_local_import_story_file'   			 => trailingslashit(get_template_directory()) . '/inc/Import/Demo/others/socialv-stories.xml',
				'story_local_import_story_boxes_file'        => trailingslashit(get_template_directory()) . '/inc/Import/Demo/others/socialv-story-boxes.xml',
				'story_local_import_user_story_file'         => trailingslashit(get_template_directory()) . '/inc/Import/Demo/others/socialv-user-stories.xml'
			);
			$import_images += array(
				'story_local_import_story_image_file'      => trailingslashit(get_template_directory()) . '/inc/Import/Demo/others/socialv-story-image.xml',
			);
		}

		$import_datas += array(
			'AI_local_import_ai_data_file'     => trailingslashit(get_template_directory()) . '/inc/Import/Demo/others/socialv-AI-data.xml',
			'menu_local_import_menu_file'      => trailingslashit(get_template_directory()) . '/inc/Import/Demo/menu/socialv-menus.xml',
		);

		$import_images += array(
			'socialv_icons'      => trailingslashit(get_template_directory()) . '/inc/Import/Demo/icon/svg-main.xml',
		);

		// Merge XML content and images
		$content_file = trailingslashit(get_template_directory()) . '/inc/Import/Demo/socialv-content.xml';
		if (
			(isset($_GET['page']) && in_array($_GET['page'], ['socialv-setup'], true)) ||
			(isset($_POST['page']) && in_array($_POST['page'], ['socialv-setup'], true))
		) {
			$content_file = $this->mergeXmlContentAndImages($import_datas, $import_images);
		}

		return array(
			array(
				'import_file_name'             => esc_html__('All Content', 'socialv'),
				'local_import_file'            => $content_file,
				'local_import_widget_file'     => trailingslashit(get_template_directory()) . '/inc/Import/Demo/socialv-widget.wie',
				'local_import_customizer_file' => trailingslashit(get_template_directory()) . '/inc/Import/Demo/socialv-export.dat',
				'local_import_redux'           => array(
					array(
						'file_path'   		   => trailingslashit(get_template_directory()) . 'inc/Import/Demo/socialv_redux.json',
						'option_name' 		   => 'socialv-options',
					),
				),
				'import_preview_image_url'     => trailingslashit(get_template_directory_uri()) . '/screenshot.png',
				'import_notice' 			   => wp_kses(__('DEMO IMPORT REQUIREMENTS: Memory Limit of 512 MB and max execution time (php time limit) of 300 seconds.<br />Based on your INTERNET SPEED it could take 5 to 25 minutes.', 'socialv'), array('br' => array())),
				'preview_url'                  => 'https://socialv-wordpress.iqonic.design/',
			),
		);
	}

	function mergeXmlContentAndImages($import_datas, $import_images)
	{
		$mergedXml = new DOMDocument();
		$mergedXml->preserveWhiteSpace = false;
		$mergedXml->formatOutput = true;

		$rss = $mergedXml->createElement('rss');
		$rss->setAttribute('version', '2.0');
		$mergedXml->appendChild($rss);

		$skipped_attachments = 0;
		$processed_attachments = 0;
		$total_items = 0;

		foreach ($import_datas as $content_key => $content_file_path) {
			if (!file_exists($content_file_path)) continue;

			$sourceDoc = new DOMDocument();
			$sourceDoc->load($content_file_path);

			$xpath = new \DOMXPath($sourceDoc);
			$xpath->registerNamespace('wp', 'http://wordpress.org/export/1.2/');
			$xpath->registerNamespace('excerpt', 'http://wordpress.org/export/1.2/excerpt/');
			$xpath->registerNamespace('content', 'http://purl.org/rss/1.0/modules/content/');
			$xpath->registerNamespace('dc', 'http://purl.org/dc/elements/1.1/');

			$channelNode = $xpath->query('/rss/channel')->item(0);
			if (!$channelNode) continue;

			// Remove <item> nodes that are unwanted attachments
			$items = $xpath->query('item', $channelNode);
			foreach ($items as $itemNode) {
				$total_items++;
				$skip_attachment_check = false;

				if (in_array($content_file_path, $import_images, true)) {
					$skip_attachment_check = true;
				}

				$keep_attachment_keys = array(
					'require_local_import_image',
					'wc_local_import_image',
					'lp_local_import_image',
					'gp_local_import_image',
					'bbp_local_import_image',
					'story_local_import_story_image',
					'socialv_icons'
				);

				foreach ($keep_attachment_keys as $key_pattern) {
					if (strpos($content_key, $key_pattern) !== false) {
						$skip_attachment_check = true;
						break;
					}
				}

				$is_attachment = false;

				if (!$skip_attachment_check) {
					// Check post type
					$postType = $xpath->query('wp:post_type', $itemNode)->item(0);
					if ($postType && trim($postType->textContent) === 'attachment') {
						$is_attachment = true;
					}

					// Check status + attachment_url
					if (!$is_attachment) {
						$status = $xpath->query('wp:status', $itemNode)->item(0);
						$attachmentUrl = $xpath->query('wp:attachment_url', $itemNode)->item(0);
						if (
							$status && trim($status->textContent) === 'inherit' &&
							$attachmentUrl
						) {
							$is_attachment = true;
						}
					}

					// Check for image in <guid>
					if (!$is_attachment) {
						$guid = $xpath->query('guid', $itemNode)->item(0);
						if ($guid) {
							$guidText = $guid->textContent;
							$image_extensions = array('.jpg', '.jpeg', '.png', '.gif', '.webp', '.svg');
							foreach ($image_extensions as $ext) {
								if (stripos($guidText, $ext) !== false) {
									$is_attachment = true;
									break;
								}
							}
						}
					}
				}

				if ($is_attachment && !$skip_attachment_check) {
					$skipped_attachments++;
					$channelNode->removeChild($itemNode);
				} elseif ($is_attachment && $skip_attachment_check) {
					$processed_attachments++;
				}
			}

			// Import the cleaned-up <channel> into the final merged document
			$importedChannel = $mergedXml->importNode($channelNode, true);
			$rss->appendChild($importedChannel);
		}

		// Process image-only files and append <item> directly under <rss>
		if (!empty($import_images)) {
			$insertedSpacer = false;

			foreach ($import_images as $image_key => $image_file_path) {
				if (!file_exists($image_file_path)) continue;

				$imageXml = new DOMDocument();
				$imageXml->load($image_file_path);

				$imageXpath = new \DOMXPath($imageXml);
				$imageXpath->registerNamespace('wp', 'http://wordpress.org/export/1.2/');

				$imageItems = $imageXpath->query('/rss/channel/item');
				foreach ($imageItems as $imageNode) {
					// Insert spacing before the first image <item>
					if (!$insertedSpacer) {
						$rss->appendChild($mergedXml->createTextNode("\n\n"));
						$insertedSpacer = true;
					}

					$importedItem = $mergedXml->importNode($imageNode, true);

					if ($importedItem instanceof DOMElement) {
						$importedItem->setAttribute('xmlns:dc', 'http://purl.org/dc/elements/1.1/');
						$importedItem->setAttribute('xmlns:content', 'http://purl.org/rss/1.0/modules/content/');
						$importedItem->setAttribute('xmlns:excerpt', 'http://wordpress.org/export/1.2/excerpt/');
						$importedItem->setAttribute('xmlns:wp', 'http://wordpress.org/export/1.2/');
					}

					// Optional: fix <link> and <guid> to match <attachment_url>
					$attachmentUrlNode = $importedItem->getElementsByTagName('wp:attachment_url')->item(0) ??
						$importedItem->getElementsByTagName('attachment_url')->item(0);

					if ($attachmentUrlNode) {
						$attachmentUrl = $attachmentUrlNode->textContent;

						$linkNode = $importedItem->getElementsByTagName('link')->item(0);
						if ($linkNode) {
							$linkNode->nodeValue = $attachmentUrl;
						}

						$guidNode = $importedItem->getElementsByTagName('guid')->item(0);
						if ($guidNode) {
							$guidNode->nodeValue = $attachmentUrl;
						}
					}

					// Append <item> directly to <rss>
					$rss->appendChild($importedItem);
					$rss->appendChild($mergedXml->createTextNode("\n"));
				}
			}
		}



		$merged_xml_path = trailingslashit(get_template_directory()) . '/inc/Import/Demo/socialv-content.xml';
		$mergedXml->save($merged_xml_path);

		return $merged_xml_path;
	}


	function socialv_after_import_setup($selected_import)
	{
		global $wp_filesystem;
		$content    =   '';
		global $wpdb;
		$table = $wpdb->prefix . 'bp_xprofile_fields';
		// // Buddypress Add Field in profile table
		if (get_option('socialv-import-user_fields', true)) {
			global $wpdb;

			$table_name = $wpdb->prefix . 'bp_xprofile_groups';
			$fileds_table_name = $wpdb->prefix . 'bp_xprofile_fields';


			// Inserting 'Social Networks' xprofile group
			$social_data = array(
				'name'        => 'Social Networks',
				'description' => 'Enter details about your social networks',
				'can_delete'  => 1, // Set to 1 if the group can be deleted
			);
			$social_sql = $wpdb->prepare(
				"INSERT INTO $table_name (name, description, can_delete) VALUES (%s, %s, %d)",
				$social_data['name'],
				$social_data['description'],
				$social_data['can_delete']
			);
			$wpdb->query($social_sql);
			$social_id = $wpdb->insert_id;

			// Inserting fields for 'Social Networks' group
			if (!empty($social_id)) {
				$sql = $wpdb->prepare(
					"INSERT INTO $fileds_table_name (group_id, parent_id, type, name, description, is_required, is_default_option, field_order, option_order, order_by, can_delete) VALUES
					(1, 0, 'textarea', 'Biography', '', 0, 0, 5, 0, 'custom', 1),
					(1, 0, 'textbox', 'Website', '', 0, 0, 4, 0, 'custom', 1),
					(1, 0, 'datebox', 'Birthdate', '', 1, 0, 2, 0, '', 1),
					(%d, 0, 'textbox', 'Facebook', 'Put your Facebook profile link here', 0, 0, 0, 0, '', 1),
					(%d, 0, 'textbox', 'Twitter', 'Put your Twitter profile link here', 0, 0, 1, 0, '', 1),
					(%d, 0, 'textbox', 'Dribbble', 'Put your Dribbble profile link here', 0, 0, 2, 0, '', 1),
					(%d, 0, 'textbox', 'Behance', 'Put your Behance profile link here', 0, 0, 3, 0, '', 1),
					(%d, 0, 'textbox', 'YouTube', 'Put your YouTube profile link here', 0, 0, 4, 0, '', 1),
					(%d, 0, 'textbox', 'Instagram', 'Put your Instagram profile link here', 0, 0, 5, 0, '', 1)",
					$social_id,
					$social_id,
					$social_id,
					$social_id,
					$social_id,
					$social_id
				);
				$wpdb->query($sql);
			}

			// Inserting 'Hobbies And Interest' xprofile group
			$hobby_data = array(
				'name'        => 'Hobbies And Interest',
				'description' => 'Enter details about your hobbies',
				'can_delete'  => 1, // Set to 1 if the group can be deleted
			);
			$hobby_sql = $wpdb->prepare(
				"INSERT INTO $table_name (name, description, can_delete) VALUES (%s, %s, %d)",
				$hobby_data['name'],
				$hobby_data['description'],
				$hobby_data['can_delete']
			);
			$wpdb->query($hobby_sql);
			$hobby_id = $wpdb->insert_id;

			// Inserting fields for 'Hobbies And Interest' group
			if (!empty($hobby_id)) {
				$sql = $wpdb->prepare(
					"INSERT INTO $fileds_table_name (group_id, parent_id, type, name, description, is_required, is_default_option, field_order, option_order, order_by, can_delete) VALUES
					(%d, 0, 'textarea', 'Movies', 'Write your favorite movies name here.', 0, 0, 3, 0, 'custom', 1),
					(%d, 0, 'textbox', 'Games', 'Write your others activities.', 0, 0, 4, 0, '', 1),
					(%d, 0, 'textarea', 'My Hobbies', 'Write here your favorite hobbies', 0, 0, 0, 0, '', 1),
					(%d, 0, 'textarea', 'Music Brands', 'Write here your favorite brands name or artists', 0, 0, 1, 0, '', 1),
					(%d, 0, 'textarea', 'Tv Shows', 'Write your favorite Tv Shows name here.', 0, 0, 2, 0, '', 1)",
					$hobby_id,
					$hobby_id,
					$hobby_id,
					$hobby_id,
					$hobby_id
				);
				$wpdb->query($sql);
			}


			update_option('socialv-import-user_fields', true);
		}

		// Assign menus to their locations based on slug.
		$locations = get_theme_mod('nav_menu_locations'); // registered menu locations in theme
		$menus = wp_get_nav_menus(); // registered menus

		if ($menus) {
			foreach ($menus as $menu) { // assign menus to theme locations
				$menu_slug = $menu->slug;

				// Assign menu to location based on slug
				switch ($menu_slug) {
					case 'main-menu':
						$locations['primary'] = $menu->term_id;
						break;
					case 'side-setting-menu':
						$locations['side_menu'] = $menu->term_id;
						break;
						// Add more cases for other menu slugs if needed
				}
			}
		}

		// Update theme mods with menu locations
		set_theme_mod('nav_menu_locations', $locations);

		//set menu content
		$menu_item = get_posts([
			'post_type' => 'nav_menu_item',
			'post_status' => 'publish',
			'numberposts' => -1,
		]);
		foreach ($menu_item as $key => $value) {
			wp_update_post(
				array(
					'ID' => $value->ID,
					'post_content' => $value->post_content,
				)
			);
		}


		$blog_page_id  = get_page_by_path('blog');
		update_option('show_on_front', 'page');
		update_option('page_for_posts', $blog_page_id->ID);

		require_once(ABSPATH . '/wp-admin/includes/file.php');
		WP_Filesystem();
		//post-types selection for edit with elementor option
		$enable_edit_with_elementor = [
			"post",
			"page",
		];
		update_option('elementor_cpt_support', $enable_edit_with_elementor);
		add_option('elementor_experiment-e_font_icon_svg', 'inactive');

		// Live Chat Setting
		if (class_exists('BP_Better_Messages')) {
			do_action('demo_import_messages_settings');
		}

		// Media Post Setting
		if (class_exists('mediapress')) {
			do_action('demo_import_media_settings');
		}

		// WooCommerce Setting
		$woof_setting_file =  trailingslashit(get_template_directory()) . 'inc/Import/Demo/socialv-woof-setting.json';

		if (file_exists($woof_setting_file)) {
			$content =  $wp_filesystem->get_contents($woof_setting_file);
			if (!empty($content)) {
				$woof_options = json_decode($content, true);
				foreach ($woof_options as $option_name => $option_data) {
					if (is_serialized($option_data)) {
						$option_data = unserialize($option_data);
					}
					update_option($option_name, $option_data);
				}
			}
		}

		update_option('woosq_button_type', 'link');
		update_option('woosq_button_position', '0');


		// Get shop page by slug
		$shop_page = get_page_by_path('shop-2');
		// Check if the shop page exists
		if ($shop_page) {
			update_option('woocommerce_shop_page_id', $shop_page->ID);
		}

		$checkout_page = get_page_by_path('checkout-2');
		if ($checkout_page) {
			update_option('woocommerce_checkout_page_id', $checkout_page->ID);
		}

		$myaccount_page = get_page_by_path('my-account-2');
		if ($myaccount_page) {
			update_option('woocommerce_myaccount_page_id', $myaccount_page->ID);
		}

		// Get wishlist page by slug
		$wishlist_page = get_page_by_path('wishlist-2');

		// Check if the wishlist page exists
		if ($wishlist_page) {
			// Update WooCommerce wishlist page ID option
			update_option('woocommerce_wishlist_page_id', $wishlist_page->ID);
		}

		//buddypress dublicate page delete
		if (class_exists('BuddyPress')) {
			$front_page_id = bp_core_get_directory_page_ids();
			update_option('page_on_front', $front_page_id['activity']);
		}

		//set redirection page after login
		if (class_exists('ReduxFramework')) {
			$socialv_option = get_option('socialv-options');
			$socialv_option['display_after_login_page'] = $front_page_id['activity'];
			update_option('socialv-options', $socialv_option);
		}

		if (class_exists('Wpstory_Premium'))
			$this->socialv_save_wp_story_options();
	}
	public function socialv_save_wp_story_options()
	{
		$new_value = array(
			'render' => 'client',
			'style' => 'facebook',
			'story_reports' => 1,
			'full_screen' => true,
			'video_silent' => 1,
			'routing' => 1,
			'story_timer' => 0,
			'single_stories_timer' => 1,
			'public_stories_timer' => 1,
			'story_time_value' => 2,
			'default_story_duration' => 3,
			'allowed_image_types' => '',
			'allowed_video_types' => '',
			'user_publish_status' => 'publish',
			'user_deleting_status' => 'draft',
			'user_single_story_limit' => 24,
			'user_public_story_limit' => 31,
			'user_public_story_item_limit' => 10,
			'max_file_size' => 10,
			'allow_link' => 1,
			'story_insights' => 1,
			'image_compression' => 1,
			'image_compression_level' => 0.6,
			'image_max_width' => 1080,
			'image_max_height' => 1920,
			'story_background_type' => 'gradient',
			'story_bg' => '',
			'story_gradient' => array(
				'color-1' => '#647dee',
				'color-2' => '#7f53ac'
			),
			'full_size_media' => 1,
			'swipe_button' => 1,
			'button_background_type' => 'normal',
			'button_bg' => 'rgba(0, 0, 0, 0.5)',
			'button_gradient' => array(
				'color-1' => '#647dee',
				'color-2' => '#7f53ac'
			),
			'text_color' => '#ffffff',
			'font_size' => 16,
			'button_padding' => array(
				'width' => 12,
				'height' => 24
			),
			'button_radius' => 24,
			'cycle_position' => 'auto',
			'cycle_background_type' => 'gradient',
			'cycle_bg' => '#000',
			'cycle_gradient' => array(
				'color-1' => '#ee583f',
				'color-2' => '#bd3381'
			),
			'title_color' => '#000',
			'uncropped_titles' => '',
			'buddypress_integration' => 1,
			'buddypress_single_stories' => 1,
			'buddypress_public_stories' => 1,
			'buddypress_users_activities' => 1,
			'buddypress_activities_form' => 1,
			'buddypress_activities_login_url' => '',
			'bbpress_integration' => '',
			'peepso_integration' => 1,
			'peepso_placement' => 'auto',
			'peepso_single_stories' => 1,
			'peepso_public_stories' => 1,
			'enable_web_stories' => 1,
			'clean_on_delete' => '',
			'posts_story_options' => 1,
			'opener' => '',
			'changelog' => ''
		);

		update_option('wp-story-premium', $new_value);
	}
}
