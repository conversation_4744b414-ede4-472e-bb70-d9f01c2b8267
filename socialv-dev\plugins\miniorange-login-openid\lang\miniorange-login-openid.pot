#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: miniorange-login-openid\n"
"POT-Creation-Date: 2020-01-14 17:37+0530\n"
"PO-Revision-Date: 2017-08-12 19:12+0530\n"
"Last-Translator: \n"
"Language-Team: miniOrange\n"
"Language: en_US\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: Poedit 2.2.4\n"
"X-Poedit-Basepath: ..\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Poedit-KeywordsList: mo_sl;__;_e\n"
"X-Poedit-SearchPath-0: .\n"

#: class-mo-openid-login-widget.php:42
msgid "Login using Social Apps like Google, Facebook, LinkedIn, Microsoft, Instagram."
msgstr ""

#: class-mo-openid-login-widget.php:542 class-mo-openid-login-widget.php:550
msgid "Logout"
msgstr ""

#: class-mo-openid-login-widget.php:1357
msgid "Share using horizontal widget. Lets you share with Social Apps like Google, Facebook, LinkedIn, Pinterest, Reddit."
msgstr ""

#: class-mo-openid-login-widget.php:1402
msgid "Share using a vertical floating widget. Lets you share with Social Apps like Google, Facebook, LinkedIn, Pinterest, Reddit."
msgstr ""

#: miniorange_openid_sso_settings.php:97
msgid "Login with"
msgstr ""

#: miniorange_openid_sso_settings.php:266
msgid "Configure OpenID"
msgstr ""

#: miniorange_openid_sso_settings_page.php:55
#: miniorange_openid_sso_settings_page.php:483
#: miniorange_openid_sso_settings_page.php:626
#: miniorange_openid_sso_settings_page.php:811
msgid "miniOrange Social Login"
msgstr ""

#: miniorange_openid_sso_settings_page.php:56
#: miniorange_openid_sso_settings_page.php:484
#: miniorange_openid_sso_settings_page.php:627
#: miniorange_openid_sso_settings_page.php:812
#: view/profile/mo_openid_profile.php:23 view/profile/mo_openid_profile.php:51
msgid "Privacy Policy"
msgstr ""

#: miniorange_openid_sso_settings_page.php:57
#: miniorange_openid_sso_settings_page.php:485
#: miniorange_openid_sso_settings_page.php:628
#: miniorange_openid_sso_settings_page.php:813
msgid "FAQs"
msgstr ""

#: miniorange_openid_sso_settings_page.php:58
#: miniorange_openid_sso_settings_page.php:486
#: miniorange_openid_sso_settings_page.php:629
#: miniorange_openid_sso_settings_page.php:814
msgid "Forum"
msgstr ""

#: miniorange_openid_sso_settings_page.php:59
#: miniorange_openid_sso_settings_page.php:98
#: miniorange_openid_sso_settings_page.php:487
#: miniorange_openid_sso_settings_page.php:630
msgid "Add On"
msgstr ""

#: miniorange_openid_sso_settings_page.php:60
#: miniorange_openid_sso_settings_page.php:488
#: miniorange_openid_sso_settings_page.php:631
#: miniorange_openid_sso_settings_page.php:816
msgid "Upgrade Now"
msgstr ""

#: miniorange_openid_sso_settings_page.php:61
msgid "What's new in miniOrange"
msgstr ""

#: miniorange_openid_sso_settings_page.php:63
msgid "Rate us"
msgstr ""

#: miniorange_openid_sso_settings_page.php:66
msgid "Restart Tour"
msgstr ""

#: miniorange_openid_sso_settings_page.php:81
#: miniorange_openid_sso_settings_page.php:500
#: miniorange_openid_sso_settings_page.php:643
#: miniorange_openid_sso_settings_page.php:826
msgid "miniOrange"
msgstr ""

#: miniorange_openid_sso_settings_page.php:84
msgid "Configure Apps"
msgstr ""

#: miniorange_openid_sso_settings_page.php:85
msgid "Customise Social Login Icons"
msgstr ""

#: miniorange_openid_sso_settings_page.php:86
#: view/disp_options/mo_openid_dispopt.php:98
#: view/disp_options/mo_openid_dispopt.php:131
#: view/soc_com/com_Enable/mo_openid_comm_enable.php:10
#: view/soc_com/com_Enable/mo_openid_comm_enable.php:12
#: view/soc_com/com_display_options/mo_openid_comm_disp_opt.php:41
#: view/soc_sha/disp_shropt/mo_openid_disp_shropt.php:214
msgid "Display Options"
msgstr ""

#: miniorange_openid_sso_settings_page.php:87
msgid "Redirect Options"
msgstr ""

#: miniorange_openid_sso_settings_page.php:88
msgid "Registration"
msgstr ""

#: miniorange_openid_sso_settings_page.php:89
msgid "GDPR"
msgstr ""

#: miniorange_openid_sso_settings_page.php:89
#: miniorange_openid_sso_settings_page.php:90
#: miniorange_openid_sso_settings_page.php:91
#: miniorange_openid_sso_settings_page.php:93
#: miniorange_openid_sso_settings_page.php:94
#: miniorange_openid_sso_settings_page.php:95
#: miniorange_openid_sso_settings_page.php:96
#: view/disp_options/mo_openid_dispopt.php:28
#: view/disp_options/mo_openid_dispopt.php:46
#: view/disp_options/mo_openid_dispopt.php:94
#: view/premium_features/mo_openid_prem_feat.php:90
#: view/premium_features/mo_openid_prem_feat.php:105
#: view/premium_features/mo_openid_prem_feat.php:121
#: view/premium_features/mo_openid_prem_feat.php:135
#: view/premium_features/mo_openid_prem_feat.php:149
#: view/premium_features/mo_openid_prem_feat.php:160
#: view/profile/mo_openid_profile.php:84
msgid "PRO"
msgstr ""

#: miniorange_openid_sso_settings_page.php:90
#: view/restrict_domain/mo_openid_restrict_dom.php:11
#: view/restrict_domain/mo_openid_restrict_dom.php:41
msgid "Domain Restriction"
msgstr ""

#: miniorange_openid_sso_settings_page.php:91
#: view/link_social_account/mo_openid_Acclink.php:72
msgid "Link Social Account"
msgstr ""

#: miniorange_openid_sso_settings_page.php:92
#: view/profile_completion/mo_openid_prof_comp.php:60
msgid "Profile Completion"
msgstr ""

#: miniorange_openid_sso_settings_page.php:93
#: view/email_settings/mo_openid_set_email.php:74
msgid "Email Notification"
msgstr ""

#: miniorange_openid_sso_settings_page.php:94
msgid "Recaptcha"
msgstr ""

#: miniorange_openid_sso_settings_page.php:95
#: view/premium_features/mo_openid_prem_feat.php:159
msgid "Premium Features"
msgstr ""

#: miniorange_openid_sso_settings_page.php:96
#: view/integration/mo_openid_integrate.php:116
msgid "Integrations"
msgstr ""

#: miniorange_openid_sso_settings_page.php:97
#: miniorange_openid_sso_settings_page.php:519
#: miniorange_openid_sso_settings_page.php:650
msgid "Shortcodes"
msgstr ""

#: miniorange_openid_sso_settings_page.php:99
msgid "User Profile"
msgstr ""

#: miniorange_openid_sso_settings_page.php:505
msgid ""
"Select\n"
"                    Social Apps"
msgstr ""

#: miniorange_openid_sso_settings_page.php:509
#: miniorange_openid_sso_settings_page.php:648
msgid "Customization"
msgstr ""

#: miniorange_openid_sso_settings_page.php:512
msgid ""
"Social\n"
"                    Share Counts"
msgstr ""

#: miniorange_openid_sso_settings_page.php:516
msgid ""
"Display\n"
"                    Option"
msgstr ""

#: miniorange_openid_sso_settings_page.php:646
#: view/soc_com/com_Enable/mo_openid_comm_enable.php:10
#: view/soc_com/com_select_app/mo_openid_comm_select_app.php:20
msgid "Select Applications"
msgstr ""

#: miniorange_openid_sso_settings_page.php:647
msgid "Display options"
msgstr ""

#: miniorange_openid_sso_settings_page.php:649
msgid "Enable and Add Social Comments"
msgstr ""

#: miniorange_openid_sso_settings_page.php:815 view/faq/mo_openid_faq.php:108
#: view/faq/mo_openid_faq.php:117
msgid "Social Login"
msgstr ""

#: miniorange_openid_sso_settings_page.php:829
#: view/add_on/custom_registration_form.php:9
#: view/mo_new/mo_openid_whats_new.php:65
msgid "Custom Registration Form"
msgstr ""

#: miniorange_openid_sso_settings_page.php:830
msgid "Go to Social Login"
msgstr ""

#: miniorange_openid_sso_settings_page.php:831
msgid "Licensing Plans"
msgstr ""

#: view/add_on/custom_registration_form.php:21
msgid ""
"Custom Registration Form Add-On helps you to integrate details of new as well as existing users. You\n"
"                        can add as many fields as you want including the one which are returned by\n"
"                        social sites at time of registration"
msgstr ""

#: view/add_on/custom_registration_form.php:46
msgid "Customization Fields"
msgstr ""

#: view/add_on/custom_registration_form.php:49
msgid "Enable Auto Field Registration Form"
msgstr ""

#: view/add_on/custom_registration_form.php:71
msgid "Registration page link"
msgstr ""

#: view/add_on/custom_registration_form.php:77
msgid "Existing Field"
msgstr ""

#: view/add_on/custom_registration_form.php:78
#: view/add_on/custom_registration_form.php:145
msgid "Field"
msgstr ""

#: view/add_on/custom_registration_form.php:79
#: view/add_on/custom_registration_form.php:150
msgid "Custom name"
msgstr ""

#: view/add_on/custom_registration_form.php:80
#: view/add_on/custom_registration_form.php:146
#: view/add_on/custom_registration_form.php:151
msgid "Field Type"
msgstr ""

#: view/add_on/custom_registration_form.php:81
msgid "Field Options"
msgstr ""

#: view/add_on/custom_registration_form.php:82
#: view/add_on/custom_registration_form.php:156
msgid "Required Field"
msgstr ""

#: view/add_on/custom_registration_form.php:94
msgid "Select Field"
msgstr ""

#: view/add_on/custom_registration_form.php:102
msgid "Select Type"
msgstr ""

#: view/add_on/custom_registration_form.php:109
msgid "No"
msgstr ""

#: view/add_on/custom_registration_form.php:124
#: view/integration/mo_openid_integrate.php:29
msgid "Cancel"
msgstr ""

#: view/add_on/custom_registration_form.php:129
msgid "Instructions to setup"
msgstr ""

#: view/add_on/custom_registration_form.php:132
msgid "Create a page and use shortcode"
msgstr ""

#: view/add_on/custom_registration_form.php:133
msgid "where you want your form to be displayed"
msgstr ""

#: view/add_on/custom_registration_form.php:135
msgid ""
"Copy the page link and paste it in the above field <b>Registration page\n"
"                                                    link"
msgstr ""

#: view/add_on/custom_registration_form.php:138
msgid "If you have any existing wp_usermeta field then enter that field's name in"
msgstr ""

#: view/add_on/custom_registration_form.php:139
msgid ""
"Existing\n"
"                                                    Field"
msgstr ""

#: view/add_on/custom_registration_form.php:140
msgid "column. For example, if you are saving"
msgstr ""

#: view/add_on/custom_registration_form.php:140
msgid "First Name"
msgstr ""

#: view/add_on/custom_registration_form.php:140
#: view/add_on/custom_registration_form.php:156
msgid "as"
msgstr ""

#: view/add_on/custom_registration_form.php:141
msgid "fname"
msgstr ""

#: view/add_on/custom_registration_form.php:142
msgid ""
"in wp_usermeta field then enter fname in Existing Field\n"
"                                                column."
msgstr ""

#: view/add_on/custom_registration_form.php:145
msgid "Select field name under the "
msgstr ""

#: view/add_on/custom_registration_form.php:145
msgid "dropdown"
msgstr ""

#: view/add_on/custom_registration_form.php:146
msgid "If selected field is other than custom, then"
msgstr ""

#: view/add_on/custom_registration_form.php:146
msgid ""
"will\n"
"                                                automatically be"
msgstr ""

#: view/add_on/custom_registration_form.php:147
msgid "Textbox"
msgstr ""

#: view/add_on/custom_registration_form.php:147
msgid "and there is no need to enter"
msgstr ""

#: view/add_on/custom_registration_form.php:147
msgid ""
"Custom\n"
"                                                    name"
msgstr ""

#: view/add_on/custom_registration_form.php:148 view/faq/mo_openid_faq.php:81
#: view/profile/mo_openid_profile.php:51
msgid "and"
msgstr ""

#: view/add_on/custom_registration_form.php:148
msgid "Field options"
msgstr ""

#: view/add_on/custom_registration_form.php:150
msgid "If selected field is custom, then enter"
msgstr ""

#: view/add_on/custom_registration_form.php:151
#: view/add_on/custom_registration_form.php:156
msgid "Select"
msgstr ""

#: view/add_on/custom_registration_form.php:151
msgid "if selected"
msgstr ""

#: view/add_on/custom_registration_form.php:151
msgid "is"
msgstr ""

#: view/add_on/custom_registration_form.php:152
msgid "Checkbox"
msgstr ""

#: view/add_on/custom_registration_form.php:152
msgid "or"
msgstr ""

#: view/add_on/custom_registration_form.php:152
msgid "Dropdown"
msgstr ""

#: view/add_on/custom_registration_form.php:152
#: view/add_on/custom_registration_form.php:153
msgid ""
"Field\n"
"                                                    Options"
msgstr ""

#: view/add_on/custom_registration_form.php:153
msgid "seprated by semicolon "
msgstr ""

#: view/add_on/custom_registration_form.php:153
msgid "otherwise leave"
msgstr ""

#: view/add_on/custom_registration_form.php:154
msgid "blank."
msgstr ""

#: view/add_on/custom_registration_form.php:156
msgid "Yes"
msgstr ""

#: view/add_on/custom_registration_form.php:156
msgid ""
"if you want to make that field\n"
"                                                compulsory for user"
msgstr ""

#: view/add_on/custom_registration_form.php:159
msgid "If you want to add more than 1 fields at a time click on"
msgstr ""

#: view/add_on/custom_registration_form.php:160
msgid "Last click on"
msgstr ""

#: view/add_on/custom_registration_form.php:160
msgid "Save"
msgstr ""

#: view/add_on/custom_registration_form.php:160
msgid "button"
msgstr ""

#: view/add_on/custom_registration_form.php:192
msgid "Social Login Add On"
msgstr ""

#: view/customise_social_icons/mo_openid_cust_icons.php:13
#: view/soc_sha/cust_text/mo_openid_cust_shricon.php:28
msgid "Shape"
msgstr ""

#: view/customise_social_icons/mo_openid_cust_icons.php:14
#: view/soc_sha/cust_text/mo_openid_cust_shricon.php:39
msgid "Round"
msgstr ""

#: view/customise_social_icons/mo_openid_cust_icons.php:17
#: view/soc_sha/cust_text/mo_openid_cust_shricon.php:45
msgid "Rounded Edges"
msgstr ""

#: view/customise_social_icons/mo_openid_cust_icons.php:21
#: view/soc_sha/cust_text/mo_openid_cust_shricon.php:50
msgid "Square"
msgstr ""

#: view/customise_social_icons/mo_openid_cust_icons.php:24
msgid "Long Button"
msgstr ""

#: view/customise_social_icons/mo_openid_cust_icons.php:31
#: view/soc_sha/cust_text/mo_openid_cust_shricon.php:29
msgid "Theme"
msgstr ""

#: view/customise_social_icons/mo_openid_cust_icons.php:34
#: view/soc_sha/cust_text/mo_openid_cust_shricon.php:59
msgid "Default"
msgstr ""

#: view/customise_social_icons/mo_openid_cust_icons.php:40
msgid "Custom background*"
msgstr ""

#: view/customise_social_icons/mo_openid_cust_icons.php:49
msgid "*Custom background:"
msgstr ""

#: view/customise_social_icons/mo_openid_cust_icons.php:49
msgid "This will change the background color of login icons"
msgstr ""

#: view/customise_social_icons/mo_openid_cust_icons.php:52
#: view/soc_sha/cust_text/mo_openid_cust_shricon.php:31
msgid "Size of Icons"
msgstr ""

#: view/customise_social_icons/mo_openid_cust_icons.php:54
msgid "Width:"
msgstr ""

#: view/customise_social_icons/mo_openid_cust_icons.php:57
msgid "Height:"
msgstr ""

#: view/customise_social_icons/mo_openid_cust_icons.php:60
msgid "Curve:"
msgstr ""

#: view/customise_social_icons/mo_openid_cust_icons.php:71
#: view/soc_sha/cust_text/mo_openid_cust_shricon.php:30
msgid "Space between Icons"
msgstr ""

#: view/customise_social_icons/mo_openid_cust_icons.php:79
msgid "Customize Text For Social Login Buttons / Icons"
msgstr ""

#: view/customise_social_icons/mo_openid_cust_icons.php:82
msgid "Select color for customize text:"
msgstr ""

#: view/customise_social_icons/mo_openid_cust_icons.php:90
msgid "Enter text to show above login widget:"
msgstr ""

#: view/customise_social_icons/mo_openid_cust_icons.php:96
msgid "Enter text to show on your login buttons:"
msgstr ""

#: view/customise_social_icons/mo_openid_cust_icons.php:103
msgid "Customize Text to show user after Login"
msgstr ""

#: view/customise_social_icons/mo_openid_cust_icons.php:106
msgid "Enter text to show before the logout link. Use ##username## to display current username:"
msgstr ""

#: view/customise_social_icons/mo_openid_cust_icons.php:113
msgid "Enter text to show as logout link:"
msgstr ""

#: view/customise_social_icons/mo_openid_cust_icons.php:215
msgid "Customize Login Icons"
msgstr ""

#: view/disp_options/mo_openid_dispopt.php:10
msgid "Select the options where you want to display the social login icons"
msgstr ""

#: view/disp_options/mo_openid_dispopt.php:11
msgid "Default Login Form [wp-admin]"
msgstr ""

#: view/disp_options/mo_openid_dispopt.php:16
msgid "Default Registration Form"
msgstr ""

#: view/disp_options/mo_openid_dispopt.php:22
msgid "Comment Form"
msgstr ""

#: view/disp_options/mo_openid_dispopt.php:26
msgid "Don't find your login page in above options use"
msgstr ""

#: view/disp_options/mo_openid_dispopt.php:26
msgid "to display social icons or"
msgstr ""

#: view/disp_options/mo_openid_dispopt.php:26
msgid "Contact Us"
msgstr ""

#: view/disp_options/mo_openid_dispopt.php:28
msgid "BuddyPress display options"
msgstr ""

#: view/disp_options/mo_openid_dispopt.php:30
msgid "Before BuddyPress Registration Form"
msgstr ""

#: view/disp_options/mo_openid_dispopt.php:35
msgid "Before BuddyPress Account Details"
msgstr ""

#: view/disp_options/mo_openid_dispopt.php:40
msgid "After BuddyPress Registration Form"
msgstr ""

#: view/disp_options/mo_openid_dispopt.php:46
msgid "Woocommerce display options"
msgstr ""

#: view/disp_options/mo_openid_dispopt.php:48
msgid "Before WooCommerce Login Form"
msgstr ""

#: view/disp_options/mo_openid_dispopt.php:53
msgid "Before 'Remember Me' of WooCommerce Login Form"
msgstr ""

#: view/disp_options/mo_openid_dispopt.php:57
msgid "After WooCommerce Login Form"
msgstr ""

#: view/disp_options/mo_openid_dispopt.php:62
msgid "Before WooCommerce Registration Form"
msgstr ""

#: view/disp_options/mo_openid_dispopt.php:66
msgid "Before 'Register button' of WooCommerce Registration Form"
msgstr ""

#: view/disp_options/mo_openid_dispopt.php:71
msgid "After WooCommerce Registration Form"
msgstr ""

#: view/disp_options/mo_openid_dispopt.php:76
msgid "Before WooCommerce Checkout Form"
msgstr ""

#: view/disp_options/mo_openid_dispopt.php:81
msgid "After WooCommerce Checkout Form"
msgstr ""

#: view/disp_options/mo_openid_dispopt.php:87
msgid "Display miniOrange logo with social login icons on selected form"
msgstr ""

#: view/disp_options/mo_openid_dispopt.php:94
msgid "These features are available in premium version only. To know more about the premium plugin "
msgstr ""

#: view/disp_options/mo_openid_dispopt.php:94
msgid "click here"
msgstr ""

#: view/disp_options/mo_openid_dispopt.php:95
msgid "Add Login Icons"
msgstr ""

#: view/disp_options/mo_openid_dispopt.php:98
msgid "You can add login icons in the following areas from"
msgstr ""

#: view/disp_options/mo_openid_dispopt.php:98
msgid "For other areas(widget areas), use Login widget"
msgstr ""

#: view/disp_options/mo_openid_dispopt.php:100
msgid "Default Login Form: This option places login icons below the default login form on wp-login"
msgstr ""

#: view/disp_options/mo_openid_dispopt.php:101
msgid "Default Registration Form: This option places login icons below the default registration form"
msgstr ""

#: view/disp_options/mo_openid_dispopt.php:103
msgid "Comment Form: This option places login icons above the comment section of all your posts"
msgstr ""

#: view/disp_options/mo_openid_dispopt.php:108
msgid "Add Login Icons as Widget"
msgstr ""

#: view/disp_options/mo_openid_dispopt.php:111
msgid ""
"Go to Widgets. Among the available widgets you\n"
"                            will find miniOrange Social Login Widget, drag it to the widget area where\n"
"                            you want it to appear"
msgstr ""

#: view/disp_options/mo_openid_dispopt.php:114
msgid "Now logout and go to your site. You will see Social Login icon for which you enabled login."
msgstr ""

#: view/disp_options/mo_openid_dispopt.php:115
msgid "Click that app icon and login with your existing app account to wordpress"
msgstr ""

#: view/disp_options/mo_openid_dispopt.php:119
msgid "Using Shortcode"
msgstr ""

#: view/email_settings/mo_openid_set_email.php:11
msgid "Send mail to Admin"
msgstr ""

#: view/email_settings/mo_openid_set_email.php:21
msgid "*NOTE: This feature requires SMTP to be setup."
msgstr ""

#: view/email_settings/mo_openid_set_email.php:22
msgid "Enable Email Notification to Admin - on User Registration*"
msgstr ""

#: view/email_settings/mo_openid_set_email.php:26
msgid "If you want to send Email Notification to multiple admins, enter emails of all admins here:"
msgstr ""

#: view/email_settings/mo_openid_set_email.php:29
#: view/email_settings/mo_openid_set_email.php:57
msgid "Email Subject:"
msgstr ""

#: view/email_settings/mo_openid_set_email.php:40
msgid "Send mail to User"
msgstr ""

#: view/email_settings/mo_openid_set_email.php:51
msgid "*NOTE: This feature requires SMTP to be setup"
msgstr ""

#: view/email_settings/mo_openid_set_email.php:52
msgid "Email Notification to User on User Registration"
msgstr ""

#: view/faq/mo_openid_faq.php:9
msgid "If you are unable to open any section, press CTRL + F5 to clear cache"
msgstr ""

#: view/faq/mo_openid_faq.php:10
msgid "Site Issue"
msgstr ""

#: view/faq/mo_openid_faq.php:12
msgid "I installed the plugin and my website stopped working. How can I recover my site?"
msgstr ""

#: view/faq/mo_openid_faq.php:14
msgid "There must have been a server error on your website. To get your website back online:"
msgstr ""

#: view/faq/mo_openid_faq.php:16
msgid "Open FTP access and look for plugins folder under wp-content."
msgstr ""

#: view/faq/mo_openid_faq.php:17
msgid "Change the extension folder name miniorange-login-openid to miniorange-login-openid"
msgstr ""

#: view/faq/mo_openid_faq.php:18
msgid "Check your website. It must have started working"
msgstr ""

#: view/faq/mo_openid_faq.php:19
msgid "Change the folder name back to miniorange-login-openid."
msgstr ""

#: view/faq/mo_openid_faq.php:22 view/faq/mo_openid_faq.php:35
#: view/faq/mo_openid_faq.php:52
msgid "For any further queries, please submit a query on right hand side in our"
msgstr ""

#: view/faq/mo_openid_faq.php:22 view/faq/mo_openid_faq.php:35
#: view/faq/mo_openid_faq.php:52
msgid "Support Section"
msgstr ""

#: view/faq/mo_openid_faq.php:29
msgid "Change email"
msgstr ""

#: view/faq/mo_openid_faq.php:31
msgid "I want to change the email address with which I access my account. How can I do that?"
msgstr ""

#: view/faq/mo_openid_faq.php:33
msgid "You will have to register in miniOrange again with your new email id."
msgstr ""

#: view/faq/mo_openid_faq.php:34
msgid "Please deactivate and activate the plugin by going to"
msgstr ""

#: view/faq/mo_openid_faq.php:34
msgid "Plugins -> Installed Plugins"
msgstr ""

#: view/faq/mo_openid_faq.php:34
msgid "and then go to the Social Login Plugin to register again. This will enable you to access miniOrange dashboard with new email address."
msgstr ""

#: view/faq/mo_openid_faq.php:42
msgid "cURL"
msgstr ""

#: view/faq/mo_openid_faq.php:44
msgid "How to enable PHP cURL extension? (Pre-requisite)"
msgstr ""

#: view/faq/mo_openid_faq.php:46
msgid "cURL is enabled by default but in case you have disabled it, follow the steps to enable it"
msgstr ""

#: view/faq/mo_openid_faq.php:48
msgid "Open php.ini(it's usually in /etc/ or in php folder on the server)."
msgstr ""

#: view/faq/mo_openid_faq.php:49
msgid "Search for extension=php_curl.dll. Uncomment it by removing the semi-colon( ; ) in front of it."
msgstr ""

#: view/faq/mo_openid_faq.php:50
msgid "Restart the Apache Server."
msgstr ""

#: view/faq/mo_openid_faq.php:55
msgid "I am getting error - curl_setopt(): CURLOPT_FOLLOWLOCATION cannot be activated when an open_basedir is set"
msgstr ""

#: view/faq/mo_openid_faq.php:57
msgid "Just setsafe_mode = Off in your php.ini file (it's usually in /etc/ on the server). If that's already off, then look around for the open_basedir in the php.ini file, and change it to open_basedir = ."
msgstr ""

#: view/faq/mo_openid_faq.php:65
msgid "OTP and Forgot Password"
msgstr ""

#: view/faq/mo_openid_faq.php:67
msgid "I did not recieve OTP. What should I do?"
msgstr ""

#: view/faq/mo_openid_faq.php:69
msgid "The OTP is sent as an email to your email address with which you have registered with miniOrange. If you can't see the email from miniOrange in your mails, please make sure to check your SPAM folder."
msgstr ""

#: view/faq/mo_openid_faq.php:69
msgid "If you don't see an email even in SPAM folder, please verify your account using your mobile number. You will get an OTP on your mobile number which you need to enter on the page. If none of the above works, please contact us using the Support form on the right."
msgstr ""

#: view/faq/mo_openid_faq.php:72
msgid "After entering OTP, I get Invalid OTP. What should I do?"
msgstr ""

#: view/faq/mo_openid_faq.php:74
msgid "Use the "
msgstr ""

#: view/faq/mo_openid_faq.php:74
msgid "Resend OTP"
msgstr ""

#: view/faq/mo_openid_faq.php:74
msgid "option to get an additional OTP. Plese make sure you did not enter the first OTP you recieved if you selected"
msgstr ""

#: view/faq/mo_openid_faq.php:74
msgid "option to get an additional OTP. Enter the latest OTP since the previous ones expire once you click on Resend OTP."
msgstr ""

#: view/faq/mo_openid_faq.php:74
msgid "If OTP sent on your email address are not working, please verify your account using your mobile number. You will get an OTP on your mobile number which you need to enter on the page. If none of the above works, please contact us using the Support form on the right."
msgstr ""

#: view/faq/mo_openid_faq.php:77
msgid "I forgot the password of my miniOrange account. How can I reset it?"
msgstr ""

#: view/faq/mo_openid_faq.php:79
msgid "There are two cases according to the page you see"
msgstr ""

#: view/faq/mo_openid_faq.php:80 view/faq/mo_openid_faq.php:81
msgid "Login with miniOrange"
msgstr ""

#: view/faq/mo_openid_faq.php:80
msgid "screen: You should click on"
msgstr ""

#: view/faq/mo_openid_faq.php:80
msgid "forgot password"
msgstr ""

#: view/faq/mo_openid_faq.php:80
msgid "link. You will get your new password on your email address which you have registered with miniOrange . Now you can login with the new password"
msgstr ""

#: view/faq/mo_openid_faq.php:81 view/profile/mo_openid_profile.php:19
msgid "Register with miniOrange"
msgstr ""

#: view/faq/mo_openid_faq.php:81
msgid "screen: Enter your email ID and any random password in "
msgstr ""

#: view/faq/mo_openid_faq.php:81
msgid "password"
msgstr ""

#: view/faq/mo_openid_faq.php:81
msgid "confirm password"
msgstr ""

#: view/faq/mo_openid_faq.php:81
msgid "input box. This will redirect you to"
msgstr ""

#: view/faq/mo_openid_faq.php:81
msgid "screen. Now follow first step"
msgstr ""

#: view/faq/mo_openid_faq.php:89
msgid "Social login"
msgstr ""

#: view/faq/mo_openid_faq.php:91
msgid "How to add login icons to frontend login page?"
msgstr ""

#: view/faq/mo_openid_faq.php:93
msgid "You can add social login icons to frontend login page using our shortcode [miniorange_social_login]. Refer to 'Shortcode' tab to add customizations to Shortcode."
msgstr ""

#: view/faq/mo_openid_faq.php:96
msgid "How can I put social login icons on a page without using widgets?"
msgstr ""

#: view/faq/mo_openid_faq.php:98
msgid "You can add social login icons to any page or custom login page using 'social login shortcode' [miniorange_social_login]. Refer to 'Shortcode' tab to add customizations to Shortcode."
msgstr ""

#: view/faq/mo_openid_faq.php:101
msgid "Social Login icons are not added to login/registration form."
msgstr ""

#: view/faq/mo_openid_faq.php:103
msgid "Your login/registration form may not be wordpress's default login/registration form. In this case you can add social login icons to custom login/registration form using 'social login shortcode' [miniorange_social_login]. Refer to 'Shortcode' tab to add customizations to Shortcode."
msgstr ""

#: view/faq/mo_openid_faq.php:106
msgid "How can I redirect to my blog page after login?"
msgstr ""

#: view/faq/mo_openid_faq.php:108
msgid "You can select one of the options from"
msgstr ""

#: view/faq/mo_openid_faq.php:108
msgid "Redirect URL after login"
msgstr ""

#: view/faq/mo_openid_faq.php:108 view/faq/mo_openid_faq.php:117
msgid "of"
msgstr ""

#: view/faq/mo_openid_faq.php:108 view/faq/mo_openid_faq.php:117
msgid "Display Option"
msgstr ""

#: view/faq/mo_openid_faq.php:108
msgid " section under"
msgstr ""

#: view/faq/mo_openid_faq.php:108
msgid "tab."
msgstr ""

#: view/faq/mo_openid_faq.php:109
#: view/redirect_options/mo_openid_redirect_op.php:13
msgid "Same page where user logged in"
msgstr ""

#: view/faq/mo_openid_faq.php:110
#: view/redirect_options/mo_openid_redirect_op.php:21
msgid "Homepage"
msgstr ""

#: view/faq/mo_openid_faq.php:111
msgid "Account Dsahboard"
msgstr ""

#: view/faq/mo_openid_faq.php:115
msgid "After logout I am redirected to blank page"
msgstr ""

#: view/faq/mo_openid_faq.php:117
msgid "Your theme and Social Login plugin may conflict during logout. To resolve it you need to uncheck"
msgstr ""

#: view/faq/mo_openid_faq.php:117
msgid "Enable Logout Redirection"
msgstr ""

#: view/faq/mo_openid_faq.php:117
msgid "checkbox under"
msgstr ""

#: view/faq/mo_openid_faq.php:117
msgid "tab"
msgstr ""

#: view/faq/mo_openid_faq.php:120
msgid "My users get the following message -\"Registration has been disabled for this site. Please contact your administrator.\" What should I do?"
msgstr ""

#: view/faq/mo_openid_faq.php:122
msgid "This means you must have unchecked the check-box of auto-register in the Social Login tab. Please check it. This will allow new users to be able to register to your site."
msgstr ""

#: view/faq/mo_openid_faq.php:125
msgid "Why do my users get a message that it is not secure to proceed?"
msgstr ""

#: view/faq/mo_openid_faq.php:126
msgid "Your website must be starting with http://. Now generally that's not an issue but our service uses https://( s stands for secure). You get a warning from the browser that the information is being passed insecurely. This happens after you log in to social media application and are coming back to your website. The warning is triggered from the browser since the data passes from https:// to http://, i.e. from a secure site to non-secure site.<br><br>We make sure that the information(email, name, username) getting passed from social media application to your website is encrypted with a key which is unique to you. So, even if the there is a warning of sending information without security, that information is encrypted."
msgstr ""

#: view/faq/mo_openid_faq.php:127
msgid "To remove this warning, you can add an SSL certificate to your website to change it to https OR use your own"
msgstr ""

#: view/faq/mo_openid_faq.php:127 view/shrtco/mo_openid_shrtco.php:112
#: view/shrtco/mo_openid_shrtco.php:113
msgid "Custom App"
msgstr ""

#: view/faq/mo_openid_faq.php:130
msgid "My users get the following message -\"There was an error in registration. Please contact your administrator.\" What should I do?"
msgstr ""

#: view/faq/mo_openid_faq.php:132
msgid "This message is thrown by WordPress when there is an error in user-registration"
msgstr ""

#: view/faq/mo_openid_faq.php:133
msgid "To see the actual error thrown by WordPress, go to \\wordpress\\wp-content\\plugins\\miniorange-login-openid\\class-mo-openid-login-widget.php file"
msgstr ""

#: view/faq/mo_openid_faq.php:134
msgid "Search for the line"
msgstr ""

#: view/faq/mo_openid_faq.php:135
msgid "Change it to"
msgstr ""

#: view/faq/mo_openid_faq.php:136
msgid "Save the file and try logging again. Please send us the error you see while logging in through the support forum to your right."
msgstr ""

#: view/faq/mo_openid_faq.php:138
msgid "How do I centre the social login icons?"
msgstr ""

#: view/faq/mo_openid_faq.php:140
msgid "If you are making changes to a PHP file"
msgstr ""

#: view/faq/mo_openid_faq.php:141
msgid "Go to the PHP file which invokes your page/post and insert the following html snippet. Also, increase the margin-left value as per your requirement. Save the file."
msgstr ""

#: view/faq/mo_openid_faq.php:144
msgid "If you are making changes to an HTML file."
msgstr ""

#: view/faq/mo_openid_faq.php:145
msgid "Go to the HTML file which invokes your page/post and insert the following html snippet. Also, increase the margin-left value as per your requirement. Save the file."
msgstr ""

#: view/faq/mo_openid_faq.php:155
#: view/soc_sha/soc_apps/mo_openid_sharing.php:570
msgid "Social Sharing"
msgstr ""

#: view/faq/mo_openid_faq.php:157
msgid "Is it possible to show sharing icons below the post content?"
msgstr ""

#: view/faq/mo_openid_faq.php:159
msgid "You can put social sharing icons before the content, after the content or both before and after the content."
msgstr ""

#: view/faq/mo_openid_faq.php:159
#: view/soc_sha/share_cnt/mo_openid_shrcnt.php:50
#: view/soc_sha/share_cnt/mo_openid_shrcnt.php:53
#: view/soc_sha/share_cnt/mo_openid_shrcnt.php:57
msgid "Go to"
msgstr ""

#: view/faq/mo_openid_faq.php:159
msgid "Sharing tab"
msgstr ""

#: view/faq/mo_openid_faq.php:159
msgid "check"
msgstr ""

#: view/faq/mo_openid_faq.php:159
msgid "Blog post"
msgstr ""

#: view/faq/mo_openid_faq.php:159
msgid "checkbox and select one of three(before, after, both) options available. Save settings"
msgstr ""

#: view/faq/mo_openid_faq.php:162
msgid "Why is sharing with some applications not working?"
msgstr ""

#: view/faq/mo_openid_faq.php:164
msgid "This issue arises if your website is not publicly hosted. Facebook, for example looks for the URL to generate its preview for sharing. That does not work on localhost or any privately hosted URL."
msgstr ""

#: view/faq/mo_openid_faq.php:167
msgid "Facebook sharing is showing the wrong image. How do I change the image?"
msgstr ""

#: view/faq/mo_openid_faq.php:169
msgid "The image is selected by Facebook and it is a part of Facebook sharing feature. We provide Facebook with webpage URL. It generates the entire preview of webpage using that URL."
msgstr ""

#: view/faq/mo_openid_faq.php:170
msgid "To set an image for the page, set it as a meta tag in"
msgstr ""

#: view/faq/mo_openid_faq.php:170
msgid "of your webpage."
msgstr ""

#: view/faq/mo_openid_faq.php:172
msgid "You can further debug the issue with Facebook's tool"
msgstr ""

#: view/faq/mo_openid_faq.php:174
msgid "If the problem still persists, please contact us using the Support form on the right."
msgstr ""

#: view/faq/mo_openid_faq.php:177
msgid "There is no option of Instagram in Social Sharing. Why?"
msgstr ""

#: view/faq/mo_openid_faq.php:179
msgid "Instagram has made a conscious effort to not allow sharing from external sources to fight spam and low quality photos."
msgstr ""

#: view/faq/mo_openid_faq.php:180
msgid "At this point of time, uploading via Instagram's API from external sources is not possible"
msgstr ""

#: view/faq/mo_openid_faq.php:184
msgid "Email share is not working. Why?"
msgstr ""

#: view/faq/mo_openid_faq.php:186
msgid "Email share in the plugin is enabled through"
msgstr ""

#: view/faq/mo_openid_faq.php:186
msgid "mailto"
msgstr ""

#: view/faq/mo_openid_faq.php:186
msgid "mailto is generally configured through desktop or browser so if it is not working, mailto is not setup or improperly configured"
msgstr ""

#: view/faq/mo_openid_faq.php:187
msgid "To set it up properly, search for mailto settings followed by your Operating System's name where you have your browser installed."
msgstr ""

#: view/faq/mo_openid_faq.php:190
msgid "I cannot see some icons in preview or on blog even though I have selected them in the plugin settings."
msgstr ""

#: view/faq/mo_openid_faq.php:192
msgid "Please check if you have an Adblock extension installed on your browser where you are checking the plugin. If you do, the Adblock extension will have a setting to block Social buttons. Uncheck this option."
msgstr ""

#: view/faq/mo_openid_faq.php:194
msgid "If you don't have Adblock installed and still face this issue, please contact us using the Support form on the right or mail <NAME_EMAIL>."
msgstr ""

#: view/faq/mo_openid_faq.php:206
msgid "Frequently Asked Questions"
msgstr ""

#: view/gdpr/mo_openid_gdpr.php:13
msgid "If GDPR check is enabled, users will be asked to give consent before using Social Login. Users who will not give consent will not be able to log in. This setting stands true only when users are registering using Social Login. This will not interfere with users registering through the regular WordPress"
msgstr ""

#: view/gdpr/mo_openid_gdpr.php:13
msgid "Click"
msgstr ""

#: view/gdpr/mo_openid_gdpr.php:13
msgid "here"
msgstr ""

#: view/gdpr/mo_openid_gdpr.php:13
msgid "to read miniOrange Social Login Privacy Policy. Please update your website's privacy policy accordingly and enter the URL to your privacy policy below."
msgstr ""

#: view/gdpr/mo_openid_gdpr.php:15
msgid "Take consent from users"
msgstr ""

#: view/gdpr/mo_openid_gdpr.php:25
msgid "Enter the Consent message:"
msgstr ""

#: view/gdpr/mo_openid_gdpr.php:28
msgid "Enter the text to be displayed for the Privacy Policy URL"
msgstr ""

#: view/gdpr/mo_openid_gdpr.php:31
msgid "Enter Privacy Policy URL"
msgstr ""

#: view/gdpr/mo_openid_gdpr.php:38
msgid "GDPR Settings"
msgstr ""

#: view/integration/mo_openid_integrate.php:7
msgid "Custom Attributes Mapping"
msgstr ""

#: view/integration/mo_openid_integrate.php:12
#: view/integration/mo_openid_integrate.php:46
#: view/integration/mo_openid_integrate.php:56
#: view/integration/mo_openid_integrate.php:67
msgid "This feature is available in premium version only. To use this feature, please upgrade to premium plugin."
msgstr ""

#: view/integration/mo_openid_integrate.php:18
msgid "Select Attribute"
msgstr ""

#: view/integration/mo_openid_integrate.php:43
msgid "BuddyPress Extended Attributes Mapping"
msgstr ""

#: view/integration/mo_openid_integrate.php:54
msgid "Paid Memberships Pro"
msgstr ""

#: view/integration/mo_openid_integrate.php:64
msgid "MailChimp Integration"
msgstr ""

#: view/integration/mo_openid_integrate.php:68
msgid "A user is added as a subscriber to a mailing list in MailChimp when that user registers using social login. First name, last name and email are also captured for that user in the Mailing List."
msgstr ""

#: view/integration/mo_openid_integrate.php:69
msgid "(List ID in MailChimp : Lists -> Select your List -> Settings -> List Name and Defaults -> List ID)"
msgstr ""

#: view/integration/mo_openid_integrate.php:70
msgid "(API Key in MailChimp : Profile -> Extras -> API Keys -> Your API Key )"
msgstr ""

#: view/integration/mo_openid_integrate.php:71
msgid "List Id"
msgstr ""

#: view/integration/mo_openid_integrate.php:72
msgid "API Key:"
msgstr ""

#: view/integration/mo_openid_integrate.php:75
msgid "Ask user for permission to be added in MailChimp Subscriber list"
msgstr ""

#: view/integration/mo_openid_integrate.php:80
msgid "If unchecked, user will get subscribed during registration."
msgstr ""

#: view/integration/mo_openid_integrate.php:82
msgid "Click on Download button to get a list of emails of WordPress users registered on your site. You can import this file in MailChimp."
msgstr ""

#: view/integration/mo_openid_integrate.php:85
msgid "Download emails of users"
msgstr ""

#: view/integration/mo_openid_integrate.php:95
msgid "Woocommerce Integration"
msgstr ""

#: view/integration/mo_openid_integrate.php:98
msgid "This feature is available in premium version only. To use this feature, please upgrade to  premium plugin."
msgstr ""

#: view/integration/mo_openid_integrate.php:99
msgid "If enabled, first name, last name and email are pre-filled in billing details of a user and on the Woocommerce checkout page."
msgstr ""

#: view/integration/mo_openid_integrate.php:102
msgid "Sync Woocommerce checkout fields"
msgstr ""

#: view/licensing_plans/mo_openid_lic_plans.php:367
#: view/licensing_plans/mo_openid_lic_plans.php:621
msgid "Licensing Plan For Social Login"
msgstr ""

#: view/link_social_account/mo_openid_Acclink.php:19
msgid "Enable account linking to let your users link their Social accounts with existing WordPress account. Users will be prompted with the option to either link to any existing account using WordPress login page or register as a new user."
msgstr ""

#: view/link_social_account/mo_openid_Acclink.php:20
msgid "Enable linking of Social Accounts"
msgstr ""

#: view/link_social_account/mo_openid_Acclink.php:33
msgid "Customize Text for Account Linking"
msgstr ""

#: view/link_social_account/mo_openid_Acclink.php:33
msgid "Preview Account Linking form"
msgstr ""

#: view/link_social_account/mo_openid_Acclink.php:35
msgid "Enter title of Account linking form"
msgstr ""

#: view/link_social_account/mo_openid_Acclink.php:36
msgid " Enter button text for create new user"
msgstr ""

#: view/link_social_account/mo_openid_Acclink.php:39
msgid "Enter button text for Link to existing user:"
msgstr ""

#: view/link_social_account/mo_openid_Acclink.php:41
msgid "Enter instruction to Create New Account :"
msgstr ""

#: view/link_social_account/mo_openid_Acclink.php:47
msgid " Enter instructions to link to an existing account :"
msgstr ""

#: view/link_social_account/mo_openid_Acclink.php:51
msgid "Enter extra instructions for account linking "
msgstr ""

#: view/link_social_account/mo_openid_Acclink.php:55
msgid "Display miniOrange logo with social login icons on account completion forms"
msgstr ""

#: view/mo_new/mo_openid_whats_new.php:9
msgid "Do you want to make your <b>website more secure"
msgstr ""

#: view/mo_new/mo_openid_whats_new.php:9
msgid "by Brute Force attack, Spam Protection etc? Try out our Plugin."
msgstr ""

#: view/mo_new/mo_openid_whats_new.php:22
#: view/mo_new/mo_openid_whats_new.php:50
#: view/mo_new/mo_openid_whats_new.php:89
msgid "Looking for"
msgstr ""

#: view/mo_new/mo_openid_whats_new.php:22
msgid "WordPress REST API Authentication?"
msgstr ""

#: view/mo_new/mo_openid_whats_new.php:22
msgid "Try out our new Plugin which secures rest API access for unauthorized users using OAuth 2.0, Basic Auth, jwt, Bearer Token."
msgstr ""

#: view/mo_new/mo_openid_whats_new.php:37
msgid "Looking for "
msgstr ""

#: view/mo_new/mo_openid_whats_new.php:37
msgid "WordPress OAuth Single Sign On?"
msgstr ""

#: view/mo_new/mo_openid_whats_new.php:37
msgid "Try out our new Plugin which allows login (Single Sign On) into WordPress with your Azure AD, AWS Cognito, Invision Community, Slack, Discord or other custom OAuth 2.0 / OpenID Connect providers"
msgstr ""

#: view/mo_new/mo_openid_whats_new.php:50
msgid "WordPress Two Factor Authentication"
msgstr ""

#: view/mo_new/mo_openid_whats_new.php:50
msgid "Try out our 2FA Plugin which is simple & easy 2FA setup with any App supporting TOTP algorithm like Google, Authy, LastPass Authenticator & other 2FA methods."
msgstr ""

#: view/mo_new/mo_openid_whats_new.php:65
msgid "Looking for a"
msgstr ""

#: view/mo_new/mo_openid_whats_new.php:65
msgid "Try our new <b>Social Login Integration Add-on"
msgstr ""

#: view/mo_new/mo_openid_whats_new.php:71
msgid "Social Login Integration Add-on"
msgstr ""

#: view/mo_new/mo_openid_whats_new.php:72
msgid "by"
msgstr ""

#: view/mo_new/mo_openid_whats_new.php:73
msgid "miniorange"
msgstr ""

#: view/mo_new/mo_openid_whats_new.php:77
msgid "Custom Registration Form Add-On helps you to integrate details of new as well as existing users. You can add as many fields as you want including the one which are returned by social sites at time of registration"
msgstr ""

#: view/mo_new/mo_openid_whats_new.php:79
msgid "Tested with 5.3"
msgstr ""

#: view/mo_new/mo_openid_whats_new.php:81
msgid "Get this plugin"
msgstr ""

#: view/mo_new/mo_openid_whats_new.php:89
msgid "OTP Verification"
msgstr ""

#: view/mo_new/mo_openid_whats_new.php:89
msgid "along with Social Login? Try our OTP Plugin."
msgstr ""

#: view/mo_new/mo_openid_whats_new.php:102
msgid "What\\'s new in miniOrange"
msgstr ""

#: view/premium_features/mo_openid_prem_feat.php:89
msgid "Force Admin To Login Using Password"
msgstr ""

#: view/premium_features/mo_openid_prem_feat.php:91
msgid "Admin user tries to login using social login then he will need to enter WordPress admin login credentials to login."
msgstr ""

#: view/premium_features/mo_openid_prem_feat.php:104
msgid "User Moderation"
msgstr ""

#: view/premium_features/mo_openid_prem_feat.php:106
msgid "Enable this feature to restrict the access of newly registered users. User created through social login will not be able to access your website until admin will not allow them by activating their accounts else"
msgstr ""

#: view/premium_features/mo_openid_prem_feat.php:107
msgid "Notice: SMTP should be configured to send activation emails. "
msgstr ""

#: view/premium_features/mo_openid_prem_feat.php:120
msgid "Reset Password"
msgstr ""

#: view/premium_features/mo_openid_prem_feat.php:122
msgid "Send password reset link to user after registration"
msgstr ""

#: view/premium_features/mo_openid_prem_feat.php:123
msgid "Notice: SMTP should be configured to send activation emails"
msgstr ""

#: view/premium_features/mo_openid_prem_feat.php:134
msgid "Extended User Attribute"
msgstr ""

#: view/premium_features/mo_openid_prem_feat.php:136
msgid "Mainly the required data(name,lastname,email) is mapped and use after the user gets login. If you want to use more data that is provided from the app you can enable this feature.(The data is depend on app to app)"
msgstr ""

#: view/premium_features/mo_openid_prem_feat.php:137
msgid "Custom App of should be set for this feature"
msgstr ""

#: view/premium_features/mo_openid_prem_feat.php:148
msgid "Redirect to social in a new window"
msgstr ""

#: view/premium_features/mo_openid_prem_feat.php:150
msgid "While login with social login. The login page opens in a new tab. After the login process the tab gets closed."
msgstr ""

#: view/profile/mo_openid_profile.php:20
msgid "Why should I register?"
msgstr ""

#: view/profile/mo_openid_profile.php:22
msgid "By registering with miniOrange we take care of creating applications for you so that you don’t have to worry about creating applications in each social network."
msgstr ""

#: view/profile/mo_openid_profile.php:23
msgid "Please Note"
msgstr ""

#: view/profile/mo_openid_profile.php:23
msgid "We do not store any information except the email that you will use to register with us. You can go through our "
msgstr ""

#: view/profile/mo_openid_profile.php:23
msgid "for how we use your information. We don’t sell your information to any third-party organization"
msgstr ""

#: view/profile/mo_openid_profile.php:27
#: view/soc_sha/soc_apps/mo_openid_sharing.php:202
msgid "Email"
msgstr ""

#: view/profile/mo_openid_profile.php:33
msgid "Password"
msgstr ""

#: view/profile/mo_openid_profile.php:38
msgid "Confirm Password"
msgstr ""

#: view/profile/mo_openid_profile.php:51
msgid "By clicking Submit, you agree to our"
msgstr ""

#: view/profile/mo_openid_profile.php:51
msgid "User Agreement"
msgstr ""

#: view/profile/mo_openid_profile.php:72
msgid "Thank you for registering with miniOrange"
msgstr ""

#: view/profile/mo_openid_profile.php:75
msgid "miniOrange Account Email"
msgstr ""

#: view/profile/mo_openid_profile.php:79
msgid "Customer ID"
msgstr ""

#: view/profile/mo_openid_profile.php:84 view/recaptcha/mo_openid_recap.php:17
msgid "Click here"
msgstr ""

#: view/profile/mo_openid_profile.php:84
msgid " to check our"
msgstr ""

#: view/profile/mo_openid_profile.php:84
msgid "plans"
msgstr ""

#: view/profile/mo_openid_profile.php:91
msgid "User Profile Details"
msgstr ""

#: view/profile_completion/mo_openid_prof_comp.php:10
msgid "Prompt users for username &amp; email when unavailable (profile completion)"
msgstr ""

#: view/profile_completion/mo_openid_prof_comp.php:11
msgid "In case of unavailability of username or email from the social media application, user is prompted to input the same"
msgstr ""

#: view/profile_completion/mo_openid_prof_comp.php:16
msgid "*NOTE:"
msgstr ""

#: view/profile_completion/mo_openid_prof_comp.php:16
msgid "Disabling profile completion is not recommended. Instagram and Twitter don't return email address. Please keep this enabled if you are using Instagram or Twitter. This feature requires SMTP to be setup for your WordPress website since we send a code to users over email to verify their email address."
msgstr ""

#: view/profile_completion/mo_openid_prof_comp.php:19
msgid "Customize Text for Profile Completion"
msgstr ""

#: view/profile_completion/mo_openid_prof_comp.php:19
msgid "Preview Profile Completion form"
msgstr ""

#: view/profile_completion/mo_openid_prof_comp.php:21
msgid "Enter title of Profle Completion"
msgstr ""

#: view/profile_completion/mo_openid_prof_comp.php:22
msgid "Enter Username Label text"
msgstr ""

#: view/profile_completion/mo_openid_prof_comp.php:23
msgid "Enter Email Label text"
msgstr ""

#: view/profile_completion/mo_openid_prof_comp.php:24
msgid "Enter Submit button text"
msgstr ""

#: view/profile_completion/mo_openid_prof_comp.php:25
msgid "Enter instruction for Profile Completion"
msgstr ""

#: view/profile_completion/mo_openid_prof_comp.php:27
msgid "Enter extra instruction for Profile Completion "
msgstr ""

#: view/profile_completion/mo_openid_prof_comp.php:29
msgid "Enter username already exists warning message text "
msgstr ""

#: view/profile_completion/mo_openid_prof_comp.php:30
msgid "Customize Text for Email Verification"
msgstr ""

#: view/profile_completion/mo_openid_prof_comp.php:32
msgid "Enter title of Email Verification form"
msgstr ""

#: view/profile_completion/mo_openid_prof_comp.php:33
msgid "Enter Resend OTP button text"
msgstr ""

#: view/profile_completion/mo_openid_prof_comp.php:34
msgid "Enter Back button text"
msgstr ""

#: view/profile_completion/mo_openid_prof_comp.php:35
msgid "Enter instruction for Email Verification form"
msgstr ""

#: view/profile_completion/mo_openid_prof_comp.php:37
msgid "Enter verification code in Email Verification form"
msgstr ""

#: view/profile_completion/mo_openid_prof_comp.php:39
msgid "Enter Message for wrong OTP"
msgstr ""

#: view/profile_completion/mo_openid_prof_comp.php:40
msgid "Customized E-mail Message"
msgstr ""

#: view/profile_completion/mo_openid_prof_comp.php:41
#: view/soc_sha/cust_text/mo_openid_cust_shricon.php:107
#: view/soc_sha/disp_shropt/mo_openid_disp_shropt.php:201
#: view/soc_sha/soc_apps/mo_openid_sharing.php:556
msgid "NOTE"
msgstr ""

#: view/profile_completion/mo_openid_prof_comp.php:41
msgid "Please enter"
msgstr ""

#: view/profile_completion/mo_openid_prof_comp.php:41
msgid "in message where you want to show one time password."
msgstr ""

#: view/profile_completion/mo_openid_prof_comp.php:43
msgid "Display miniOrange logo with social login icons on profile completion forms"
msgstr ""

#: view/recaptcha/mo_openid_recap.php:8
msgid "Enable reCAPTCHA"
msgstr ""

#: view/recaptcha/mo_openid_recap.php:17
msgid "Prerequisite"
msgstr ""

#: view/recaptcha/mo_openid_recap.php:17
msgid "Before you can use reCAPTCHA, you need to register your domain/webiste."
msgstr ""

#: view/recaptcha/mo_openid_recap.php:18
msgid "Enter Site key and Secret key that you get after registration."
msgstr ""

#: view/recaptcha/mo_openid_recap.php:21
msgid "Select type of reCAPTCHA "
msgstr ""

#: view/recaptcha/mo_openid_recap.php:23
msgid "reCAPTCHA v3"
msgstr ""

#: view/recaptcha/mo_openid_recap.php:30
msgid "reCAPTCHA v2"
msgstr ""

#: view/recaptcha/mo_openid_recap.php:38
msgid "Site key"
msgstr ""

#: view/recaptcha/mo_openid_recap.php:42
msgid "Secret key"
msgstr ""

#: view/recaptcha/mo_openid_recap.php:46
msgid "Enable reCAPTCHA for "
msgstr ""

#: view/recaptcha/mo_openid_recap.php:50
msgid "WordPress Login form"
msgstr ""

#: view/recaptcha/mo_openid_recap.php:61
msgid "WordPress Registration form"
msgstr ""

#: view/recaptcha/mo_openid_recap.php:82
msgid "Configure reCAPTCHA Settings"
msgstr ""

#: view/redirect_options/mo_openid_redirect_op.php:9
msgid "Redirect URL after login:"
msgstr ""

#: view/redirect_options/mo_openid_redirect_op.php:32
msgid "Account dashboard"
msgstr ""

#: view/redirect_options/mo_openid_redirect_op.php:40
msgid "Custom URL"
msgstr ""

#: view/redirect_options/mo_openid_redirect_op.php:51
#: view/redirect_options/mo_openid_redirect_op.php:110
msgid "Relative URL"
msgstr ""

#: view/redirect_options/mo_openid_redirect_op.php:62
msgid "*NOTE: If you login through WordPress default login page after login you will be redirected to Homepage"
msgstr ""

#: view/redirect_options/mo_openid_redirect_op.php:69
msgid "Redirect URL after logout:"
msgstr ""

#: view/redirect_options/mo_openid_redirect_op.php:78
#: view/soc_sha/disp_shropt/mo_openid_disp_shropt.php:51
msgid "Home Page"
msgstr ""

#: view/redirect_options/mo_openid_redirect_op.php:90
msgid "Current Page"
msgstr ""

#: view/redirect_options/mo_openid_redirect_op.php:100
msgid "Login Page"
msgstr ""

#: view/redirect_options/mo_openid_redirect_op.php:128
msgid "Redirection Options"
msgstr ""

#: view/registration/mo_openid_registration.php:9
msgid "Auto Registration"
msgstr ""

#: view/registration/mo_openid_registration.php:11
msgid "Auto-register users"
msgstr ""

#: view/registration/mo_openid_registration.php:15
msgid "Registration disabled message"
msgstr ""

#: view/registration/mo_openid_registration.php:17
msgid "If Auto-register users is unchecked, users will not be able to register using Social Login. The users who already have an account will be able to login.  This setting stands true only when users are registering using Social Login. This will not interfere with users registering through the regular WordPress registration form."
msgstr ""

#: view/registration/mo_openid_registration.php:20
msgid "Role Mapping"
msgstr ""

#: view/registration/mo_openid_registration.php:22
msgid "Universal Role"
msgstr ""

#: view/registration/mo_openid_registration.php:30
msgid "Use Role Mapping to assign this role to the all users registering through Social Login. According to the role mapped user will be granted role on the website."
msgstr ""

#: view/registration/mo_openid_registration.php:32
msgid "Enable Email Notification to Admin"
msgstr ""

#: view/registration/mo_openid_registration.php:34
msgid "Enable Email Notification to Admin - on User Registration"
msgstr ""

#: view/registration/mo_openid_registration.php:38
msgid "This feature requires SMTP to be configured"
msgstr ""

#: view/registration/mo_openid_registration.php:41
msgid "Set Display Picture for User"
msgstr ""

#: view/registration/mo_openid_registration.php:43
msgid "Set Display Picture for User - on User Registration"
msgstr ""

#: view/registration/mo_openid_registration.php:53
msgid "Registration Options"
msgstr ""

#: view/restrict_domain/mo_openid_restrict_dom.php:17
msgid "Users with these domains will not be able to register"
msgstr ""

#: view/restrict_domain/mo_openid_restrict_dom.php:24
msgid "Allow Domain"
msgstr ""

#: view/restrict_domain/mo_openid_restrict_dom.php:29
msgid "Only users with these domains will be able to register"
msgstr ""

#: view/shrtco/mo_openid_shrtco.php:9
msgid "Use social login Shortcode in the content of required page/post where you want to display Social Login Icons."
msgstr ""

#: view/shrtco/mo_openid_shrtco.php:10
msgid "Default Social Login Shortcode"
msgstr ""

#: view/shrtco/mo_openid_shrtco.php:11
msgid "This will display Social Login Icons with same default settings"
msgstr ""

#: view/shrtco/mo_openid_shrtco.php:14
msgid "For Icons"
msgstr ""

#: view/shrtco/mo_openid_shrtco.php:15
msgid "You can use  different attribute to customize social login icons. All attributes are optional except view."
msgstr ""

#: view/shrtco/mo_openid_shrtco.php:16
msgid "Square Shape Example"
msgstr ""

#: view/shrtco/mo_openid_shrtco.php:23
msgid "Round Shape Example"
msgstr ""

#: view/shrtco/mo_openid_shrtco.php:31
msgid "For Long-Buttons"
msgstr ""

#: view/shrtco/mo_openid_shrtco.php:32
msgid "You can use different attribute to customize social login buttons. All attributes are optional"
msgstr ""

#: view/shrtco/mo_openid_shrtco.php:33
msgid "Vertical View Example"
msgstr ""

#: view/shrtco/mo_openid_shrtco.php:38
msgid "Horizontal View Example"
msgstr ""

#: view/shrtco/mo_openid_shrtco.php:42
msgid "Note:"
msgstr ""

#: view/shrtco/mo_openid_shrtco.php:42
msgid "By default Long-Button view is"
msgstr ""

#: view/shrtco/mo_openid_shrtco.php:42
#: view/soc_sha/shrt_co/mo_openid_shrtco.php:11
msgid "Vertical"
msgstr ""

#: view/shrtco/mo_openid_shrtco.php:44
msgid "For Selected Applications"
msgstr ""

#: view/shrtco/mo_openid_shrtco.php:45
msgid "If you want to show selected applications without setting up default settings then use this shortcode. You can use different attribute to customize social login buttons. All attributes are optional"
msgstr ""

#: view/shrtco/mo_openid_shrtco.php:46
#: view/soc_sha/shrt_co/mo_openid_shrtco.php:17
msgid "Example"
msgstr ""

#: view/shrtco/mo_openid_shrtco.php:111
msgid "Note"
msgstr ""

#: view/shrtco/mo_openid_shrtco.php:112
msgid "1. If you are not registered with miniOrange and "
msgstr ""

#: view/shrtco/mo_openid_shrtco.php:112
msgid "is also not set up then please register with us first or set up"
msgstr ""

#: view/shrtco/mo_openid_shrtco.php:112
msgid "otherwise the buttons will be"
msgstr ""

#: view/shrtco/mo_openid_shrtco.php:112 view/shrtco/mo_openid_shrtco.php:113
msgid "disabled"
msgstr ""

#: view/shrtco/mo_openid_shrtco.php:113
msgid "2. If Facebook is selected then please set up "
msgstr ""

#: view/shrtco/mo_openid_shrtco.php:113
msgid "first otherwise the buttons will be "
msgstr ""

#: view/shrtco/mo_openid_shrtco.php:124
msgid "Available values for attributes"
msgstr ""

#: view/shrtco/mo_openid_shrtco.php:128
msgid "view"
msgstr ""

#: view/shrtco/mo_openid_shrtco.php:129
msgid "horizontal, vertical"
msgstr ""

#: view/shrtco/mo_openid_shrtco.php:133
#: view/soc_sha/shrt_co/mo_openid_shrtco.php:21
msgid "shape"
msgstr ""

#: view/shrtco/mo_openid_shrtco.php:134
msgid "round, roundededges, square, longbuttonwithtext"
msgstr ""

#: view/shrtco/mo_openid_shrtco.php:138
#: view/soc_sha/shrt_co/mo_openid_shrtco.php:22
msgid "theme"
msgstr ""

#: view/shrtco/mo_openid_shrtco.php:139
msgid "default, custombackground"
msgstr ""

#: view/shrtco/mo_openid_shrtco.php:143
#: view/soc_sha/shrt_co/mo_openid_shrtco.php:23
msgid "size"
msgstr ""

#: view/shrtco/mo_openid_shrtco.php:144
#: view/soc_sha/shrt_co/mo_openid_shrtco.php:23
msgid "Any value between 20 to 100"
msgstr ""

#: view/shrtco/mo_openid_shrtco.php:148
#: view/soc_sha/shrt_co/mo_openid_shrtco.php:24
msgid "space"
msgstr ""

#: view/shrtco/mo_openid_shrtco.php:149
msgid "Any value between 0 to 100"
msgstr ""

#: view/shrtco/mo_openid_shrtco.php:153
msgid "width"
msgstr ""

#: view/shrtco/mo_openid_shrtco.php:154
msgid "Any value between 200 to 1000"
msgstr ""

#: view/shrtco/mo_openid_shrtco.php:158
msgid "heigth"
msgstr ""

#: view/shrtco/mo_openid_shrtco.php:159
msgid "Any value between 35 to 50"
msgstr ""

#: view/shrtco/mo_openid_shrtco.php:163
#: view/soc_sha/shrt_co/mo_openid_shrtco.php:30
msgid "color"
msgstr ""

#: view/shrtco/mo_openid_shrtco.php:164
msgid "Enter color code for example"
msgstr ""

#: view/shrtco/mo_openid_shrtco.php:168
#: view/soc_sha/shrt_co/mo_openid_shrtco.php:29
msgid "heading"
msgstr ""

#: view/shrtco/mo_openid_shrtco.php:169
msgid "Enter custom heading"
msgstr ""

#: view/shrtco/mo_openid_shrtco.php:173
msgid "appcnt"
msgstr ""

#: view/shrtco/mo_openid_shrtco.php:174
msgid "Any value for number of icons in one row"
msgstr ""

#: view/shrtco/mo_openid_shrtco.php:188
msgid "Social Login Shortcodes"
msgstr ""

#: view/soc_com/com_Cust/mo_openid_comm_cust.php:11
msgid "Comment Section Heading"
msgstr ""

#: view/soc_com/com_Cust/mo_openid_comm_cust.php:15
msgid "Comments - Default Label"
msgstr ""

#: view/soc_com/com_Cust/mo_openid_comm_cust.php:19
msgid "Comments - Facebook Label"
msgstr ""

#: view/soc_com/com_Cust/mo_openid_comm_cust.php:30
msgid "Customize Text For Social Comment Labels"
msgstr ""

#: view/soc_com/com_Enable/mo_openid_comm_enable.php:9
#: view/soc_com/com_Enable/mo_openid_comm_enable.php:23
msgid "Enable Social Comments"
msgstr ""

#: view/soc_com/com_Enable/mo_openid_comm_enable.php:10
msgid "To enable Social Comments, please select Facebook Comments from"
msgstr ""

#: view/soc_com/com_Enable/mo_openid_comm_enable.php:10
msgid " Also select one or both of the options from"
msgstr ""

#: view/soc_com/com_Enable/mo_openid_comm_enable.php:11
msgid "Add Social Comments"
msgstr ""

#: view/soc_com/com_Enable/mo_openid_comm_enable.php:12
msgid "You can add social comments in the following areas from "
msgstr ""

#: view/soc_com/com_Enable/mo_openid_comm_enable.php:12
msgid "If you require a shortcode, please contact us from the Support form on the right."
msgstr ""

#: view/soc_com/com_Enable/mo_openid_comm_enable.php:14
msgid "Blog Post: This option enables Social Comments on Posts / Blog Post"
msgstr ""

#: view/soc_com/com_Enable/mo_openid_comm_enable.php:15
msgid "Static pages: This option places Social Comments on Pages / Static Pages with comments enabled"
msgstr ""

#: view/soc_com/com_display_options/mo_openid_comm_disp_opt.php:14
msgid "Select the options where you want to add social comments"
msgstr ""

#: view/soc_com/com_display_options/mo_openid_comm_disp_opt.php:20
#: view/soc_sha/disp_shropt/mo_openid_disp_shropt.php:74
msgid "Blog Post"
msgstr ""

#: view/soc_com/com_display_options/mo_openid_comm_disp_opt.php:28
#: view/soc_sha/disp_shropt/mo_openid_disp_shropt.php:98
msgid "Static Pages"
msgstr ""

#: view/soc_com/com_select_app/mo_openid_comm_select_app.php:9
msgid "Select applications to add Social Comments. These commenting applications will be added to your blog post pages at the location of your comments."
msgstr ""

#: view/soc_com/com_select_app/mo_openid_comm_select_app.php:21
msgid "If none of the below are selected, default WordPress comments will only be visible. Only selecting Default WordPress Comments will not result in any changes"
msgstr ""

#: view/soc_com/com_select_app/mo_openid_comm_select_app.php:28
msgid "Default WordPress Comments"
msgstr ""

#: view/soc_com/com_select_app/mo_openid_comm_select_app.php:39
msgid "Facebook Comments"
msgstr ""

#: view/soc_com/com_select_app/mo_openid_comm_select_app.php:55
msgid "Social Comments"
msgstr ""

#: view/soc_com/com_shrtco/comm_shrtco.php:9
msgid "Configure Social Comments in the Social Comments tab of the plugin"
msgstr ""

#: view/soc_com/com_shrtco/comm_shrtco.php:10
msgid "Keep both the display options checked and Save"
msgstr ""

#: view/soc_com/com_shrtco/comm_shrtco.php:11
msgid "Enable Comments for the post/page you want to add"
msgstr ""

#: view/soc_com/com_shrtco/comm_shrtco.php:12
msgid "Pages-> Quick Edit-> Allow Comments"
msgstr ""

#: view/soc_com/com_shrtco/comm_shrtco.php:12
msgid "(Skip this step if you already have Comments enabled.)"
msgstr ""

#: view/soc_com/com_shrtco/comm_shrtco.php:13
msgid "Add the shortcode "
msgstr ""

#: view/soc_com/com_shrtco/comm_shrtco.php:13
msgid "to an individual page/post"
msgstr ""

#: view/soc_com/com_shrtco/comm_shrtco.php:16
msgid "Social Comments Shortcodes"
msgstr ""

#: view/soc_sha/cust_text/mo_openid_cust_shricon.php:20
msgid "Customize Sharing Icons"
msgstr ""

#: view/soc_sha/cust_text/mo_openid_cust_shricon.php:21
msgid "Customize shape, size and background for sharing icons"
msgstr ""

#: view/soc_sha/cust_text/mo_openid_cust_shricon.php:71
msgid "Custom background"
msgstr ""

#: view/soc_sha/cust_text/mo_openid_cust_shricon.php:81
msgid "No background"
msgstr ""

#: view/soc_sha/cust_text/mo_openid_cust_shricon.php:107
msgid "Custom background: This will change the background color of share icons"
msgstr ""

#: view/soc_sha/cust_text/mo_openid_cust_shricon.php:107
msgid "No background:This will remove the background color of share icons"
msgstr ""

#: view/soc_sha/cust_text/mo_openid_cust_shricon.php:112
msgid "Preview"
msgstr ""

#: view/soc_sha/cust_text/mo_openid_cust_shricon.php:199
msgid "Customize Text For Social Share Icons"
msgstr ""

#: view/soc_sha/cust_text/mo_openid_cust_shricon.php:207
msgid "Select color for share heading"
msgstr ""

#: view/soc_sha/cust_text/mo_openid_cust_shricon.php:210
msgid "Enter text to show above share widget"
msgstr ""

#: view/soc_sha/cust_text/mo_openid_cust_shricon.php:213
msgid "Enter your twitter Username (without @)"
msgstr ""

#: view/soc_sha/cust_text/mo_openid_cust_shricon.php:217
msgid "Enter the Email subject (email share)"
msgstr ""

#: view/soc_sha/cust_text/mo_openid_cust_shricon.php:221
msgid "Enter the Email body (add ##url## to place the URL)"
msgstr ""

#: view/soc_sha/disp_shropt/mo_openid_disp_shropt.php:46
msgid "Select the options where you want to display social share icons"
msgstr ""

#: view/soc_sha/disp_shropt/mo_openid_disp_shropt.php:57
#: view/soc_sha/disp_shropt/mo_openid_disp_shropt.php:81
#: view/soc_sha/disp_shropt/mo_openid_disp_shropt.php:105
#: view/soc_sha/disp_shropt/mo_openid_disp_shropt.php:140
#: view/soc_sha/disp_shropt/mo_openid_disp_shropt.php:160
#: view/soc_sha/disp_shropt/mo_openid_disp_shropt.php:185
msgid "Before content"
msgstr ""

#: view/soc_sha/disp_shropt/mo_openid_disp_shropt.php:61
#: view/soc_sha/disp_shropt/mo_openid_disp_shropt.php:85
#: view/soc_sha/disp_shropt/mo_openid_disp_shropt.php:109
#: view/soc_sha/disp_shropt/mo_openid_disp_shropt.php:144
#: view/soc_sha/disp_shropt/mo_openid_disp_shropt.php:164
#: view/soc_sha/disp_shropt/mo_openid_disp_shropt.php:189
msgid "After content"
msgstr ""

#: view/soc_sha/disp_shropt/mo_openid_disp_shropt.php:65
#: view/soc_sha/disp_shropt/mo_openid_disp_shropt.php:91
#: view/soc_sha/disp_shropt/mo_openid_disp_shropt.php:114
#: view/soc_sha/disp_shropt/mo_openid_disp_shropt.php:149
#: view/soc_sha/disp_shropt/mo_openid_disp_shropt.php:169
#: view/soc_sha/disp_shropt/mo_openid_disp_shropt.php:194
msgid "Both before and after content"
msgstr ""

#: view/soc_sha/disp_shropt/mo_openid_disp_shropt.php:120
msgid " WooCommerce Individual Product Page(Top)"
msgstr ""

#: view/soc_sha/disp_shropt/mo_openid_disp_shropt.php:130
msgid "WooCommerce Individual Product Page"
msgstr ""

#: view/soc_sha/disp_shropt/mo_openid_disp_shropt.php:135
msgid "BBPress Forums Page"
msgstr ""

#: view/soc_sha/disp_shropt/mo_openid_disp_shropt.php:155
msgid "BBPress Topic Page"
msgstr ""

#: view/soc_sha/disp_shropt/mo_openid_disp_shropt.php:178
msgid "BBPress Reply Page"
msgstr ""

#: view/soc_sha/disp_shropt/mo_openid_disp_shropt.php:201
msgid "The icons in above pages will be placed horizontally. For vertical icons, add "
msgstr ""

#: view/soc_sha/disp_shropt/mo_openid_disp_shropt.php:201
msgid "miniOrange Sharing - Vertical"
msgstr ""

#: view/soc_sha/disp_shropt/mo_openid_disp_shropt.php:201
msgid "widget from Appearance->Widgets."
msgstr ""

#: view/soc_sha/share_cnt/mo_openid_shrcnt.php:17
msgid "Share counts are supported for Facebook, Vkontakte, Stumble Upon and Pinterest."
msgstr ""

#: view/soc_sha/share_cnt/mo_openid_shrcnt.php:19
msgid "Enable share counts"
msgstr ""

#: view/soc_sha/share_cnt/mo_openid_shrcnt.php:47
msgid "Facebook Access Token"
msgstr ""

#: view/soc_sha/share_cnt/mo_openid_shrcnt.php:48
msgid "Instructions to configure Facebook Share Counts"
msgstr ""

#: view/soc_sha/share_cnt/mo_openid_shrcnt.php:50
msgid "Sign in with your Facebook account"
msgstr ""

#: view/soc_sha/share_cnt/mo_openid_shrcnt.php:52
msgid ""
"Use an existing app if you already have one or create a new facebook\n"
"                                    app by clicking on "
msgstr ""

#: view/soc_sha/share_cnt/mo_openid_shrcnt.php:53
#: view/soc_sha/share_cnt/mo_openid_shrcnt.php:54
msgid "Create App"
msgstr ""

#: view/soc_sha/share_cnt/mo_openid_shrcnt.php:53
msgid "My Apps"
msgstr ""

#: view/soc_sha/share_cnt/mo_openid_shrcnt.php:53
msgid ""
"and\n"
"                                    select"
msgstr ""

#: view/soc_sha/share_cnt/mo_openid_shrcnt.php:54
msgid ""
"Enter Display Name i.e App Name and click\n"
"                                    on"
msgstr ""

#: view/soc_sha/share_cnt/mo_openid_shrcnt.php:55
msgid "Create App ID"
msgstr ""

#: view/soc_sha/share_cnt/mo_openid_shrcnt.php:57
msgid "Tools"
msgstr ""

#: view/soc_sha/share_cnt/mo_openid_shrcnt.php:57
msgid "select "
msgstr ""

#: view/soc_sha/share_cnt/mo_openid_shrcnt.php:57
msgid "Graph API Explorer"
msgstr ""

#: view/soc_sha/share_cnt/mo_openid_shrcnt.php:57
msgid "and click on"
msgstr ""

#: view/soc_sha/share_cnt/mo_openid_shrcnt.php:57
msgid ""
"Get\n"
"                                        Access Token"
msgstr ""

#: view/soc_sha/share_cnt/mo_openid_shrcnt.php:60
msgid "Now, go to"
msgstr ""

#: view/soc_sha/share_cnt/mo_openid_shrcnt.php:60
msgid "Access Token Tool"
msgstr ""

#: view/soc_sha/share_cnt/mo_openid_shrcnt.php:60
msgid "and press"
msgstr ""

#: view/soc_sha/share_cnt/mo_openid_shrcnt.php:60
msgid "Debug"
msgstr ""

#: view/soc_sha/share_cnt/mo_openid_shrcnt.php:60
msgid "option at right side for the"
msgstr ""

#: view/soc_sha/share_cnt/mo_openid_shrcnt.php:60
msgid "User Token"
msgstr ""

#: view/soc_sha/share_cnt/mo_openid_shrcnt.php:61
msgid "Now copy the"
msgstr ""

#: view/soc_sha/share_cnt/mo_openid_shrcnt.php:61
msgid "Access Token"
msgstr ""

#: view/soc_sha/share_cnt/mo_openid_shrcnt.php:61
msgid ""
"and paste it in the above field and\n"
"                                    click on"
msgstr ""

#: view/soc_sha/share_cnt/mo_openid_shrcnt.php:62
msgid "save"
msgstr ""

#: view/soc_sha/share_cnt/mo_openid_shrcnt.php:65
msgid ""
"According to the new updates of\n"
"                                    Facebook API it will expires after every 60 days. So to avoid any\n"
"                                    issues update it again before 60 days."
msgstr ""

#: view/soc_sha/share_cnt/mo_openid_shrcnt.php:101
msgid "Share Counts"
msgstr ""

#: view/soc_sha/shrt_co/mo_openid_shrtco.php:10
msgid "Horizontal"
msgstr ""

#: view/soc_sha/shrt_co/mo_openid_shrtco.php:15
msgid "For Sharing Icons"
msgstr ""

#: view/soc_sha/shrt_co/mo_openid_shrtco.php:16
msgid " You can use  different attribute to customize social sharing icons. All attributes are optional"
msgstr ""

#: view/soc_sha/shrt_co/mo_openid_shrtco.php:20
msgid "Common attributes - Horizontal and Vertical"
msgstr ""

#: view/soc_sha/shrt_co/mo_openid_shrtco.php:21
msgid " round, roundededges, square"
msgstr ""

#: view/soc_sha/shrt_co/mo_openid_shrtco.php:22
msgid "default, custombackground, nobackground"
msgstr ""

#: view/soc_sha/shrt_co/mo_openid_shrtco.php:24
msgid "Any value between 0 to 50"
msgstr ""

#: view/soc_sha/shrt_co/mo_openid_shrtco.php:25
msgid "url"
msgstr ""

#: view/soc_sha/shrt_co/mo_openid_shrtco.php:25
msgid "Enter custom URL for sharing"
msgstr ""

#: view/soc_sha/shrt_co/mo_openid_shrtco.php:26
msgid "fontcolor"
msgstr ""

#: view/soc_sha/shrt_co/mo_openid_shrtco.php:26
msgid "Enter custom color for icons (only works with no background theme"
msgstr ""

#: view/soc_sha/shrt_co/mo_openid_shrtco.php:27
msgid "sharecnt"
msgstr ""

#: view/soc_sha/shrt_co/mo_openid_shrtco.php:27
msgid " yes, no "
msgstr ""

#: view/soc_sha/shrt_co/mo_openid_shrtco.php:27
msgid "To see social share count*"
msgstr ""

#: view/soc_sha/shrt_co/mo_openid_shrtco.php:28
msgid "Horizontal attributes"
msgstr ""

#: view/soc_sha/shrt_co/mo_openid_shrtco.php:29
msgid "Enter custom heading text"
msgstr ""

#: view/soc_sha/shrt_co/mo_openid_shrtco.php:30
msgid " Enter custom text color for heading eg: cyan, red, blue, orange, yellow"
msgstr ""

#: view/soc_sha/shrt_co/mo_openid_shrtco.php:32
msgid "Vertical attributes"
msgstr ""

#: view/soc_sha/shrt_co/mo_openid_shrtco.php:33
msgid "alignment"
msgstr ""

#: view/soc_sha/shrt_co/mo_openid_shrtco.php:33
msgid "left,right"
msgstr ""

#: view/soc_sha/shrt_co/mo_openid_shrtco.php:34
msgid "topoffset"
msgstr ""

#: view/soc_sha/shrt_co/mo_openid_shrtco.php:34
msgid "Any value(height from top) between 0 to 1000"
msgstr ""

#: view/soc_sha/shrt_co/mo_openid_shrtco.php:35
msgid "rightoffset(Applicable if alignment is right)"
msgstr ""

#: view/soc_sha/shrt_co/mo_openid_shrtco.php:35
#: view/soc_sha/shrt_co/mo_openid_shrtco.php:36
msgid "Any value between 0 to 200"
msgstr ""

#: view/soc_sha/shrt_co/mo_openid_shrtco.php:36
msgid "leftoffset(Applicable if alignment is left)"
msgstr ""

#: view/soc_sha/shrt_co/mo_openid_shrtco.php:45
msgid "Social Sharing Shortcode"
msgstr ""

#: view/soc_sha/soc_apps/mo_openid_sharing.php:18
msgid "Select Social Apps"
msgstr ""

#: view/soc_sha/soc_apps/mo_openid_sharing.php:20
msgid "Select applications to enable social sharing"
msgstr ""

#: view/soc_sha/soc_apps/mo_openid_sharing.php:28
msgid "Facebook"
msgstr ""

#: view/soc_sha/soc_apps/mo_openid_sharing.php:36
msgid "Twitter"
msgstr ""

#: view/soc_sha/soc_apps/mo_openid_sharing.php:47
msgid "Google"
msgstr ""

#: view/soc_sha/soc_apps/mo_openid_sharing.php:61
msgid "Vkontakte"
msgstr ""

#: view/soc_sha/soc_apps/mo_openid_sharing.php:74
msgid "Tumblr"
msgstr ""

#: view/soc_sha/soc_apps/mo_openid_sharing.php:91
msgid "StumbleUpon"
msgstr ""

#: view/soc_sha/soc_apps/mo_openid_sharing.php:105
msgid "LinkedIn"
msgstr ""

#: view/soc_sha/soc_apps/mo_openid_sharing.php:134
msgid "Pinterest"
msgstr ""

#: view/soc_sha/soc_apps/mo_openid_sharing.php:148
msgid "Pocket"
msgstr ""

#: view/soc_sha/soc_apps/mo_openid_sharing.php:165
msgid "Digg"
msgstr ""

#: view/soc_sha/soc_apps/mo_openid_sharing.php:178
msgid "Delicious"
msgstr ""

#: view/soc_sha/soc_apps/mo_openid_sharing.php:191
msgid "Odnoklassniki"
msgstr ""

#: view/soc_sha/soc_apps/mo_openid_sharing.php:214
msgid "Print"
msgstr ""

#: view/soc_sha/soc_apps/mo_openid_sharing.php:228
msgid "Whatsapp"
msgstr ""

#: view/soc_sha/soc_apps/mo_openid_sharing.php:249
msgid "No apps selected"
msgstr ""

#: view/soc_sha/soc_apps/mo_openid_sharing.php:556
msgid ""
"Custom background: This will\n"
"                        change the background color of sharing icons"
msgstr ""

#: view/soc_sha/soc_apps/mo_openid_sharing.php:558
msgid "No background: This will change the font color of icons without background"
msgstr ""
