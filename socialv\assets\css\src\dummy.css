@import "_custom-properties.css";

.wp-block-cover .wp-block-cover-text a {
	font-size: inherit;
}

.socialv-blog-meta ul li {
	font-size: var(--font-size-normal);
	color: var(--global-font-title);
	line-height: var(--font-line-height-body);
	position: relative;
	overflow: hidden;
	display: inline-block;
	padding-right: 1em;
	margin-right: 1em;
	margin-bottom: .313em;
}

.socialv-blog-meta ul li:last-child {
	padding-right: 0;
	margin-right: 0;
}

.list-inline-item:not(:last-child) {
	margin-right: .625em;
}

.socialv-blog-meta ul li a {
	display: inline-block;
	text-transform: capitalize;
}

.socialv-blog-meta ul li .author a {
    font-weight: var(--font-weight-semi-bold);
    color: var(--global-font-color);
    letter-spacing: var(--letter-spacing-one);
}

.socialv-blog-meta ul li .author a:hover {
    color: var(--color-theme-primary);
}

.socialv-blog-meta svg {
	color: var(--color-theme-primary);
	margin-right: .625em;
}

.socialv-blog-meta a,
.socialv-blog-meta a>time {
	color: var(--global-font-color);
	text-transform: uppercase;
	transition: all .5s ease-in;
}

.socialv-blog-meta a>time {
	letter-spacing: var(--letter-spacing-one);
	font-weight: var(--font-weight-semi-bold);
	color: var(--global-font-color);
	letter-spacing: var(--letter-spacing-one);
}

.socialv-blog-meta a:hover,
.socialv-blog-meta a>time:hover {
	color: var(--color-theme-primary);
}

.socialv-blog-meta ul li a:hover {
	color: var(--color-theme-primary);
}

.socialv-blog-meta ul li a:before {
	content: "";
	position: absolute;
	top: 50%;
	right: 0;
	background: var(--global-font-color);
	height: 1em;
	width: .0625em;
	transform: translateY(-50%);
}

.socialv-blog-meta ul li:last-child a::before {
	width: 0;
}

.socialv-blog-box .socialv-blogtag {
	padding: 0;
	display: inline-block;
	width: 100%;
	margin-top: 1.875em;
}

.socialv-blog-box .socialv-blogtag li {
	list-style: none;
	float: left;
	margin: 0 .625em .625em 0;
}

.post.sticky .socialv-blog-box {
	background: var(--color-post-sticky-bg);
	border: none;
}


.socialv-blog-box {
	position: relative;
	overflow: hidden;
	margin-bottom: 2em;
	background: var(--color-theme-white-box);
	border-radius: var(--border-radius);
}
.socialv_video_height .socialv-blog-box {
	margin-bottom: 1.125em;
}
.socialv-blog-main-list article[class~="entry"]:last-of-type .socialv-blog-box {
	margin-bottom: 0;
}

.socialv-blog-meta>ul {
	margin: 0;
}

.socialv-blog-head .entry-title {
	margin-top: 0;
}

.socialv-blog-box .socialv-blog-detail>a>h2 {
	margin: 0;
}

.socialv-blog-box .entry-title {
	display: inline;
	vertical-align: top;
	margin: 0;
}

.socialv-blog-box .entry-title:hover {
	color: var(--color-theme-primary);
}

/* Blog Page Link */
.page-links {
	margin: 1em 0 .625em;
	text-transform: uppercase;
	clear: both;
}

.page-links>span.page-number,
.page-links a {
	width: 2.813em;
	height: 2.813em;
	line-height: 2.813em;
	text-align: center;
	margin-left: .625em;
	padding: 0;
	display: inline-block;
	color: var(--global-font-color);
	border: .0625em solid rgba(134, 140, 156, 0.18);
	transition: all .5s ease-in-out;
	border-radius: var(--border-radius);
}

.page-links span.post-page-numbers.current {
	width: 2.813em;
	height: 2.813em;
	line-height: 2.813em;
	display: inline-block;
	text-align: center;
	border-radius: var(--border-radius);
	margin-left: .625em;
	background: var(--color-theme-primary);
	color: var(--color-theme-white);
}

.page-links a:hover {
	background: var(--color-theme-primary);
	color: var(--color-theme-white);
}

article.hentry .socialv-blog-detail .blog-content .page-links a:hover {
	color: var(--color-theme-white);
}

/* Sticky */
.sticky .socialv-blog-box .socialv-blog-head .entry-title a {
	position: relative;
	display: inline-block;
	color: var(--global-font-title);
}

.sticky .socialv-blog-box .socialv-blog-head .entry-title a:hover {
	color: var(--color-theme-primary);
}

.sticky .socialv-blog-box .socialv-blog-head .entry-title a:after {
	content: '*';
	font-size: 1.2em;
	position: absolute;
	right: -1.625em;
}

/* Gallery */
.gallery-size-thumbnail .gallery-item {
	margin-right: 2%;
	width: 18.4%;
	margin-bottom: 2%;
	display: inline-block;
	vertical-align: top;
}

.gallery-item .gallery-caption {
	line-height: 1.375em;
	font-size: var(--font-size-normal);
}

.gallery-size-thumbnail .gallery-item img {
	margin-bottom: .625em;
}

.gallery-columns-1 .gallery-item {
	width: 100%;
	margin-right: 0;
}

.gallery-columns-2 .gallery-item {
	width: 48%;
}

.gallery-columns-3 .gallery-item {
	width: 31.3%;
}

.gallery-columns-4 .gallery-item {
	width: 23%;
}

.gallery-columns-5 .gallery-item {
	width: 18%;
}

.gallery-columns-6 .gallery-item {
	width: 14.6%;
}

.gallery-columns-7 .gallery-item {
	width: 12.2%;
}

.gallery-columns-8 .gallery-item {
	width: 10.5%;
}

.gallery-columns-9 .gallery-item {
	width: 9.1%;
}

.gallery.gallery-size-thumbnail {
	display: inline-block;
	width: 100%;
}

.socialv-blog-box .socialv-blog-image > .gallery.gallery-size-thumbnail {
    padding: 2em 2em 0;
}

.gallery-caption,
.wp-caption,
figcaption {
	display: block;
	font-size: .813em !important;
	line-height: 1.5em !important;
	margin: .438em auto 0;
	max-width: 100%;
	opacity: 1;
}

/* Audio */
article.hentry.format-audio .socialv-blog-image {
	text-align: left;
}

article.hentry.format-audio .socialv-blog-image p:nth-child(-n+2) {
	display: inline-block;
	margin-bottom: 0;
	padding-top: 1.875em;
}

article.hentry.format-audio .socialv-blog-image p {
	margin-bottom: 0;
	padding-left: 1.875em;
}

article.hentry.format-video .socialv-blog-image p {
	margin-bottom: 0;
}

/*pagination-nav*/
.page-numbers li .next.page-numbers,
.page-numbers li .prev.page-numbers {
	width: auto;
	padding: 0 1em;
}

.page-numbers {
	display: -ms-flexbox;
	display: flex;
	flex-wrap: wrap;
	justify-content: center;
	padding-left: 0;
	list-style: none;
	padding: 0;
	margin: 0;
	gap: 1em;
}

.page-numbers li .page-numbers {
	position: relative;
	display: block;
	width: 2.813em;
	height: 2.813em;
	text-align: center;
	line-height: 2.813em;
	color: var(--global-font-color);
	background-color: var(--color-theme-white-box);
	border: .0625em solid var(--border-color-light);
	border-radius: var(--border-radius);
}

.page-numbers li:first-child .page-numbers {
	margin-left: 0;
}

.page-numbers li .page-numbers:hover {
	z-index: 2;
	color: var(--color-theme-white);
	text-decoration: none;
	background-color: var(--color-theme-primary);
	border-color: var(--color-theme-primary);
}

.page-numbers li .page-numbers:focus {
	z-index: 2;
	outline: 0;
	box-shadow: none;
}

.page-numbers li .page-numbers:not(:disabled):not(.disabled) {
	cursor: pointer
}

.page-numbers li .page-numbers.current {
	width: 2.813em;
	height: 2.813em;
	z-index: 1;
	color: var(--color-theme-white);
	transition: all .5s ease-out 0s;
	background: var(--color-theme-primary);
	border-color: var(--color-theme-primary);
	border-radius: var(--border-radius);
}

/* Footer */
footer.footer-one .copyright-footer .copyright a {
	color: var(--color-theme-white);
}

footer .widget ul li {
	border: none;
	padding: .5em 0;
	margin-bottom: 0;
	list-style: none;
}


/* blockquote */
blockquote,
.wp-block-quote.is-style-large {
	position: relative;
	font-style: italic;
	font-family: var(--highlight-font-family);
	background: var(--color-theme-white-box);
	color: var(--global-font-color);
	padding: 1.875em;
	margin-bottom: 1.875em;
	border-radius: var(--border-radius);
	width: 100%;
	border-left: .313em solid var(--color-theme-primary);
	margin: 1.875em auto;
	font-size: var(--font-size-18);

}

.socialv-blog-detail .wp-block-quote.is-style-large,
.socialv-blog-detail blockquote,
.socialv-activity-parent blockquote {
    background: var(--global-body-bgcolor);
}

.wp-block-quote.is-style-large,
blockquote p a {
	color: var(--color-theme-primary);
}

blockquote p {
	margin: 0;
}

.wp-block-column>p {
	margin: 0;
}

ul.wp-block-archives-list li {
	margin-bottom: 1em;
	list-style: none;
}

ul.wp-block-archives-list li .archiveCount {
	float: right;
	clear: both;
}

/* wp-block-button */
.wp-block-button .wp-block-button__link {
	position: relative;
	z-index: 9;
	background: var(--color-theme-primary);
	font-size: var(--font-size-normal);
	font-family: var(--highlight-font-family);
	letter-spacing: var(--letter-spacing-one);
	font-weight: var(--font-weight-semi-bold);
	line-height: var(--font-line-height-body);
	text-transform: uppercase;
}

.wp-block-button.is-style-squared .wp-block-button__link,
.wp-block-button.is-style-outline .wp-block-button__link,
.wp-block-button.aligncenter .wp-block-button__link,
.wp-block-button.alignleft .wp-block-button__link,
.wp-block-button.alignright .wp-block-button__link {
	border-radius: 0;
}

.wp-block-button.alignright {
	margin-left: 2em;
	margin-top: 0;
	text-align: right;
}

.wp-block-button {
	float: none;
	margin: 1em 0;
}

.wp-block-file {
	text-decoration: none;
	transition: color .2s ease-out;
	cursor: pointer;
	align-items: center;
	display: flex;
	margin: 0;
	color: var(--global-font-color);
}

.wp-block-button.is-style-outline .wp-block-button__link {
	background: transparent;
	border-color: var(--color-theme-primary);
	color: var(--color-theme-primary);
}

.wp-block-button.is-style-outline .wp-block-button__link:before {
	background: transparent;
	border-radius: 0;
}


.wp-block-button .wp-block-button__link::before {
	border-radius: 1.55em;
}

.wp-block-button.is-style-outline .wp-block-button__link::before,
.wp-block-button.is-style-squared .wp-block-button__link::before,
.wp-block-button.aligncenter .wp-block-button__link::before,
.wp-block-button.alignleft .wp-block-button__link::before,
.wp-block-button.alignright .wp-block-button__link::before {
	border-radius: 0;
}

.wp-block-group.has-background {
	padding: 1.25em 1.875em;
	margin-bottom: 1em;
	color: var(--global-white-light-color);
}

.wp-block-group.has-background .wp-block-button__link {
	color: var(--color-theme-white);
}

.wp-block-group.has-background .wp-block-button__link:hover {
	color: var(--color-theme-white);
}

/* Text meant only for screen readers */
.screen-reader-text {
	clip: rect(.0625em, .0625em, .0625em, .0625em);
	position: absolute !important;
	white-space: nowrap;
	height: 0;
	width: .0625em;
	overflow: hidden;
}

.screen-reader-text:focus {
	background-color: var(--global-white-light-color);
	border-radius: .188em;
	box-shadow: 0 0 .125em .125em rgba(0, 0, 0, .6);
	clip: auto !important;
	color: var(--color-theme-blue);
	display: block;
	font-size: .875rem;
	font-weight: var(--font-weight-bold);
	height: auto;
	left: .313em;
	line-height: normal;
	padding: 1em 1.438em .875em;
	text-decoration: none;
	top: .313em;
	width: auto;
	z-index: 100000;
}

.wp-block-table.is-style-stripes tbody tr:nth-child(odd) {
	background-color: var(--global-body-bgcolor);
	color: var(--color-theme-white);
}

/* wp-block */
.wp-block-gallery.alignleft {
	margin: 0 1.875em 1.875em 0 !important;
}

.wp-block-image.alignfull {
	margin: 0 -1.875em 1.875em;
}

.wp-block-cover {
	margin-bottom: 1.875em;
}

.wp-block-table.is-style-stripes td {
	border-color: var(--border-color-light);
	color: var(--global-font-color);
}

.wp-block-table td,
.wp-block-table th {
	text-align: left;
}

.wp-block-latest-posts.is-grid.has-dates {
	margin: 0;
}

/* WordPress Core */
.aligncenter,
div.aligncenter {
	display: block;
	margin-left: auto;
	margin-right: auto;
	text-align: center;
}

a img.alignright {
	float: right;
	margin: 0 0 1.875em 1.875em;
}

a img.alignnone {
	margin: 0 1.875em 1.875em 0;
}

.format-image .socialv-blog-detail a img.alignnone {
	width: 100%;
	margin: 0;
}

a img.alignleft {
	float: left;
	margin: 0 1.875em 1.875em 0;
}

a img.aligncenter {
	display: block;
	margin-left: auto;
	margin-right: auto;
}

.wp-caption p.wp-caption-text {
	font-size: .813em;
	color: var(--global-font-color);
}

.elementor-icon-box-title {
	margin: 0;
}

.alignleft {
	float: left;
	text-align: left;
	margin: 0 1.875em 1.875em 0 !important;
	clear: left;
}

.alignright {
	float: right;
	text-align: right;
	margin: 0 0 1.875em 1.875em !important;
	clear: right;
}

body:not([class*=aare-core]) .alignright {
	clear: right;
}

.wp-block-button a:not([href]):not([tabindex]).wp-block-button__link {
	color: var(--color-theme-white);
}

.is-style-outline a:not([href]):not([tabindex]).wp-block-button__link {
	color: var(--color-theme-primary);
	background: transparent;
	border-color: var(--color-theme-primary);
}

.wp-block-button .wp-block-button__link:hover {
	background: var(--color-theme-primary-dark);
	color: var(--color-theme-white);
}

.is-style-outline a:not([href]):not([tabindex]).wp-block-button__link:hover {
	background: var(--color-theme-primary-dark);
	color: var(--color-theme-white);
	border-color: var(--color-theme-primary-dark);
}

.has-drop-cap::after {
	clear: both;
	content: '';
	display: table;
	table-layout: fixed;
}

.has-avatars .wp-block-latest-comments__comment .wp-block-latest-comments__comment-excerpt,
.has-avatars .wp-block-latest-comments__comment .wp-block-latest-comments__comment-meta,
.wp-block-latest-comments__comment-meta {
	background: transparent;
}

.has-avatars .wp-block-latest-comments__comment .wp-block-latest-comments__comment-excerpt p {
	margin: 0;
}

article.hentry .socialv-blog-box .socialv-blog-detail .blog-content .widget_tag_cloud ul li a:hover {
	color: var(--color-theme-white);
}

/*------------------ Recent Post ---------------------*/

.post-img-holder {
	width: 5em;
	margin-right: 1.25em;
	flex: none;
	line-height: 0;
}

.post-img-holder a {
	display: block;
	width: 100%;
	height: 4.375em;
	border-radius: 0;
	overflow: hidden;
	background-position: center bottom;
	background-size: cover;
	background-repeat: no-repeat;
}

.socialv-widget-menu ul.socialv-post li .post-img img {
	width: 4.375em;
	height: 4.375em;
	border-radius: 0;
	margin-right: 1em;
	background: var(--global-white-light-color);
}

.socialv-widget-menu ul.socialv-post li .post-img {
	display: flex;
}

.socialv-widget-menu ul.socialv-post li {
	margin-bottom: 1.5em;
}

.socialv-widget-menu ul.socialv-post li:last-child {
	margin-bottom: 0;
}

.socialv-widget-menu .post-img .post-blog .blog-box ul li a i {
	color: var(--color-theme-primary);
}

.socialv-widget-menu .post-img .post-blog a.new-link:hover h5 {
	color: var(--color-theme-primary);
}

.socialv-widget-menu .post-blog {
	line-height: 0;
}

.socialv-widget-menu .post-img .post-blog .blog-box ul li {
	margin-bottom: 0;
}

.socialv-widget-menu .post-img .post-blog a.new-link {
	font-size: var(--font-size-normal);
}

.socialv-widget-menu .post-img .post-blog a.new-link h5 {
	color: var(--global-font-title);
	line-height: 1.875em;
	display: block;
	overflow: hidden;
	margin-top: .625em;
}

/*------------------  Latest Blog Post  --------------------------*/

.socialv-blog-box .socialv-blog-detail .socialv-blog-meta ul {
	margin: 0 0 .625em;
	padding: 0;
	line-height: 1em;
}

.socialv-blog-box .socialv-blog-image {
	position: relative;
	text-align: center;
	display: inline-block;
	float: left;
	width: 100%;
}

.socialv-blog-box .socialv-blog-image img {
	margin-bottom: 0;
	border-radius: var(--border-radius) var(--border-radius) 0 0;
}

.socialv-blog-box .blog-title {
	margin: 0 0 .625em;
}

.socialv-blog-box .blog-title a h4 {
	color: var(--global-font-title);
	text-decoration: none;
	display: inline-block;
}

.socialv-blog-box .blog-title a:hover h4,
.socialv-blog-box .blog-title a:hover {
	color: var(--color-theme-primary);
	text-decoration: none;
}

.socialv-blog-box .socialv-blog-detail {
	padding: 2em;
	display: inline-block;
	float: left;
	width: 100%;
}

.socialv-blog-box .socialv-blog-detail .wp-block-cover p {
	margin: 0;
	color: var(--color-theme-white);
}

.wp-block-search .wp-block-search__input {
	border: .0625em solid var(--border-color-light);
	padding-right: 3.125em;
	padding-left: 1em;
	border-radius: var(--border-radius);
}

.socialv-blog-box .socialv-blog-detail p {
	margin-bottom: 1em;
}

.socialv-blog-box .blog-footer .socialv-blogtag {
    margin-top: 0;
}

.socialv-blog-box .socialv-blogtag {
	padding: 0;
	display: inline-block;
	align-items: center;
	width: 100%;
	margin-top: 1em;
	margin-bottom: 0;
}

.socialv-blogtag a:visited {
	background-color: var(--color-theme-primary);
	color: var(--color-theme-white)
}

.socialv-blog-box .socialv-blogtag li.socialv-label {
	color: var(--global-font-title);
	font-size: var(--font-size-body);
	font-weight: var(--font-weight-semi-bold);
	letter-spacing: var(--letter-spacing-one);
	line-height: normal;
	padding-right: 0;
}

.wp-block-media-text.alignwide {
	margin-bottom: 1em
}

.socialv-blog-box .socialv-blogtag .socialv-tag-title {
	margin-top: .313em;
	font-weight: var(--font-weight-semi-bold);
	line-height: var(--button-line-height);
	letter-spacing: var(--font-letter-spacing-link);
	color: var(--global-font-title);
}

.socialv-blog-box .socialv-blogtag .socialv-tag-title svg {
	color: var(--global-font-color);
	font-size: var(--font-size-normal);
}

.socialv-blog-box .socialv-blogtag li {
	list-style: none;
	float: left;
	margin: 0 .325em .625em 0;
	padding: 0 .6em 0 0;
	position: relative;
}

.socialv-blog-box .socialv-blogtag li a {
	display: block;
	color: var(--global-font-color);
	font-size: var(--font-size-body) !important;
	line-height: 1.313em;
}

.socialv-blog-box .socialv-blogtag li a:before {
	position: absolute;
	content: "";
	background: var(--global-font-color);
	top: 50%;
	right: .2em;
	height: .8em;
	width: 1px;
	display: block;
	transform: rotate(10deg) translateY(-50%);
}

.socialv-blog-box .socialv-blogtag li:last-child a:before {
	display: none;
}

.socialv-blog-box .socialv-blogtag li a:hover {
	color: var(--color-theme-primary);
}

.socialv-blog-box .socialv-blogcat {
	padding: 0;
	display: inline-block;
	width: 100%;
	position: static;
	margin: 1.875em 0 0;
}

.socialv-blog-box .socialv-blogcat li {
	list-style: none;
	float: left;
	margin: 0 .625em .625em 0;
}

.socialv-blog-box .socialv-blogcat li a {
	background: rgba(41, 41, 41, 0.76);
	color: var(--global-font-color);
	padding: .313em .625em;
	text-transform: uppercase;
	font-size: var(--font-size-normal);
}

.widget .calendar_wrap .wp-calendar-table {
	background: var(--color-theme-white-box);
}

figcaption.blocks-gallery-caption {
	margin-bottom: 1em;
}

.has-post-thumbnail .socialv-blog-box .socialv-blog-detail {
	display: inline-block;
	float: left;
	width: 100%;
}

.socialv-blog-box .socialv-blog-detail blockquote p,
.socialv-blog-box .socialv-blog-detail blockquote cite {
	margin-bottom: 0;
	color: var(--global-font-color);
}

.socialv-blog-box .socialv-blog-detail .blockquote {
	margin-top: 0;
}

.blog-content .wp-audio-shortcode {
	margin-bottom: 1em;
}

.post-password-form input {
	float: none;
}

.post-password-form input[type=submit] {
	background: var(--color-theme-primary);
	color: var(--color-theme-white);
	width: auto;
	padding: 0 1.563em;
	cursor: pointer;
}

.blog .pagination,
.pagination {
	margin-top: 1.5em;
	justify-content: center;
}

.socialv-blog-detail ul.wp-block-archives,
.socialv-blog-detail ul.wp-block-latest-posts,
.socialv-blog-detail ul.wp-block-categories {
	padding: 0;
}

.socialv-blog-detail .blog-content .wp-block-archives,
.wp-block-archives-dropdown,
.socialv-blog-detail .blog-content .wp-block-categories,
.socialv-blog-detail .blog-content .wp-block-latest-posts {
	margin: 0 0 1.875em;
	padding: 0;
}

.socialv-blog-detail .blog-content .wp-block-archives li,
.socialv-blog-detail .blog-content .socialv-blog-detail .blog-content .wp-block-categories li,
.socialv-blog-detail .blog-content .wp-block-latest-posts li {
	list-style-type: none;
}

.widget .menu-testing-menu-container .menu-item .toggledrop {
	display: none;
}

.post_format-post-format-chat .socialv-blog-box .socialv-blog-detail p {
	margin-top: 0;
	margin-bottom: 1.5em;
}

.fourzero-image img {
    width: 50%;
}

/* -----------------  responsive css  -------------------------- */

@media(max-width:767px) {

	.page-numbers li .prev.page-numbers,
	.page-numbers li .next.page-numbers {
		display: none;
	}

	.widget_categories ul ul.children,
	.widget_pages ul ul.children {
		padding-left: 0;
	}

	.post .socialv-blog-box .socialv-blog-head .entry-title {
		font-size: var(--font-size-h4);
		line-height: normal;
	}

	.socialv-blog-box .socialv-blog-detail {
		padding: 1em;
	}
	.socialv-blog-box .socialv-blog-image > .gallery.gallery-size-thumbnail {
		padding: 1em 1em 0;
	}
}