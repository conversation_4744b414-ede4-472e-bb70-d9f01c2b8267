"use strict";if("undefined"==typeof iqSearchInputs)for(var _iqSearchInputs=document.getElementsByClassName("ajax_search_input"),ind=0;ind<_iqSearchInputs.length;ind++){var element=_iqSearchInputs[ind];element.addEventListener("keyup",function(e){getAjaxSearch(e)})}var getAjaxSearch=_.debounce(function(e){var t,a=e.target,e=a.value;3<e.length?(a.closest(".header-search").querySelector(".socialv-search-result").classList.remove("search-result-dislogbox"),(t=new FormData).append("action","ajax_search_content"),t.append("keyword",e),(e=new XMLHttpRequest).open("POST",socialv_loadmore_params.ajaxurl,!0),e.onload=function(){var e,t;200<=this.status&&this.status<400?(e=JSON.parse(this.response).data.content,t=JSON.parse(this.response).data.details,a.closest(".header-search").querySelector(".socialv-search-activity-content").innerHTML=e,a.closest(".header-search").querySelector(".item-footer").innerHTML=void 0!==t?t:""):a.closest(".header-search").querySelector(".socialv-search-activity-content").innerHTML=""},e.onerror=function(){a.closest(".header-search").querySelector(".socialv-search-activity-content").innerHTML=""},e.onprogress=function(){a.closest(".header-search").querySelector(".socialv-search-activity-content").innerHTML='<li><span class="socialv-loader"></span></li>'},e.send(t)):a.closest(".header-search").querySelector(".socialv-search-result").classList.add("search-result-dislogbox")},500);document.addEventListener("DOMContentLoaded",function(){var t=document.querySelector(".socialv-search-result");null!==t&&document.addEventListener("click",function(e){e=e.target;t.contains(e)||t.classList.add("search-result-dislogbox")})}),document.addEventListener("DOMContentLoaded",function(){document.querySelectorAll(".socialv-friendship-btn").forEach(function(e){e.addEventListener("click",function(e){e.preventDefault(),e.stopPropagation();var a=this,e=a.getAttribute("data-friendship-id"),t=a.classList.contains("accept")?"friends_accept_friendship":"friends_reject_friendship",o=new XMLHttpRequest;return o.open("POST",ajaxurl),o.setRequestHeader("Content-Type","application/x-www-form-urlencoded"),o.onreadystatechange=function(){var e,t;o.readyState===XMLHttpRequest.DONE&&(200===o.status?(t=(e=JSON.parse(o.responseText)).data.feedback,a.closest(".socialv-friend-request").querySelector(".response").innerHTML=t,e.success&&a.closest(".request-button").remove(),(t=document.getElementById("notify-count"))&&(1<(e=parseInt(t.textContent))?t.textContent=e-1:(t.textContent="",t.classList.remove("notify-count")))):console.error("An error occurred during the AJAX request."))},o.send("action=socialv_ajax_addremove_friend&friendship_id="+e+"&data_action="+t),!1})})}),document.addEventListener("click",function(e){var t;e.target.closest(".btn-dropdown")&&(0!==(t=document.querySelectorAll(".sharing-options")).length?t.forEach(function(e){e.classList.remove("open")}):document.querySelectorAll(".service-buttons").forEach(function(e){e.style.display="none"})),e.target.closest(".socialv-header-right .dropdown-toggle")&&document.querySelector(".socialv-search-result").classList.add("search-result-dislogbox")}),document.addEventListener("click",function(e){for(var t=e.target;t;){if(t.classList.contains("share-btn")){e.preventDefault();var a=t.parentElement.querySelector(".sharing-options");a&&(a.classList.contains("open")?document.querySelectorAll(".sharing-options").forEach(function(e){e.classList.remove("open")}):(document.querySelectorAll(".sharing-options").forEach(function(e){e.classList.remove("open")}),a.classList.add("open")));break}if(t.classList.contains("bp-share-btn")&&t.classList.contains("generic-button")){e.preventDefault();document.querySelectorAll(".service-buttons").forEach(function(e){e.style.display="none"});a=t.closest(".socialv-activity_comment").nextElementSibling;a&&(a.style.display="block");break}t=t.parentElement}}),jQuery(document).ready(function(t){t("#read_all_notification").on("click",function(e){e.preventDefault(),t.ajax({type:"POST",url:ajaxurl,data:{action:"socialv_read_all_notification"},success:function(e){location.reload()},error:function(e){location.reload()}})})}),document.addEventListener("click",function(e){var t,a,o,n,i;e.target.classList.contains("socialv_copy_url")&&(a=e.target.getAttribute("data_url"),t=e.target.outerHTML,o=e.target),e.target.classList.contains("icon-copying")&&(a=(i=e.target.parentElement).getAttribute("data_url"),t=i.outerHTML,o=e.target.parentElement),a&&((n=o.parentElement).innerHTML='<i class="icon-loader-circle"></i>',(i=document.createElement("textarea")).value=a,document.body.appendChild(i),i.select(),document.execCommand("copy"),document.body.removeChild(i),setTimeout(function(){n.innerHTML='<i class="icon-circle-check"></i>'},2e3),setTimeout(function(){n.innerHTML=t},3e3))}),document.addEventListener("click",function(e){if(e.target.classList.contains("show-activity-comments")){e.preventDefault();var e=e.target.getAttribute("activity_id"),o=document.querySelector(".activitypopup .modal-dialog .modal-content"),n=document.querySelector(".activitypopup .modal-dialog .modal-content .modal-body"),i=(document.querySelector(".activitypopup .modal-dialog .modal-content .modal-header"),document.querySelector(".activitypopup .modal-dialog .modal-content .modal-header .modal-user-name")),r=document.querySelector(".activitypopup .modal-dialog .modal-content .modal-footer");for(o.classList.add("loading-popup");n.firstChild;)n.removeChild(n.firstChild);i.innerHTML="",r.innerHTML="",jQuery.ajax({type:"POST",url:ajaxurl,data:{action:"socialv_get_popup_activity",activity_id:e},dataType:"json",success:function(e){e=e.data.activity_html,e=jQuery(e);e.find('[name="ac_form_submit"]').attr("name","socialv_ac_form_submit");var t=e.find(".socialv-form").prop("outerHTML"),a=e.find(".activity-header p").prop("outerHTML"),e=e.prop("outerHTML"),a=(o.classList.remove("loading-popup"),i.insertAdjacentHTML("beforeend",a),n.insertAdjacentHTML("beforeend",e),r.innerHTML=t,r.querySelector("form"));a&&(a.style.display="block")},error:function(e){console.error("Error occurred:",e.responseText)}})}}),jQuery(document).ready(function(c){c(document).on("click","a.share_activity-share, i.icon-share-box",function(e){e.preventDefault();var n=e.target.getAttribute("data-post-id"),i=c(".shareactivitypopup .modal-body .share_activity-content"),r=c(".shareactivitypopup .modal-header .modal-user-name");c(".shareactivitypopup .modal-footer");c("#shareactivitypopup").modal("show"),i.empty(),r.text(""),i.addClass("loading-popup"),c.ajax({type:"POST",url:ajaxurl,data:{action:"socialv_share_on_activity_click",post_id:n},success:function(e){var t,a,o;e.success?(t=e.data.activity_html,(o=(t=c(t)).find(".socialv-form")).find(".send-comment-btn .ac_form_submit").remove(),o=o.prop("outerHTML"),t.find(".socialv-activity-header-right").remove(),a=t.find(".socialv-activity-header").prop("outerHTML"),r.html(a+o),(a=t.find(".activity-content").clone()).find(".socialv-meta-details, .socialv-comment-main").remove(),i.removeClass("loading-popup"),i.append(a),c("#share-btn").data("post-id",n),0===i.find(".activity-media").length&&(o=e.data.media_meta)&&i.append(o)):console.log("Error: "+e.data.message)},error:function(e,t,a){console.log("Error fetching activity details: "+a)}})}),c(document).on("click",".socialv-reshare-post",function(e){e.preventDefault();var e=c(this).data("post-id"),t=c("#comment-input").val();c.ajax({type:"POST",url:ajaxurl,data:{action:"socialv_post_share_activity",activity_id:e,commentText:t},success:function(e){c("#shareactivitypopup").modal("hide")},error:function(e,t,a){console.error("Error reposting activity: "+a),c("#shareactivitypopup").modal("hide")}})})}),jQuery(document).ready(function(r){r(document).on("click","a.show_file_preview",function(e){e.preventDefault();var e=r(this).data("post-id"),n=r(".showfilepreview .modal-body .file-content").empty().addClass("loading-popup"),i=r(".showfilepreview .modal-header .modal-user-name").text("");r("#showfilepreview").modal("show"),r.post(ajaxurl,{action:"socialv_share_on_activity_click",post_id:e},function(e){var t,a,o;e.success?(a=(t=r(e.data.activity_html)).find(".socialv-form").find(".ac_form_submit").remove().end().prop("outerHTML"),o=t.find(".socialv-activity-header-right").remove().end().find(".socialv-activity-header").prop("outerHTML"),i.html(o+a),o=t.find(".mpp-attached-pdf-media-item").first().clone(),n.removeClass("loading-popup").append(o)):console.error("Error: "+e.data.message),console.log(e.data.activity_html)}).fail(function(e,t,a){console.error("Error fetching activity details: "+a)})})}),document.addEventListener("DOMContentLoaded",function(){function a(){var t=0<document.querySelectorAll(".mpp-uploaded-media-item").length;e.forEach(function(e){t&&e!==o?(e.style.pointerEvents="none",e.classList.add("disabled")):(e.style.pointerEvents="",e.classList.remove("disabled"))})}var e=document.querySelectorAll("#mpp-activity-upload-buttons a"),o=null;new MutationObserver(a).observe(document.body,{childList:!0,subtree:!0}),e.forEach(function(e){e.addEventListener("click",function(){o=e,a()})}),document.addEventListener("click",function(e){var t;e.target.classList.contains("mpp-delete-uploaded-media-item")&&(e.preventDefault(),null!=(t=e.target.closest(".mpp-uploaded-media-item"))&&t.remove(),a()),e.target.classList.contains("mpp-upload-container-close")&&(e.preventDefault(),document.querySelectorAll(".mpp-uploaded-media-item").forEach(function(e){e.remove()}),o=null,a())}),a()}),jQuery(document).on("click",".socialv_suggestion_add",function(e){var t=jQuery(this).find("i.icon-add");t.length&&t.removeClass("icon-add").addClass("icon-check")});