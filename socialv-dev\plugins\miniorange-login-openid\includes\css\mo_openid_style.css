.mo-nav-tab {
	float: left;
	border: 1px solid #ccc;
	border-bottom: none;
	margin-left: .5em;
	padding: 5px 10px;
	font-size: 14px;
	line-height: 1.71428571;
	font-weight: 600;
	background: #F3F5F6;
	color: #000000;
	text-decoration: none;
	white-space: nowrap;
}

.mo-nav-tab:focus, .mo-nav-tab:hover {
	background-color: #0867B2;
	color: #ffffff;
}

.mo-nav-tab-active, .mo-nav-tab-active:focus, .mo-nav-tab-active:focus:active, .mo-nav-tab-active:hover {
	border-bottom: 1px solid #0867B2;
	background: #0867B2;
	color: #ffffff;
}

#mo_shortcode_table {
	font-family: "Trebuchet MS", Arial, Helvetica, sans-serif;
	border-collapse: collapse;
	width: 80%;
}

#mo_shortcode_table td, #mo_shortcode_table th {
	border: 1px solid #000000;
	padding: 8px;
}

#mo_shortcode_table tr:nth-child(even){background-color: #6d97dd;}

#mo_shortcode_table tr:hover {background-color: #ddd;}

#mo_shortcode_table th {
	padding-top: 12px;
	padding-bottom: 12px;
	text-align: left;
	background-color: #6d97dd;
	color: black;
}

#mo_openid_notice_snackbar {
	visibility: hidden;
	min-width: 250px;
	margin-left: -125px;
	color: #fff;
	text-align: center;
	border-radius: 2px;
	padding: 16px;
	position: fixed;
	z-index: 1;
	top: 8%;
	right: 30px;
	font-size: 17px;
}

#mo_openid_notice_snackbar.show {
	visibility: visible;
	-webkit-animation: fadein 0.5s, fadeout 0.5s 3.5s;
	animation: fadein 0.5s, fadeout 0.5s 3.5s;
}

@-webkit-keyframes fadein {
	from {right: 0; opacity: 0;}
	to {right: 30px; opacity: 1;}
}

@keyframes fadein {
	from {right: 0; opacity: 0;}
	to {right: 30px; opacity: 1;}
}

@-webkit-keyframes fadeout {
	from {right: 30px; opacity: 1;}
	to {right: 0; opacity: 0;}
}

@keyframes fadeout {
	from {right: 30px; opacity: 1;}
	to {right: 0; opacity: 0;}
}


#mo_openid_ajax_wait_fade {
	display: none;
	position:absolute;
	top: 0%;
	left: 0%;
	width: 100%;
	height: 100%;
	background-color: #ababab;
	z-index: 1001;
	-moz-opacity: 0.8;
	opacity: .70;
	filter: alpha(opacity=80);
}

#mo_openid_ajax_wait_img {
	display: none;
	position: absolute;
	top: 40%;
	left: 45%;
	width: 6%;
	height: 8%;
	padding:30px 15px 0px;
	border: 3px solid #ababab;
	box-shadow:1px 1px 10px #ababab;
	border-radius:20px;
	background-color: white;
	z-index: 1002;
	text-align:center;
	overflow: auto;
}

/*support form*/
/* The Modal (background) */

.mo_openid_modal_rateus {
	display: none; /* Hidden by default */
	position: fixed; /* Stay in place */
	z-index: 1; /* Sit on top */
	padding-top: 100px; /* Location of the box */
	left: 0;
	top: 0;
	width: 100%; /* Full width */
	height: 100%; /* Full height */
	overflow: auto; /* Enable scroll if needed */
	background-color: rgb(0,0,0); /* Fallback color */
	background-color: rgba(0,0,0,0.8); /* Black w/ opacity */
}

/* Modal Content */
.mo_openid_rateus_modal_content {
	background-color: #0867b2;
	margin: auto;


	border: 1px solid #888;
	min-height: 20%;
	margin-left: auto;
	margin-right: auto;
	width: 35%;
	height: 50%;
}

/* The Close Button */
.mo_openid_rateus_close {
	color: #aaaaaa;
	float: right;
	font-size: 28px;
	font-weight: bold;
}

.mo_openid_rateus_close:hover,
.mo_openid_rateus_close:focus {
	color: white;
	text-decoration: none;
	cursor: pointer;
}

/*Rate us feedback close button*/
.mo_openid_rateus_feedback_close {
	color: #aaaaaa;
	float: right;
	font-size: 28px;
	font-weight: bold;
}
.mo_openid_rateus_feedback_close:hover,
.mo_openid_rateus_feedback_close:focus {
	color: white;
	text-decoration: none;
	cursor: pointer;
}


legend {
	font-size: 1.4em;
	margin-bottom: 10px;
}

.mo_openid_modal_rateus_style {
	font-family: Georgia, "Times New Roman", Times, serif;
	background: rgba(255, 255, 255, .1);
	border: none;
	border-radius: 4px;
	font-size: 15px;
	margin: 0;
	outline: 0;
	padding: 10px;
	width: 100%;
	box-sizing: border-box;
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	background-color: #FFFFFF;
	color: #8a97a0;
	-webkit-box-shadow: 0 1px 0 rgba(0, 0, 0, 0.03) inset;
	box-shadow: 0 1px 0 rgba(0, 0, 0, 0.03) inset;
	margin-bottom: 30px;
}

select {
	-webkit-appearance: menulist-button;
	height: 35px;
}

.number {
	background: #1abc9c;
	color: #fff;
	height: 30px;
	width: 30px;
	display: inline-block;
	font-size: 0.8em;
	margin-right: 4px;
	line-height: 30px;
	text-align: center;
	text-shadow: 0 1px 0 rgba(255, 255, 255, 0.2);
	border-radius: 15px 15px 15px 0px;
}

.mo_openid_modal_rateus_style_button{
	position: relative;
	display: block;
	padding: 19px 39px 18px 39px;
	color: #FFF;
	margin: 0 auto;
	background: #0867b2;
	font-size: 18px;
	text-align: center;
	font-style: normal;
	width: 100%;
	border: 1px solid #0867b2;
	border-width: 1px 1px 3px;
	margin-bottom: 10px;
}

input[type="submit"]:hover,
input[type="button"]:hover {
	background: #0867b2;
}

.mo_openid_star-cb-group {
	/* remove inline-block whitespace */
	font-size: 0;
	/* flip the order so we can use the + and ~ combinators */
	unicode-bidi: bidi-override;
	direction: rtl;
	/* the hidden clearer */
}
.mo_openid_star-cb-group * {
	font-size: 1rem;
}
.mo_openid_star-cb-group > input {
	display: none;
}
.mo_openid_star-cb-group > input + label {
	/* only enough room for the star */
	display: inline-block;
	overflow: hidden;
	text-indent: 9999px;
	width: 26px;
	white-space: nowrap;
	cursor: pointer;
}
.mo_openid_star-cb-group > input + label:before {
	display: inline-block;
	font-family: Helvetica, arial, sans-serif;
	text-indent: -9999px;
	font-size: 2em;
	content: "☆";
	color: #888;
}
.mo_openid_star-cb-group > input:checked ~ label:before, .mo_openid_star-cb-group > input + label:hover ~ label:before, .mo_openid_star-cb-group > input + label:hover:before {
	content: "★";
	color: #ecf00e;
	text-shadow: 0 0 1px #333;
}
.mo_openid_star-cb-group > .mo_openid_star-cb-clear + label {
	text-indent: -9999px;
	width: .5em;
	margin-left: -.5em;
}
.mo_openid_star-cb-group > .mo_openid_star-cb-clear + label:before {
	width: .5em;
}
.mo_openid_star-cb-group:hover > input + label:before {
	content: "☆";
	color: #888;
	text-shadow: none;
}
.mo_openid_star-cb-group:hover > input + label:hover ~ label:before, .mo_openid_star-cb-group:hover > input + label:hover:before {
	content: "★";
	color: #e52;
	text-shadow: 0 0 1px #333;
}

.mo-openid-star-back-rateus {
	border: 8px solid;
	border-color: #0867b2;
	background: #FFFFFF;
	width: max-content;
	border-radius: 1px;
	padding: 1em 1.5em 0.9em;
	margin: 1em auto;
}

#wpfooter {
	position: unset;
}
.mo_support-help-button{
	padding: 4px 8px;
	float: right;
	background-color: #32373c;
	color: #fff;
	border: 1px solid #32373c;
	border-radius: 4px;
	text-shadow: none;
	font-size: 16px;
	line-height: normal;
	cursor: pointer;
	position: fixed;
	left: 94%;
	width:10% ;
	top: 35%;
	height: 5%;
	z-index: 9999;
	transform: rotateZ(89deg);
	letter-spacing: 2px;
}

.mo-openid-premium{
	font-size: 10px;
	background: #236d31;
	color: white;
	font-weight: 600;
	padding: 1px 4px;
	border-radius: 4px;
	font-family: monospace;
	position: relative;
	left: 11%;
}
.mo-openid-premium:hover{

	color: white;

}

/*premium features*/
.card-content {
	border: 5px solid #CCC;
	border-radius: 3px;
	padding: 25px 25px 10px 25px;
	min-height: 250px;
	min-width: 350px;
}
.card-content * {
	cursor: pointer;
}
.card-wrapper {
	position: relative;
	width: 350px;
	height: 300px;
	min-width: 350px;
	float: left;
	margin-right: 25px;
}
.c-card {
	position: absolute;
	top: 0;
	left: 0;
	opacity: 0;
	visibility: hidden;
}
.c-card ~ .card-content {
	transition: all 500ms ease-out;
}
.c-card ~ .card-content .card-state-icon {
	position: absolute;
	top: 5px;
	right: 5px;
	z-index: 2;
	width: 20px;
	height: 20px;

	background-position: 0 0;
	transition: all 100ms ease-out;
}
.c-card ~ .card-content:before {
	position: absolute;
	top: 1px;
	right: 1px;
	content: "";
	width: 0;
	height: 0;
	border-top: 00px solid #CCC;
	border-left: 00px solid transparent;
	transition: all 200ms ease-out;
}
.c-card ~ .card-content:after {
	position: absolute;
	top: 1px;
	right: 1px;
	content: "";
	width: 0;
	height: 0;
	border-top: 00px solid #FFF;
	border-left: 00px solid transparent;
	transition: all 200ms ease-out;
}
.c-card ~ .card-content:hover {
	/*box-shadow: 3px 5px 3px rgba(0,0,0,0.16), 0 6px 9px rgba(0,0,0,0.23)*/
}
.c-card ~ .card-content:hover .card-state-icon {
	background-position: -30px 0;
}
.c-card ~ .card-content:hover:before {
	border-top: 52px solid #0867b2;
}
.c-card:checked ~ .card-content {
	border: 5px solid #0867b2;
}
.c-card:checked ~ .card-content .card-state-icon {
	background-position: -90px 2px;
}
.c-card:checked ~ .card-content:before {
	border-top: 52px solid #0867b2;
}
.c-card:checked ~ .card-content:after {
	border-top: 52px solid #0867b2;
}
.c-card:checked:hover ~ .card-content .card-state-icon {
	background-position: -60px 2px;
}
.c-card:checked:hover ~ .card-content:before {
	border-top: 52px solid #0867b2;
}
.c-card:checked:hover ~ .card-content:after {
	border-top: 52px solid #0867b2;
}

/* Style the buttons inside the tab */
.mo_openid_highlight{
	background-color:#32373c;
	padding: 0.004%;
}
.mo_openid_note_style{
	display:block;margin-top:10px;background-color:#ebf6fc;padding:10px;border:solid 1px darkblue;
}
.mo_openid_tooltip {
	position: relative;
	/*opacity: 2%;*/
}

.mo_openid_tooltip .mo_openid_tooltiptext {
	visibility: hidden;
	width: 290px;
	background-color:gray;
	color:#ffffff;
	text-align: center;
	border-radius: 6px;
	padding: 5px 0;
	position: absolute;
	z-index: 1;
	top: 95%;
	left: 0%;
}

.mo_openid_tooltip .mo_openid_tooltiptext::after {
	content: "";
	position: absolute;
	top: 50%;
	right: 100%;
	margin-top: -5px;
	border-width: 5px;
	border-style: solid;
}
.mo_openid_tooltip:hover .mo_openid_tooltiptext {
	visibility: visible;
}

.mo_openid_tab {
	float: left;
	background-color: #32373C;
	/*width: 15%;*/
	/*height: 350px;*/
	box-shadow: -1px 0px 3px #777777;
}
.mo_openid_tab a {
	display: block;
	background-color: #32373C;
	color: white;
	padding: 11px 7px;
	text-decoration: none;
	outline: none;
	text-align: left;
	cursor: pointer;
	transition: 0.3s;
	font-size: 14px;
}
.mo_openid_tab a:hover {
	background-color: #585858;
	color: white;
	transform: scale(1.1);
}
.mo_sl_header_text a:hover {
	background-color: #585858;
	color: white;
	transform: scale(1.5);
}

.mo_sl_header_text {
	color: white; text-decoration: none;font-size: 15px;
	font-weight: 5;
}
.mo_sl_header_text a:hover {
	 color: #ea1414; }

.mo_openid_tab a.tablinks_active{
	background-color: #585858;
	color: white;
	font-weight: bold;
}

.mo-openid-switch-app {
	position: relative;
	display: inline-block;
	width: 50px;
	height: 26px;
	bottom: 5px;
}

.mo-openid-switch-app input {display:none;}

.mo-openid-slider-app {
	position: absolute;
	cursor: pointer;
	top: 2px;
	left: 0;
	right: 3px;
	bottom: 2px;
	background-color: grey;
	-webkit-transition: .4s;
	transition: .4s;
}
.mo-openid-slider-app:before {
	position: absolute;
	content: "";
	height: 12px;
	width: 12px;
	left: 4px;
	bottom: 5px;
	background-color: white;
	-webkit-transition: .4s;
	transition: .4s;
}
input:checked + .mo-openid-slider-app {
	background-color: #0085ba;
}
input:focus + .mo-openid-slider-app {
	box-shadow: 0 0 1px #2196F3;
}
input:checked + .mo-openid-slider-app:before {
	-webkit-transform: translateX(26px);
	-ms-transform: translateX(26px);
	transform: translateX(26px);
}
input:checked+ .mo-openid-slider-app .on
{display: block;}
input:checked + .mo-openid-slider-app .off
{display: none;}
.mo-openid-slider-app.round {
	border-radius: 34px;
}
.mo-openid-slider-app.round:before {
	border-radius: 50%;
}
.mo-openid-sort-apps {
	position: relative;
	padding: 15px;
}

.mo-openid-sort-apps .mo-openid-sort-apps-div {
	position: relative;
	float: left;
	width: 215px;
	height: 120px;
	margin: 15px;
	display: flex;
	flex-flow: column;
}

.mo-openid-sort-apps .mo-openid-sort-apps-i-div {
	height: 166px;
	display: flex;
	flex-flow: column;
	justify-content: center;
	align-items: center;
}

.mo-openid-sort-apps-move {
	position: absolute;
	left: 0;
	top: 0;
	width: 9%;
	height: 17%;
	cursor: move;
}

.mo-openid-hover-div-sett:hover .mo-openid-sort-apps-move{
	background: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABsAAAAiCAYAAACuoaIwAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAA+tpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDUuNi1jMTExIDc5LjE1ODMyNSwgMjAxNS8wOS8xMC0wMToxMDoyMCAgICAgICAgIj4gPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvIiB4bWxuczpkYz0iaHR0cDovL3B1cmwub3JnL2RjL2VsZW1lbnRzLzEuMS8iIHhtbG5zOnhtcE1NPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvbW0vIiB4bWxuczpzdFJlZj0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wL3NUeXBlL1Jlc291cmNlUmVmIyIgeG1wOkNyZWF0b3JUb29sPSJBZG9iZSBQaG90b3Nob3AgQ0MgMjAxNSAoV2luZG93cykiIHhtcDpDcmVhdGVEYXRlPSIyMDE4LTAxLTE3VDE1OjE4OjM5KzAxOjAwIiB4bXA6TW9kaWZ5RGF0ZT0iMjAxOC0wMS0xN1QxNjoxMTo0MSswMTowMCIgeG1wOk1ldGFkYXRhRGF0ZT0iMjAxOC0wMS0xN1QxNjoxMTo0MSswMTowMCIgZGM6Zm9ybWF0PSJpbWFnZS9wbmciIHhtcE1NOkluc3RhbmNlSUQ9InhtcC5paWQ6QjhCNkM3MkZGQjk4MTFFN0E4RDJBRUZBQTI4OUVBNzIiIHhtcE1NOkRvY3VtZW50SUQ9InhtcC5kaWQ6QjhCNkM3MzBGQjk4MTFFN0E4RDJBRUZBQTI4OUVBNzIiPiA8eG1wTU06RGVyaXZlZEZyb20gc3RSZWY6aW5zdGFuY2VJRD0ieG1wLmlpZDpCOEI2QzcyREZCOTgxMUU3QThEMkFFRkFBMjg5RUE3MiIgc3RSZWY6ZG9jdW1lbnRJRD0ieG1wLmRpZDpCOEI2QzcyRUZCOTgxMUU3QThEMkFFRkFBMjg5RUE3MiIvPiA8L3JkZjpEZXNjcmlwdGlvbj4gPC9yZGY6UkRGPiA8L3g6eG1wbWV0YT4gPD94cGFja2V0IGVuZD0iciI/Po8u91EAAABlSURBVHjaYvz//z8DvQATAx3BqGWjlg0ey1iIUKMDxHFQ9iIgvkKkHFk+AxnGD8VxJMhRHIyMZMoRbRkoeD4B8QcgXkiCHKZrRsvG0aQ/mvRHk/5o0h9N+qNJf9SyUcuoCAACDABr5TA7L7qpSQAAAABJRU5ErkJggg==');
}

.mo-openid-sort-message {
	position: absolute;
	left: 12%;
	top: 2%;
	color: #fff;
	line-height: 22px;
	font-size: 12px;
}

.mo-openid-hover-div-sett:hover #mo-openid-hover-app-sett-show{
	display: block;
	transition: .4s;
}

.mo-openid-hover-div-sett #mo-openid-hover-app-sett-show {
	height: 29px;
	background-color: #f0f0f0;
	padding: 5px;
	color: #8e8ef6b8;
	font-size: 16px;
	display: none;
}

.mo-openid-hover-div-sett .mo-openid-capp-sett#mo-openid-hover-app-sett-show{
	height: 17px;
	background-color: #f0f0f0;
	padding: 5px;
	color: #8e8ef6b8;
	font-size: 16px;
}

/* Popup Open button */

.mo_openid_popup {
	position:fixed;
	top:0px;
	left:2%;
	background:rgba(0,0,0,0.75);
	width:100%;
	height:100%;
	display:none;
}

/* Popup inner div */
.mo_openid_popup-content {
	width: 75%;
	margin: 0 auto;
	box-sizing: border-box;
	padding: 20px;
	margin-top: 85px;
	margin-left: 215px;
	box-shadow: 0px 2px 6px rgba(0, 0, 0, 1);
	border-radius: 3px;
	background: #fff;
	overflow-y: auto;
	position: relative;
	height: 82%;
}
.mo_openid_close-button {
	/*width: 25px;*/
	height: 25px;
	position: absolute;
	top: 10px;
	right: 10px;
	border-radius: 20px;
	/*background: rgba(0,0,0,0.8);*/
	font-size: 17px;
	text-align: center;
	color: #6e6666;
	text-decoration:none;
}

.mo_openid_close-button:hover {
	/*background: rgba(0,0,0,1);*/
}

@media screen and (max-width: 720px) {
	.mo_openid_popup-content {
		width:90%;
	}
}

a.mo-add-hover:hover{
	text-decoration: underline;
}
a.mo-pugin-add-link-sl {
	text-decoration: none;
	color: black;
}

a.mo-pugin-add-comp-link-sl {
	text-decoration: none;
	color: gray;
}
.mo_openid_sharecount ul.mo_openid_share_count_icon {
	text-align: center;
}
.mo_openid_sharecount ul.mo_openid_share_count_icon li {
	margin: 0;
	padding: 0;
	list-style: none;
	display: inline-block;
}
.mo_openid_sharecount ul.mo_openid_share_count_icon li span {
	margin: 0;
	width: 60px;
	display: block;
	background: url(../../includes/images/count.png) no-repeat;
	height: 40px;
	overflow: hidden;
	padding: 10px 2px 2px;
	font-size: 17px;
	text-align: center;
	line-height: 24px;
	color: #5a6570;
}
.mo_openid_sharecount ul.mo_openid_share_count_icon li span2 {
	margin: 0;
	width: 60px;
	display: block;
	height: 40px;
	overflow: hidden;
	padding: 10px 2px 2px;
	font-size: 17px;
	text-align: center;
	line-height: 24px;
	color: #5a6570;
}
.mo_openid_mywatermark {
	background-image:url(../../includes/images/sample.png);
	padding-bottom: 20px;
}
.dis{
	pointer-events: none;
}
.mo_openid_tooltip .mo_tooltiptext1 {
	visibility: hidden;
	width: 120px;
	background-color: black;
	color: #fff;
	text-align: center;
	border-radius: 6px;
	padding:10px 5px 5px 5px;
	position: absolute;
	z-index: 1;
	top: 150%;
	left: 50%;
	margin-left: -60px;

}

.mo_openid_tooltip .mo_tooltiptext1::after {
	content: "";
	position: absolute;
	bottom: 100%;
	left: 50%;
	margin-left: -5px;
	border-width: 5px;
	border-style: solid;
	border-color: transparent transparent black transparent;
}

.mo_openid_tooltip:hover .mo_tooltiptext1 {
	visibility: visible;
}

.mo_openid_table_layout {
	background-color:#FFFFFF;
	padding:0px 10px 10px 10px;
	margin-bottom: 10px;
	height:auto;
	min-height:400px;
	border-radius: 0px 15px 15px 0px;
}

.mo_openid_table_layout input[type=text] {
	width: 80%;
}

.mo_openid_table_textbox {
	width:80%;
}
.mo_openid_table_contact {
	width:95%;
}
.mo_openid_settings_table,.mo_openid_display_table {
	width: 100%;
}
.mo_openid_settings_table tr td:first-child {
	width: 30%;
}

.mo_openid_display_table tr td:first-child{
	width: 50%;
}

.mo_openid_display_table .mo_openid_table_textbox {
	width:70%;
}
.mo_openid_login_wid li{
	margin:5px;
	list-style-type:none;
}

.mo-openid-app-share-icons{
	width:35px !important;
	height:35px !important;
	display:inline !important;
}

.mo-openid-share-link{
	border-bottom: 0px !important;
	box-sizing:border-box !important;
}
.mo_image_id {
	width: 40%;
}

div.mo_image_id>a>img.mo_openid_image {
	opacity: 0.6;
	display: block;
	transition: .5s ease;
	backface-visibility: hidden;
}

.mo_image_id:hover .mo_openid_image {
	opacity: 1;
}

.mo_openid_vertical{
	display:inline-block !important;
	overflow: visible;
	z-index: 10000000;
	padding: 10px;
	border-radius: 4px;
	opacity: 1;
	-webkit-box-sizing: content-box!important;
	-moz-box-sizing: content-box!important;
	box-sizing: content-box!important;
	width:40px !important;
	position:fixed;
}
.mo_openid-login-button{
	text-align:center !important;
	color:white !important;
	padding-top:8px !important;
	box-sizing: border-box !important;

}
.mo-custom-share-icon{
	box-sizing:border-box !important;
}

.mo_openid_msgs{
	font-size: 14px !important;
}

.mo_openid_comment_tab {
	margin-left: 0px !important;
	margin-bottom: 20px;
	padding-left: 0px !important;
}

.mo_openid_comment_tab li{
	color: grey;
	margin-right: 5%;
	margin-left: 0px !important;
	list-style: none;
	font-size: 14px;
	cursor:pointer;
	float: left;
}

.mo_openid_selected_tab{
	color: black !important;
	border-bottom: 2px solid #FF5A00;
}

p>a.mo-openid-share-link>img.mo-openid-app-share-icons
{
	box-shadow:0 0 0 0px #fff !important;
}

p>a.mo-openid-share-link
{
	box-shadow: inset 0 -1px 0 rgba(15, 15, 15, 0)!important;
}

.mo-consent{
	font-size:11px !important;
	font-weight:normal;
	color:#72777c;
	padding-bottom: 5px;
}

.mo_copy{
	border: 1px solid #CCCCCC;
	padding:8px;
	background-color:#FBFBFB;
	cursor: pointer;
}

.mo_copy:active{
	background-color:#CCCCCC;
}

/* Tooltip container */
.mo_copytooltip {
	position: relative;
	display: inline-block;
	size: letter;
	/* If you want dots under the hoverable text */
}
/* Tooltip text */
.mo_copytooltip .mo_copytooltiptext {
	visibility: hidden;
	width: 120px;
	background-color:grey;
	color: white;
	text-align: center;
	padding: 5px 0;
	border-radius: 6px;
	font-size: 12px;
	/* Position the tooltip text */
	position: absolute;
	z-index: 1;
	bottom: 125%;
	left: 50%;
	margin-left: -60px;
	font-size: small;
	/* Fade in tooltip */
	opacity: 50;
	transition: opacity 0.3s;
}

/* Tooltip arrow */
.mo_copytooltip .mo_copytooltiptext::after {
	content: "";
	position: absolute;
	top: 100%;
	left: 50%;
	margin-left: -5px;
	border-width: 5px;
	border-style: solid;
	border-color: #555 transparent transparent transparent;
}

/* Show the tooltip text when you mouse over the tooltip container */
.mo_copytooltip:hover .mo_copytooltiptext {
	visibility: visible;
	opacity: 1;
}

/*RAdio button*/

.mo-openid-radio-container {
	display: block;
	position: relative;
	padding-left: 20px;
	margin-bottom: 1px;
	cursor: pointer;

	font-size: 14px;
	-webkit-user-select: none;
	-moz-user-select: none;
	-ms-user-select: none;
	user-select: none;
}

/* Hide the browser's default radio button */
.mo-openid-radio-container input {
	position: absolute;
	opacity: 0;
	cursor: pointer;
}

/* Create a custom radio button */
.mo-openid-radio-checkmark {
	position: absolute;
	top: 0;
	left: 0;
	height: 16px;
	width: 16px;
	background-color: #e5e7eb;
	border-radius: 50%;
}

/* On mouse-over, add a grey background color */
.mo-openid-radio-container:hover input ~ .mo-openid-radio-checkmark {
	background-color: #F0F0F0;
}

/* When the radio button is checked, add a blue background */
.mo-openid-radio-container input:checked ~ .mo-openid-radio-checkmark {
	background-color: #0867b2;
}

/* Create the indicator (the dot/circle - hidden when not checked) */
.mo-openid-radio-checkmark:after {
	content: "";
	position: absolute;
	display: none;
}

/* Show the indicator (dot/circle) when checked */
.mo-openid-radio-container input:checked ~ .mo-openid-radio-checkmark:after {
	display: block;
}

/* Style the indicator (dot/circle) */
.mo-openid-radio-container .mo-openid-radio-checkmark:after {
	top: 5.5px;
	left: 5px;
	width: 6px;
	height: 6px;
	border-radius: 50%;
	background: white;
}

/*textfield css*/
.mo_openid_textfield_css{
	font-family: Arial;
	border: 1px solid;
	border-radius: 1px !important;
	font-size: 13px;
	margin: 0;
	outline: 0;
	padding: 5px;
	width:80%;
	border-color:#0867b2;
	background-color: #FFFFFF;

}

.mo_openid_fix_fontsize{
	font-family: Arial;

	font-size: 14px;
}
.mo_openid_fix_fontsize_semiheading{
	font-family: Georgia, "Times New Roman", Times, serif;
	font-size: 18px;
}

/*chech-box*/

.mo_openid_checkbox_container {
	display: block;
	position: relative;
	padding-left: 22px;
	margin-bottom: 18px;
	cursor: pointer;
	font-size: 14px;
}

/* Hide the browser's default checkbox */
.mo_openid_checkbox_container input {
	position: absolute;
	opacity: 0;
	cursor: pointer;
	height: 0;
	width: 0;
}

/* Create a custom checkbox */
.mo_openid_checkbox_checkmark {
	position: absolute;
	top: 0;
	left: 0;
	height: 18px;
	width: 18px;
	background-color: #e5e7eb;
}

/* On mouse-over, add a grey background color */
.mo_openid_checkbox_container:hover input ~ .mo_openid_checkbox_checkmark {
	background-color: #ccc;
}

/* When the checkbox is checked, add a blue background */
.mo_openid_checkbox_container input:checked ~ .mo_openid_checkbox_checkmark {
	background-color: #2196F3;
}

/* Create the checkmark/indicator (hidden when not checked) */
.mo_openid_checkbox_checkmark:after {
	content: "";
	position: absolute;
	display: none;
}

/* Show the checkmark when checked */
.mo_openid_checkbox_container input:checked ~ .mo_openid_checkbox_checkmark:after {
	display: block;
}

/* Style the checkmark/indicator */
.mo_openid_checkbox_container .mo_openid_checkbox_checkmark:after {
	left: 5px;
	top: 3px;
	width: 4px;
	height: 8px;
	border: solid white;
	border-width: 0 3px 3px 0;
	-webkit-transform: rotate(45deg);
	-ms-transform: rotate(45deg);
	transform: rotate(45deg);
}

.mo_openid_checkbox_container_disable {
	display: block;
	position: relative;
	padding-left: 22px;
	margin-bottom: 18px;
	cursor: context-menu;
	font-size: 14px;
}

/* Hide the browser's default checkbox */
.mo_openid_checkbox_container_disable input {
	position: absolute;
	opacity: 0;
	cursor: context-menu;
	height: 0;
	width: 0;
}

/* Create a custom checkbox */
.mo_openid_checkbox_checkmark_disable {
	position: absolute;
	top: 0;
	left: 0;
	height: 18px;
	width: 18px;
	background-color: #e5e7eb;
}

.mo-openid-radio-container_disable {
	display: block;
	position: relative;
	padding-left: 20px;
	margin-bottom: 1px;
	cursor: pointer;
	font-size: 14px;
	-webkit-user-select: none;
	-moz-user-select: none;
	-ms-user-select: none;
	user-select: none;
}

/* Hide the browser's default radio button */
.mo-openid-radio-container_disable input {
	position: absolute;
	opacity: 0;
	cursor: pointer;
}

/* Create a custom radio button */
.mo-openid-radio-checkmark_disable {
	position: absolute;
	top: 0;
	left: 0;
	height: 16px;
	width: 16px;
	background-color: #e5e7eb;
	border-radius: 50%;
}

/* Advertisement Baner */
.mo_openid_notice {
	background: #fff;
	border: 1px solid #ccd0d4;
	border-left-color: rgb(204, 208, 212);
	border-left-width: 1px;
	border-left-width: 4px;
	box-shadow: 0 1px 1px rgba(0,0,0,.04);
	margin: 5px 15px 2px;
	padding: 1px 12px;
	border-left-color: #069c51;

}
/* Newly Edited for licensing plans  */
#mo-license-table {
	font-family: "Trebuchet MS", Arial, Helvetica, sans-serif;
	border-collapse: collapse;
	width: 100%;
}

#mo-license-table td, #mo-license-table th {
	border: 1px solid #ddd;
	/*text-align: center;*/
	padding: 8px;
}

#mo-license-table tr:nth-child(even){background-color: #f2f2f2;}

#mo-license-table tr:hover {background-color: #ddd;}

#mo-license-table th {
	padding-top: 12px;
	width: 25%;
	padding-bottom: 12px;
	/*text-align: center;*/
	background-color:#E97D68;
	/*background-color: #4CAF50;*/
	color: white;
}

.mo_openid_notice-warning {
	border-left-color: #069c51
}

/* Reports Table CSS Start */

.mo_openid_reports_tables table {
	font-family: Arial, Helvetica, sans-serif;
	border-collapse: collapse;
	width: 98%;
	margin-right: 5%;
}

.mo_openid_reports_tables th {
	border: thin solid #ddd;
	padding: 8px;
	font-weight: bold;
	text-align: left;
	font-size: 16px;
}

.mo_openid_reports_tables td {
	border: thin solid #ddd;
	padding: 8px;
	text-align: left;
	font-size: 14px;

}
.mo_openid_reports_tables tr:nth-child(even){background-color: #f2f2f2;}



.mo_openid_reports_tables th {
	padding-top: 12px;
	padding-bottom: 12px;
	background-color: #1c1c1c;
	color: white;
	text-align: left;
	font-weight: bold;
	font-size: 16px;

}

/* Reports Table CSS End */

/* license toggle CSS */

.mo-openid-lic-bold-cl{
	font-weight: 900;
}
.mo_openid_popup-modal {
	display: none; /* Hidden by default */
	position: fixed; /* Stay in place */
	z-index: 1; /* Sit on top */
	padding-top: 100px; /* Location of the box */
	left: 0;
	top: 0;
	margin-left: 6%;
	width: 100%; /* Full width */
	height: 100%; /* Full height */
	overflow: auto; /* Enable scroll if needed */
	background-color: rgb(0,0,0); /* Fallback color */
	background-color: rgba(0,0,0,0.4); /* Black w/ opacity */
}


/* Modal Content */
.mo_openid_modal-content {
	background-color: #fefefe;
	margin: auto;
	padding: 20px;
	border: 1px solid #888;
	width: 65%;
}


/* The Close Button */
.mo_openid-popup-modal-close {
	color: #aaaaaa;
	float: right;
	font-size: 28px;
	font-weight: bold;
}

.mo_openid-popup-modal-close:hover,
.mo_openid-popup-modal-close:focus {
	color: #000;
	text-decoration: none;
	cursor: pointer;
}

.mo_openid-on-hover-free-fetr:hover,
.mo_openid-on-hover-free-fetr:focus{
	color: #1B79AE;
}
/* license toggle end */

/* Doc Tab CSS Start */

.modt-container {
	width: 94%;
	margin: 3% 3% 0% 3% ;
	text-align:center;
}

.modt-main-panel {
	/*background: rgba(0, 0, 0, 0.270);*/
	border-radius: 0.1em;
	padding: 0.1em;
}

.modt-panel {
	/*background: rgba(0, 0, 0, 0.135);*/
	border-radius: 0.1em;
	padding: 0.1em;
	width: 30%;
	margin-right: 0px;
}

.modt-panel-header,
.modt-panel-content {
	padding: 0.75em;
}

.modt-panel-title {
	line-height: 1;
}

.modt-panel-content {
	background: #fff;
	height: 600px;
}

.modt-panel-content footer{
	height: 40px;
}

.modt-leftbox {
	float:left;
	/*background: rgba(0, 0, 0, 0.270);*/
	width:30%;
	height:300px;
	margin-left: 2%;
	margin-right: 1%;
	/*border-radius: 0.1em;*/
	/*padding: 0.1em;*/
}
.modt-middlebox{
	float:left;
	/*background: rgba(0, 0, 0, 0.270);*/
	width:30%;
	height:300px;
	margin-left: 1%;
	margin-right: 1%;
	/*border-radius: 0.1em;*/
	/*padding: 0.1em;*/
}
.modt-rightbox{
	float:right;
	/*background: rgba(0, 0, 0, 0.270);*/
	width:30%;
	height:300px;
	margin-left: 1%;
	margin-right: 2%;
	/*border-radius: 0.1em;*/
	/*padding: 0.1em;*/
}

.modt-box-content{
	background-color: #FFFFFF;
	padding: 0.75em;
}

.modt-imagedropshadow img{
	padding: 5px;
	border: solid 1px #EFEFEF;

}
.modt-imagedropshadow img:hover{
	border: solid 1px #CCC;
	-moz-box-shadow: 1px 1px 5px #999;
	-webkit-box-shadow: 1px 1px 5px #999;
	box-shadow: 1px 1px 5px #999;
}

.modt-table-container {
	max-width: 1000px;
	margin-left: auto;
	margin-right: auto;
	padding-left: 10px;
	padding-right: 10px;
}

.modt-table-h2 {
	font-size: 26px;
	margin: 20px 0;
	text-align: center;
}

/*.modt-table-responsive-table {*/
.modt-table li {
	border-radius: 3px;
	padding: 25px 30px;
	display: flex;
	justify-content: space-between;
	margin-bottom: 25px;
}
.modt-table-header {
	background-color: #c2d6d6;
	font-size: 14px;
	text-transform: uppercase;
	letter-spacing: 0.03em;
	text-align: center;
}
.modt-table-row {
	background-color: #ffffff;
	box-shadow: 0px 0px 9px 0px rgba(0,0,0,0.1);
	text-align: center;
}
.modt-table-col-1 {
	margin-top: 27px;
	flex-basis: 10%;
}
.modt-table-col-2 {
	margin-top: 5px;
	flex-basis: 25%;
}

.modt-table-col-2 img {
	height: 40px;
	width: 40px;
}

.modt-table-col-3 {
	margin-top: 27px;
	flex-basis: 40%;
}
.modt-table-col-4 {
	flex-basis: 30%;
}

.modt-table-col-4 p {
	margin-top: 27px;
	font-family: "Helvetica Neue",Helvetica,Arial,sans-serif;
	font-size: 14px;
	line-height: 1.428571429;
}

@media all and (max-width: 767px) {
	.modt-table-header {
		display: none;
	}
	.modt-table-row{

	}
	.modt-table-li {
		display: block;
	}
	.modt-table-col {

		flex-basis: 100%;

	}
	.modt-table-col {
		display: flex;
		padding: 10px 0;
	}
	.modt-table-col before
	{
		color: #6C7A89;
		padding-right: 10px;
		content: attr(data-label);
		flex-basis: 50%;
		text-align: right;
	}
}

.modt-footer{
	color: #111;
	text-transform: uppercase;
	margin-top: 15px;
	font-family: 'Enriqueta', arial, serif;
	font-size: 15px;
	font-weight: bold;
	letter-spacing: 2.5px;
	line-height: 1;
	text-align: center;
}

/* Doc Tab CSS End */

/*Mobile SSO Page*/
.mo_mobile_sso_click_here{
	background-color: orangered;
	height: 88px;
	width: 176px;
	font-size: xx-large;
	margin: 0 0 0 3%;
	font-weight: bold;
	color: white;
	border-radius: 10px;
	animation-name: clickhere;
	animation-duration: 1s;
	animation-iteration-count: infinite;
}
@keyframes clickhere {
	0% {background-color: orangered;}
	50% {background-color: #1B79AE;}
}

/* meeting scheduler*/
.call-setup-div{
	background: #F2F5FB;
	border-radius: 5px;
	margin-top: 10px;
	margin-right: 20px;
	padding-top: 10px;
	border-style: solid;
	border-color: #2f6062;
	padding-left: 10px;
}
.call-setup-heading {
	margin-top: 0px;
	margin-left: 5px;
}
.call-setup-label{
	padding-left:5px;
	font-size: 15px;
}
.call-setup-details{
	margin-left: 5px;
	margin-top: 5px;
}
.call-setup-datetime{
	width: 50%;
	float: left;
	position: relative;
	height: auto;
	min-height: 100% !important;
}
.call-setup-textbox{
	width: 90% !important;
}
.call-setup-notice{
	margin: 15px 25px 10px 10px;
}

/* The switch - the box around the slider */
.switch {
	position: relative;
	display: inline-block;
	width: 40px;
	height: 24px;
}

/* Hide default HTML checkbox */
.switch input {
	opacity: 0;
	width: 0;
	height: 0;
}

/* The slider */
.slider {
	position: absolute;
	cursor: pointer;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background-color: #ccc;
	-webkit-transition: .4s;
	transition: .4s;
}

input:disabled + .slider{
	background-color: #ebebeb;
	cursor: default;
}

.slider:before {
	position: absolute;
	content: "";
	height: 20px;
	width: 20px;
	left: 2px;
	bottom: 2px;
	background-color: white;
	-webkit-transition: .4s;
	transition: .4s;
}

input:checked + .slider {
	background-color: #0085ba;
}

input:focus + .slider {
	box-shadow: 0 0 1px #0085ba;
}

input:checked + .slider:before {
	-webkit-transform: translateX(15px);
	-ms-transform: translateX(15px);
	transform: translateX(15px);
}


/* Rounded sliders */
.slider.round {
	border-radius: 24px;
}

.slider.round:before {
	border-radius: 50%;
}
/* added for tlwp by Roshan*/

.mo_succ_notice {
	background: #e7fcd0;
	border: 1px solid #f6f3f4;
	border-left-color: rgb(195, 182, 182);

	border-left-width: 6px;
	box-shadow: 0 1px 1px rgba(0,0,0,.04);
	margin: 5px 15px 2px;
	padding: 1px 12px;
	border-left-color: #427e05;

}


#mo-support-menu {
	position: absolute;
	left: 168%;
	top: 128%;
	margin: -75px 0 0 -75px;
	list-style: none;
	font-size: 200%;
}

.mo-support-menu-button {
	opacity: 0;
	z-index: -1;
}

.mo-support-menu-button {
	width: 70px;
	height: 70px;
	position: absolute;
	left: 190%;
	top: 140%;
	margin: 82px 0 0 140px;
	border-radius: 50%;
	box-shadow: 0 5px 10px black;
	background-size: 100%;
	overflow: hidden;
	text-decoration: none;
	background-color:#0002ab;
}

#mo-support-menu:not(:target)>a:first-of-type,
#mo-support-menu:target>a:last-of-type {
	opacity: 1;
	z-index: 1;
}

#mo-support-menu:not(:target)>.icon-plus:before,
#mo-support-menu:target>.icon-minus:before {
	opacity: 1;
}

.mo-support-menu-item {
	width: 50px;
	height: 50px;
	position: absolute;
	line-height: 5px;
	left: 170%;
	top: 128%;
	margin: 88px 0 0 150px;
	border-radius: 50%;
	background-color:#0002ab;
	transform: translate(0px, 0px);
	transition: transform 500ms;
	z-index: -2;
	transition: .5s;
	box-shadow: 0 5px 10px black;
}

.mo-support-menu-item:hover{
	opacity: 0.9;
	box-shadow: 0 10px 20px black;
}


.mo-support-menu-item a {
	color: #fff;
	position: relative;
	top: 30%;
	left: 0;
	text-decoration: none;
}

#mo-support-menu:target>.mo-support-menu-item:nth-child(6) {
	transform: rotate(-100deg) translateY(-115px) rotate(100deg);
	transition-delay: 0s;
}

#mo-support-menu:target>.mo-support-menu-item:nth-child(5) {
	transform: rotate(20deg) translateY(-120px) rotate(-20deg);
	transition-delay: 0.1s;
}

#mo-support-menu:target>.mo-support-menu-item:nth-child(3) {
	transform: rotate(-20deg) translateY(-120px) rotate(20deg);
	transition-delay: 0.2s;
}

#mo-support-menu:target>.mo-support-menu-item:nth-child(4) {
	transform: rotate(-60deg) translateY(-115px) rotate(60deg);
	transition-delay: 0.3s;
}

.mo-sl-help-button-text {
	cursor: pointer;
	font-size: 16px;
	background-color: #002ab6;
	box-shadow: 1px 1px 10px 3px #8e8e9c;
	bottom: 6%;
	right: 11%;
	position: fixed;
	font-weight: 700;
	color: #fff;
	border-top-left-radius: 15px;
	border-top-right-radius: 15px;
	border-bottom-right-radius: 15px;
	border-bottom-left-radius: 15px;
	border: 3px solid #002ab6;
	padding: 9px;
	transition: all 3s ease-in-out;
}
@media (min-width:1750px){.mo-sl-help-button-text{right:6%!important}}
@media (min-width:1600px) and (max-width:1750px){.mo-sl-help-button-text{right:9%!important}}
@media (min-width:1400px) and (max-width:1600px){.mo-sl-help-button-text{right:10%!important}}
@media (min-width:1300px) and (max-width:1400px){.mo-sl-help-button-text{right:11%!important}}
@media (min-width:1100px) and (max-width:1300px){.mo-sl-help-button-text{right:12%!important}}
@media (min-width:900px) and (max-width:1100px){.mo-sl-help-button-text{right:13%!important}}
@media (min-width:800px) and (max-width:900px){.mo-sl-help-button-text{right:14%!important}}
@media (min-width:700px) and (max-width:800px){.mo-sl-help-button-text{right:15%!important}}
.mo-sl-help-button-text:before {
	content: "";
	width: 0;
	height: 0;
	position: absolute;
	border-top: 15px solid transparent;
	border-left: 30px solid #002ab6;
	border-bottom: 15px solid transparent;
	right: -32px;
	top: 16px;
}

.tttooltip {
	position: relative;
	display: inline-block;
}

.tttooltip .tttooltiptext {
	display: none;
	width: 120px;
	background-color: #002ab6;
	color: #fff;
	text-align: center;
	border-radius: 6px;
	padding: 20px 5px;
	margin-left: -170px;
	margin-top: -55px;
	font-size: 18px;
	/* Position the tooltip */
	box-shadow: 1px 1px 10px 3px #8e8e9c;
	position: absolute;
	z-index: 1;
}

.tttooltip:hover .tttooltiptext {
	display: block;
	margin-left: -160px;
	margin-top: -55px;
}

a.mo_sl_effect-shine:hover {
	-webkit-mask-image: linear-gradient(-75deg, rgba(0,0,0,.6) 30%, #000 50%, rgba(0,0,0,.6) 70%);
	-webkit-mask-size: 200%;
	animation: shine 2s infinite;
}

@-webkit-keyframes shine {
	from {
		-webkit-mask-position: 150%;
	}

	to {
		-webkit-mask-position: -50%;
	}
}

a.mo_sl_effect-shining {
	-webkit-mask-image: linear-gradient(-75deg, rgba(0,0,0,.6) 30%, #000 50%, rgba(255, 255, 255, 0.6) 70%);
	-webkit-mask-size: 200%;
	animation: shining 2s infinite;
}

@-webkit-keyframes shining {
	from {
		-webkit-mask-position: 150%;
	}

	to {
		-webkit-mask-position: -50%;
	}
}


.mo_sl_expand{
	position:relative;
	text-decoration:none;
	display:inline-block;
}

.mo_sl_expand:after {
	display:block;
	content: '';
	border-bottom: solid 3px #ffffff;
	transform: scaleX(0);
	transition: transform 250ms ease-in-out;
	transform-origin:100% 50%;
}

.mo_sl_expand:hover:after {
	transform: scaleX(1);
	transform-origin:0 50%;
}

/* Animation property */

/* Keyframes */
@keyframes mo_wiggle {
	0%, 7% {
		transform: rotateZ(0);
	}
	15% {
		transform: rotateZ(-15deg);
	}
	20% {
		transform: rotateZ(10deg);
	}
	25% {
		transform: rotateZ(-10deg);
	}
	30% {
		transform: rotateZ(6deg);
	}
	35% {
		transform: rotateZ(-4deg);
	}
	40%, 100% {
		transform: rotateZ(0);
	}
}


.mo_button_black {
	position: absolute;
	background-color: white;
	color: black;
	border: 2px solid #e7e7e7;
	border-radius: 5px;
	padding: 5px;
	padding-left: 19px;
	padding-right: 19px;
	border-top: 6px solid #db4437;
	animation: mo_wiggle 2s linear infinite;
	transform-origin: 50% 5em;
	margin-top: -14px;
}

.mo_button_black:hover {
	background-color: #e7e7e7;
	color: white;
}
