.mo_btn-block {
	display: block;
	width: 90%;
}

.mo_btn-block-inline {
	display: inline-block;
	width: 90%;
	margin-left: 10px;
}

.mo_btn {
	display: inline-block;
	/*padding: 6px 12px;*/
	margin-bottom: 0;
	font-size: 14px;
	font-weight: 400;
	line-height: 1.42857143;
	text-align: center;
	white-space: nowrap;
	vertical-align: middle;
	-ms-touch-action: manipulation;
	touch-action: manipulation;
	cursor: pointer;
	-webkit-user-select: none;
	-moz-user-select: none;
	-ms-user-select: none;
	user-select: none;
	background-image: none;
	border: 1px solid transparent;
	border-radius: 4px;
	text-decoration: none
}

.mo_btn_smart {
	display: inline-block;
	/*padding: 6px 12px;*/
	margin-bottom: 0;
	font-size: 14px;
	font-weight: 400;
	line-height: 1.42857143;
	text-align: center;
	white-space: nowrap;
	vertical-align: middle;
	-ms-touch-action: manipulation;
	touch-action: manipulation;
	cursor: pointer;
	-webkit-user-select: none;
	-moz-user-select: none;
	-ms-user-select: none;
	user-select: none;
	background-image: none;
	border-radius: 4px;
	text-decoration: none;
}
.mo_btn_transform:hover{
	transform: scale(1.1);
	transition: 0.3s;
}
.mo_btn_transform_i:hover{
	transform: scale(1.2);
	transition: 0.5s;
}
.mo_btn:focus,.mo_btn:active:focus,.mo_btn.active:focus,.mo_btn.focus,.mo_btn:active.focus,.mo_btn.active.focus
	{
	outline: thin dotted;
	outline: 5px auto -webkit-focus-ring-color;
	outline-offset: -2px
}

.mo_btn:hover,.mo_btn:focus,.mo_btn.focus {

	text-decoration: none
}

.mo_btn:active,.mo_btn.active {
	background-image: none;
	outline: 0;
	-webkit-box-shadow: inset 0 3px 5px rgba(0, 0, 0, .125);
	box-shadow: inset 0 3px 5px rgba(0, 0, 0, .125)
}

.mo_btn.disabled,.mo_btn[disabled],fieldset[disabled] .mo_btn {
	pointer-events: none;
	cursor: not-allowed;
	filter: alpha(opacity = 65);
	-webkit-box-shadow: none;
	box-shadow: none;
	opacity: .65
}

.mo_btn-default {
	color: #333;
	background-color: #fff;
	border-color: #ccc
}

.mo_btn-default:hover,.mo_btn-default:focus,.mo_btn-default.focus,.mo_btn-default:active,.mo_btn-default.active,.open>.dropdown-toggle.mo_btn-default
	{
	color: #333;
	background-color: #e6e6e6;
	border-color: #adadad
}

.mo_btn-default:active,.mo_btn-default.active,.open>.dropdown-toggle.mo_btn-default
	{
	background-image: none
}

.mo_btn-default.disabled,.mo_btn-default[disabled],fieldset[disabled] .mo_btn-default,.mo_btn-default.disabled:hover,.mo_btn-default[disabled]:hover,fieldset[disabled] .mo_btn-default:hover,.mo_btn-default.disabled:focus,.mo_btn-default[disabled]:focus,fieldset[disabled] .mo_btn-default:focus,.mo_btn-default.disabled.focus,.mo_btn-default[disabled].focus,fieldset[disabled] .mo_btn-default.focus,.mo_btn-default.disabled:active,.mo_btn-default[disabled]:active,fieldset[disabled] .mo_btn-default:active,.mo_btn-default.disabled.active,.mo_btn-default[disabled].active,fieldset[disabled] .mo_btn-default.active
	{
	background-color: #fff;
	border-color: #ccc
}

.mo_btn-default .badge {
	color: #fff;
	background-color: #333
}

.mo_btn-primary {
	color: #fff;
	background-color: #337ab7;
	border-color: #2e6da4
}

.mo_btn-primary:hover,.mo_btn-primary:focus,.mo_btn-primary.focus,.mo_btn-primary:active,.mo_btn-primary.active,.open>.dropdown-toggle.mo_btn-primary
	{
	color: #fff;
	background-color: #286090;
	border-color: #204d74
}

.mo_btn-primary:active,.mo_btn-primary.active,.open>.dropdown-toggle.mo_btn-primary
	{
	background-image: none
}

.mo_btn-primary.disabled,.mo_btn-primary[disabled],fieldset[disabled] .mo_btn-primary,.mo_btn-primary.disabled:hover,.mo_btn-primary[disabled]:hover,fieldset[disabled] .mo_btn-primary:hover,.mo_btn-primary.disabled:focus,.mo_btn-primary[disabled]:focus,fieldset[disabled] .mo_btn-primary:focus,.mo_btn-primary.disabled.focus,.mo_btn-primary[disabled].focus,fieldset[disabled] .mo_btn-primary.focus,.mo_btn-primary.disabled:active,.mo_btn-primary[disabled]:active,fieldset[disabled] .mo_btn-primary:active,.mo_btn-primary.disabled.active,.mo_btn-primary[disabled].active,fieldset[disabled] .mo_btn-primary.active
	{
	background-color: #337ab7;
	border-color: #2e6da4
}

.mo_btn-primary .badge {
	color: #337ab7;
	background-color: #fff
}

.mo_btn-success {
	color: #fff;
	background-color: #5cb85c;
	border-color: #4cae4c
}

.mo_btn-success:hover,.mo_btn-success:focus,.mo_btn-success.focus,.mo_btn-success:active,.mo_btn-success.active,.open>.dropdown-toggle.mo_btn-success
	{
	color: #fff;
	background-color: #449d44;
	border-color: #398439
}

.mo_btn-success:active,.mo_btn-success.active,.open>.dropdown-toggle.mo_btn-success
	{
	background-image: none
}

.mo_btn-success.disabled,.mo_btn-success[disabled],fieldset[disabled] .mo_btn-success,.mo_btn-success.disabled:hover,.mo_btn-success[disabled]:hover,fieldset[disabled] .mo_btn-success:hover,.mo_btn-success.disabled:focus,.mo_btn-success[disabled]:focus,fieldset[disabled] .mo_btn-success:focus,.mo_btn-success.disabled.focus,.mo_btn-success[disabled].focus,fieldset[disabled] .mo_btn-success.focus,.mo_btn-success.disabled:active,.mo_btn-success[disabled]:active,fieldset[disabled] .mo_btn-success:active,.mo_btn-success.disabled.active,.mo_btn-success[disabled].active,fieldset[disabled] .mo_btn-success.active
	{
	background-color: #5cb85c;
	border-color: #4cae4c
}

.mo_btn-success .badge {
	color: #5cb85c;
	background-color: #fff
}

.mo_btn-info {
	color: #fff;
	background-color: #5bc0de;
	border-color: #46b8da
}

.mo_btn-info:hover,.mo_btn-info:focus,.mo_btn-info.focus,.mo_btn-info:active,.mo_btn-info.active,.open>.dropdown-toggle.mo_btn-info
	{
	color: #fff;
	background-color: #31b0d5;
	border-color: #269abc
}

.mo_btn-info:active,.mo_btn-info.active,.open>.dropdown-toggle.mo_btn-info {
	background-image: none
}

.mo_btn-info.disabled,.mo_btn-info[disabled],fieldset[disabled] .mo_btn-info,.mo_btn-info.disabled:hover,.mo_btn-info[disabled]:hover,fieldset[disabled] .mo_btn-info:hover,.mo_btn-info.disabled:focus,.mo_btn-info[disabled]:focus,fieldset[disabled] .mo_btn-info:focus,.mo_btn-info.disabled.focus,.mo_btn-info[disabled].focus,fieldset[disabled] .mo_btn-info.focus,.mo_btn-info.disabled:active,.mo_btn-info[disabled]:active,fieldset[disabled] .mo_btn-info:active,.mo_btn-info.disabled.active,.mo_btn-info[disabled].active,fieldset[disabled] .mo_btn-info.active
	{
	background-color: #5bc0de;
	border-color: #46b8da
}

.mo_btn-info .badge {
	color: #5bc0de;
	background-color: #fff
}

.mo_btn-warning {
	color: #fff;
	background-color: #f0ad4e;
	border-color: #eea236
}

.mo_btn-warning:hover,.mo_btn-warning:focus,.mo_btn-warning.focus,.mo_btn-warning:active,.mo_btn-warning.active,.open>.dropdown-toggle.mo_btn-warning
	{
	color: #fff;
	background-color: #ec971f;
	border-color: #d58512
}

.mo_btn-warning:active,.mo_btn-warning.active,.open>.dropdown-toggle.mo_btn-warning
	{
	background-image: none
}

.mo_btn-warning.disabled,.mo_btn-warning[disabled],fieldset[disabled] .mo_btn-warning,.mo_btn-warning.disabled:hover,.mo_btn-warning[disabled]:hover,fieldset[disabled] .mo_btn-warning:hover,.mo_btn-warning.disabled:focus,.mo_btn-warning[disabled]:focus,fieldset[disabled] .mo_btn-warning:focus,.mo_btn-warning.disabled.focus,.mo_btn-warning[disabled].focus,fieldset[disabled] .mo_btn-warning.focus,.mo_btn-warning.disabled:active,.mo_btn-warning[disabled]:active,fieldset[disabled] .mo_btn-warning:active,.mo_btn-warning.disabled.active,.mo_btn-warning[disabled].active,fieldset[disabled] .mo_btn-warning.active
	{
	background-color: #f0ad4e;
	border-color: #eea236
}

.mo_btn-warning .badge {
	color: #f0ad4e;
	background-color: #fff
}

.mo_btn-danger {
	color: #fff;
	background-color: #d9534f;
	border-color: #d43f3a
}

.mo_btn-danger:hover,.mo_btn-danger:focus,.mo_btn-danger.focus,.mo_btn-danger:active,.mo_btn-danger.active,.open>.dropdown-toggle.mo_btn-danger
	{
	color: #fff;
	background-color: #c9302c;
	border-color: #ac2925
}

.mo_btn-danger:active,.mo_btn-danger.active,.open>.dropdown-toggle.mo_btn-danger
	{
	background-image: none
}

.mo_btn-danger.disabled,.mo_btn-danger[disabled],fieldset[disabled] .mo_btn-danger,.mo_btn-danger.disabled:hover,.mo_btn-danger[disabled]:hover,fieldset[disabled] .mo_btn-danger:hover,.mo_btn-danger.disabled:focus,.mo_btn-danger[disabled]:focus,fieldset[disabled] .mo_btn-danger:focus,.mo_btn-danger.disabled.focus,.mo_btn-danger[disabled].focus,fieldset[disabled] .mo_btn-danger.focus,.mo_btn-danger.disabled:active,.mo_btn-danger[disabled]:active,fieldset[disabled] .mo_btn-danger:active,.mo_btn-danger.disabled.active,.mo_btn-danger[disabled].active,fieldset[disabled] .mo_btn-danger.active
	{
	background-color: #d9534f;
	border-color: #d43f3a
}

.mo_btn-danger .badge {
	color: #d9534f;
	background-color: #fff
}

.mo_btn-link {
	font-weight: 400;
	color: #337ab7;
	border-radius: 0
}

.mo_btn-link,.mo_btn-link:active,.mo_btn-link.active,.mo_btn-link[disabled],fieldset[disabled] .mo_btn-link
	{
	background-color: transparent;
	-webkit-box-shadow: none;
	box-shadow: none
}

.mo_btn-link,.mo_btn-link:hover,.mo_btn-link:focus,.mo_btn-link:active {
	border-color: transparent
}

.mo_btn-link:hover,.mo_btn-link:focus {
	color: #23527c;
	text-decoration: underline;
	background-color: transparent
}

.mo_btn-link[disabled]:hover,fieldset[disabled] .mo_btn-link:hover,.mo_btn-link[disabled]:focus,fieldset[disabled] .mo_btn-link:focus
	{
	color: #777;
	text-decoration: none
}

.mo_btn-lg,.mo_btn-group-lg>.mo_btn {
	padding: 10px 16px;
	font-size: 18px;
	line-height: 1.33;
	border-radius: 6px
}

.mo_btn-sm,.mo_btn-group-sm>.mo_btn {
	padding: 5px 10px;
	font-size: 12px;
	line-height: 1.5;
	border-radius: 3px
}

.mo_btn-xs,.mo_btn-group-xs>.mo_btn {
	padding: 1px 5px;
	font-size: 12px;
	line-height: 1.5;
	border-radius: 3px
}

.mo_btn-block {
	display: block;
	width: 100%
}

.mo_btn-block+.mo_btn-block {
	margin-top: 5px
}

input[type=submit].mo_btn-block,input[type=reset].mo_btn-block,input[type=button].mo_btn-block
	{
	width: 100%
}



.mo_btn-group,.mo_btn-group-vertical {
	position: relative;
	display: inline-block;
	vertical-align: middle
}

.mo_btn-group>.mo_btn,.mo_btn-group-vertical>.mo_btn {
	position: relative;
	float: left
}

.mo_btn-group>.mo_btn:hover,.mo_btn-group-vertical>.mo_btn:hover,.mo_btn-group>.mo_btn:focus,.mo_btn-group-vertical>.mo_btn:focus,.mo_btn-group>.mo_btn:active,.mo_btn-group-vertical>.mo_btn:active,.mo_btn-group>.mo_btn.active,.mo_btn-group-vertical>.mo_btn.active
	{
	z-index: 2
}

.mo_btn-group .mo_btn+.mo_btn,.mo_btn-group .mo_btn+.mo_btn-group,.mo_btn-group .mo_btn-group+.mo_btn,.mo_btn-group .mo_btn-group+.mo_btn-group
	{
	margin-left: -1px
}

.mo_btn-toolbar {
	margin-left: -5px
}

.mo_btn-toolbar .mo_btn-group,.mo_btn-toolbar .input-group {
	float: left
}

.mo_btn-toolbar>.mo_btn,.mo_btn-toolbar>.mo_btn-group,.mo_btn-toolbar>.input-group {
	margin-left: 5px
}

.mo_btn-group>.mo_btn:not (:first-child ):not (:last-child ):not (.dropdown-toggle
	){
	border-radius: 0
}

.mo_btn-group>.mo_btn:first-child {
	margin-left: 0
}

.mo_btn-group>.mo_btn:first-child:not (:last-child ):not (.dropdown-toggle ){
	border-top-right-radius: 0;
	border-bottom-right-radius: 0
}

.mo_btn-group>.mo_btn:last-child:not (:first-child ),.mo_btn-group>.dropdown-toggle:not
	(:first-child ){
	border-top-left-radius: 0;
	border-bottom-left-radius: 0
}

.mo_btn-group>.mo_btn-group {
	float: left
}

.mo_btn-group>.mo_btn-group:not (:first-child ):not (:last-child )>.mo_btn {
	border-radius: 0
}

.mo_btn-group>.mo_btn-group:first-child>.mo_btn:last-child,.mo_btn-group>.mo_btn-group:first-child>.dropdown-toggle
	{
	border-top-right-radius: 0;
	border-bottom-right-radius: 0
}

.mo_btn-group>.mo_btn-group:last-child>.mo_btn:first-child {
	border-top-left-radius: 0;
	border-bottom-left-radius: 0
}

.mo_btn-group .dropdown-toggle:active,.mo_btn-group.open .dropdown-toggle {
	outline: 0
}

.mo_btn-group>.mo_btn+.dropdown-toggle {
	padding-right: 8px;
	padding-left: 8px
}

.mo_btn-group>.mo_btn-lg+.dropdown-toggle {
	padding-right: 12px;
	padding-left: 12px
}

.mo_btn-group.open .dropdown-toggle {
	-webkit-box-shadow: inset 0 3px 5px rgba(0, 0, 0, .125);
	box-shadow: inset 0 3px 5px rgba(0, 0, 0, .125)
}

.mo_btn-group.open .dropdown-toggle.mo_btn-link {
	-webkit-box-shadow: none;
	box-shadow: none
}

.mo_btn .caret {
	margin-left: 0
}

.mo_btn-lg .caret {
	border-width: 5px 5px 0;
	border-bottom-width: 0
}

.dropup .mo_btn-lg .caret {
	border-width: 0 5px 5px
}

.mo_btn-group-vertical>.mo_btn,.mo_btn-group-vertical>.mo_btn-group,.mo_btn-group-vertical>.mo_btn-group>.mo_btn
	{
	display: block;
	float: none;
	width: 100%;
	max-width: 100%
}

.mo_btn-group-vertical>.mo_btn-group>.mo_btn {
	float: none
}

.mo_btn-group-vertical>.mo_btn+.mo_btn,.mo_btn-group-vertical>.mo_btn+.mo_btn-group,.mo_btn-group-vertical>.mo_btn-group+.mo_btn,.mo_btn-group-vertical>.mo_btn-group+.mo_btn-group
	{
	margin-top: -1px;
	margin-left: 0
}

.mo_btn-group-vertical>.mo_btn:not (:first-child ):not (:last-child ){
	border-radius: 0
}

.mo_btn-group-vertical>.mo_btn:first-child:not (:last-child ){
	border-top-right-radius: 4px;
	border-bottom-right-radius: 0;
	border-bottom-left-radius: 0
}

.mo_btn-group-vertical>.mo_btn:last-child:not (:first-child ){
	border-top-left-radius: 0;
	border-top-right-radius: 0;
	border-bottom-left-radius: 4px
}

.mo_btn-group-vertical>.mo_btn-group:not (:first-child ):not (:last-child )>.mo_btn
	{
	border-radius: 0
}

.mo_btn-group-vertical>.mo_btn-group:first-child:not (:last-child )>.mo_btn:last-child,.mo_btn-group-vertical>.mo_btn-group:first-child:not
	(:last-child )>.dropdown-toggle {
	border-bottom-right-radius: 0;
	border-bottom-left-radius: 0
}

.mo_btn-group-vertical>.mo_btn-group:last-child:not (:first-child )>.mo_btn:first-child
	{
	border-top-left-radius: 0;
	border-top-right-radius: 0
}
