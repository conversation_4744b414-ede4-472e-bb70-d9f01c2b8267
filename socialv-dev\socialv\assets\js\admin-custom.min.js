"use strict";function notify_wordpress(e){e={action:"socialv_dismiss_notice",data:e},jQuery.post(ajaxurl,e)}jQuery,jQuery(document).ready(function(){jQuery(".socialv-notice-dismiss").click(function(e){e.preventDefault(),jQuery(this).parent().parent(".socialv-notice").fadeOut(600,function(){jQuery(this).parent().parent(".socialv-notice").remove()}),notify_wordpress(jQuery(this).data("msg"))})}),jQuery(document).ready(function(){jQuery(".iqonic_media_form").on("submit",function(e){e.preventDefault(),confirm(socialv_global_script.admin_notice)&&(e={action:"socialv_import_settings",formType:(e=jQuery(this)).find('[name="media_form_type"]').val(),dataValue:e.find('[name="media_form_type"]').attr("data-value"),chatValue:e.find('[name="media_form_type"]').attr("data-chat-value")},jQuery.ajax({type:"POST",url:ajaxurl,data:e,dataType:"json",success:function(e){"media-success"===e.status&&jQuery(document).find(".socialv-notice .socialv-media").fadeIn("slow").after('<div class="iqonic-result-msg">'+e.message+"</div>"),"message-success"===e.status&&jQuery(document).find(".socialv-notice .socialv-message").fadeIn("slow").after('<div class="iqonic-result-msg">'+e.message+"</div>"),setTimeout("location.reload(true);",1500)}}))})});