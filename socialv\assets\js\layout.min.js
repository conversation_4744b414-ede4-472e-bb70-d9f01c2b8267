"use strict";var setting_options=null!=(setting_options=document.querySelector('meta[name="setting_options"]'))?JSON.parse(setting_options.getAttribute("content")):JSON.parse("{}"),setting=window.IQSetting=new IQSetting(setting_options);function rtlLanguages(){var t=document.getElementsByTagName("html")[0].getAttribute("lang");return["ar","he","fa","ku","ur"].includes(t)}var bgClass,modeSetting,mode=document.querySelector(".switch-mode-icon")?document.querySelector(".switch-mode-icon"):null,LangMode=(null!=mode&&(""!=(bgClass=window.IQUtils.getCookie("data-mode"))?(document.getElementsByTagName("html")[0].setAttribute("data-mode",bgClass),document.querySelector(".socialv-switch-mode").setAttribute("data-mode",bgClass)):window.IQUtils.setCookie("data-mode",bgClass),null!==(modeSetting=document.querySelector(".socialv-switch-mode")))&&modeSetting.addEventListener("click",function(t){t.preventDefault();t="dark"==(t=modeSetting.getAttribute("data-mode"))?"light":"dark";modeSetting.setAttribute("data-mode",t),document.getElementsByTagName("html")[0].setAttribute("data-mode",t),window.IQUtils.setCookie("data-mode",t),"function"==typeof window.IQSetting.__updateThemeColor__&&window.IQSetting.__updateThemeColor__("theme_color",null)}),document.getElementsByTagName("html")[0].getAttribute("dir")),LangElements=Array.from(document.getElementsByClassName("elementor-section-stretched"));function LangLoad(){if(0==is_rtlExits&&"rtl"==LangMode){for(var t=0;t<LangElements.length;t++)LangElements[t].style.right=LangElements[t].style.left,LangElements[t].style.left="auto";wpeditorblock()}}document.addEventListener("DOMContentLoaded",function(){var t;setTimeout(LangLoad,500),"function"==typeof window.IQSetting.theme_scheme_direction&&1==is_rtlExits&&(t=1==is_rtlExits?"rtl":"ltr",window.IQSetting.theme_scheme_direction(t,null))});var resetSettings=document.querySelector('[data-reset="settings"]'),copySettings=(null!==resetSettings&&resetSettings.addEventListener("click",function(t){t.preventDefault(),window.confirm(socialv_global_script.reset_setting)&&window.IQSetting.reInit()}),document.querySelector('[data-copy="settings"]'));function wpeditorblock(){jQuery("iframe").contents().find("#tinymce").attr("dir","rtl"),jQuery(document.body).on("woosq_loaded",function(t){jQuery(".thumbnails").attr("dir","ltr")})}null!==copySettings&&copySettings.addEventListener("click",function(t){t.preventDefault();var t=window.IQSetting.getSettingJson(),e=document.createElement("textarea");document.querySelector("body").appendChild(e),document.querySelector(".setting_options").setAttribute("value",t),copySettings.setAttribute("data-bs-original-title","Copied!"),document.getElementById("save_layout_setting").submit()});