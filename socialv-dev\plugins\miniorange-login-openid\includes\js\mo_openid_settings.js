jQuery(document).ready(function () {	
	//show mcrypt extension installation reason
    jQuery("#help_openid_mcrypt_title").click(function () {
        jQuery("#help_openid_mcrypt").toggle();
    });
	
	jQuery("#openid_login_shortcode_title").click(function () {
    	jQuery("#openid_login_shortcode").slideToggle(400);
    });
	jQuery("#openid_sharing_shortcode_title").click(function () {
    	jQuery("#openid_sharing_shortcode").slideToggle(400);
    });
	jQuery("#openid_comments_shortcode_title").click(function () {
    	jQuery("#openid_comments_shortcode").slideToggle(400);
    });	
	jQuery("#openid_shortcode_inphp_title").click(function () {
    	jQuery("#openid_shortcode_inphp").slideToggle(400);
    });
	jQuery("#openid_question1").click(function () {
    	jQuery("#openid_question1_desc").slideToggle(400);
    });
	jQuery("#openid_question2").click(function () {
    	jQuery("#openid_question2_desc").slideToggle(400);
    });
	jQuery("#openid_question3").click(function () {
    	jQuery("#openid_question3_desc").slideToggle(400);
    });
	jQuery("#openid_question4").click(function () {
    	jQuery("#openid_question4_desc").slideToggle(400);
    });
	jQuery("#openid_question5").click(function () {
    	jQuery("#openid_question5_desc").slideToggle(400);
    });
	jQuery("#openid_question6").click(function () {
    	jQuery("#openid_question6_desc").slideToggle(400);
    });
	jQuery("#openid_question7").click(function () {
    	jQuery("#openid_question7_desc").slideToggle(400);
    });
	jQuery("#openid_question8").click(function () {
    	jQuery("#openid_question8_desc").slideToggle(400);
    });
	jQuery("#openid_question9").click(function () {
    	jQuery("#openid_question9_desc").slideToggle(400);
    });
	jQuery("#openid_question10").click(function () {
    	jQuery("#openid_question10_desc").slideToggle(400);
    });
	jQuery("#openid_question11").click(function () {
    	jQuery("#openid_question11_desc").slideToggle(400);
    });
	jQuery("#openid_question12").click(function () {
    	jQuery("#openid_question12_desc").slideToggle(400);
    });
    jQuery("#openid_question13").click(function () {
        jQuery("#openid_question13_desc").slideToggle(400);
    });
    jQuery("#openid_question14").click(function () {
        jQuery("#openid_question14_desc").slideToggle(400);
    });
    jQuery("#openid_question15").click(function () {
        jQuery("#openid_question15_desc").slideToggle(400);
    });
    jQuery("#openid_question16").click(function () {
        jQuery("#openid_question16_desc").slideToggle(400);
    });
    jQuery("#openid_question17").click(function () {
        jQuery("#openid_question17_desc").slideToggle(400);
    });
    jQuery("#openid_question18").click(function () {
        jQuery("#openid_question18_desc").slideToggle(400);
    });
    jQuery("#openid_question19").click(function () {
        jQuery("#openid_question19_desc").slideToggle(400);
    });
	jQuery("#openid_question_curl").click(function () {
    	jQuery("#openid_question_curl_desc").slideToggle(400);
    });
	jQuery("#openid_question_email").click(function () {
    	jQuery("#openid_question_email_desc").slideToggle(400);
    });	
	jQuery("#openid_question_otp").click(function () {
    	jQuery("#openid_question_otp_desc").slideToggle(400);
    });
	jQuery("#openid_question_login").click(function () {
    	jQuery("#openid_question_login_desc").slideToggle(400);
    });
	jQuery("#openid_question_sharing").click(function () {
    	jQuery("#openid_question_sharing_desc").slideToggle(400);
    });
	jQuery("#openid_question_payment").click(function () {
    	jQuery("#openid_question_payment_desc").slideToggle(400);
    });
    jQuery("#openid_question_plugin").click(function () {
        jQuery("#openid_question_plugin_desc").slideToggle(400);
    });
});