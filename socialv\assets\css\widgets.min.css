.widget{margin-bottom:2em;display:inline-block;width:100%;float:left;position:relative;border-radius:var(--border-radius-box);background:var(--color-theme-white-box);padding:2em;-webkit-box-shadow:var(--global-box-shadow);box-shadow:var(--global-box-shadow)}.widget:last-child{margin-bottom:0}.widget.widget_rss.text-left span{-webkit-box-align:center;-ms-flex-align:center;align-items:center;display:-webkit-box;display:-ms-flexbox;display:flex}.widget.widget_rss.text-left span .rsswidget{line-height:0;display:inline-block;margin-right:.313em;color:var(--global-font-title)}.widget.widget_rss.text-left span:before{top:-.2em}.widget ul{padding:0;margin:0}.widget ul li{list-style:none;margin-bottom:.875em}.widget ul li a{color:var(--global-font-color);-ms-word-wrap:break-word;word-wrap:break-word}.widget ul li a,ul.wp-block-archives-list li a{position:relative}.widget.widget_archive ul li a:hover,.widget_categories a:hover,.widget_categories ul li a:hover,.widget_pages ul li a:hover{color:var(--color-theme-primary)}.socialv-blog-meta .widget_categories ul li a,.socialv-blog-meta ul li.widget_categories a{letter-spacing:var(--letter-spacing-two);text-transform:uppercase;color:var(--color-theme-primary);font-weight:var(--font-weight-bold)}.socialv-blog-meta .widget_categories ul li a:hover,.socialv-blog-meta ul li.widget_categories a:hover{color:var(--color-theme-primary-dark)}.socialv-blog-meta ul li.widget_categories{padding-right:.5em;margin-right:.3em}.socialv-blog-meta ul li.widget_categories:last-child{padding-right:0;margin-right:0}.socialv-blog-meta ul li.widget_categories a:before{content:"";position:absolute;top:50%;right:0;background:var(--color-theme-primary);height:.0625em;width:.313em;-webkit-transform:translateY(-50%);transform:translateY(-50%)}.socialv-blog-meta ul li.widget_categories:last-child a:before{display:none}.widget_pages li.page_item a{text-transform:capitalize;font-size:var(--font-size-body)}.widget ul li a:hover{color:var(--color-theme-primary)}.widget_tag_cloud ul li,.wp-block-tag-cloud,footer ul.wp-tag-cloud li{margin:0 .325em .625em 0;padding:0 .6em;display:inline-block;border:none}.widget ul li:last-child{margin-bottom:0;padding-bottom:0;border:none}.widget ul ul.children{padding-left:1.563em}.widget ul.menu li{border:none}.widget_categories ul ul.children,.widget_pages ul ul.children{padding-top:.938em}.widget.widget_nav_menu ul li a,.widget ul.menu li a{padding:.625em 0;display:inline-block;width:100%}.widget_nav_menu .menu .menu-item .toggledrop{display:none}.widget.widget_nav_menu ul li,.widget ul.menu li{margin-bottom:0;padding:0}footer .widget_iqonic_navigation_menu ul li a{display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-align:center;-ms-flex-align:center;align-items:center;grid-gap:.6em;gap:.6em;color:var(--global-font-color);font-size:var(--font-size-normal);text-transform:capitalize;font-weight:var(--font-weight-medium);font-family:var(--highlight-font-family);letter-spacing:unset;padding:0;-webkit-transition:.3s;transition:.3s}footer .widget_iqonic_navigation_menu ul li a:hover{color:var(--color-theme-primary)}footer .widget_iqonic_navigation_menu.menu-no-icon ul li a i,footer .widget_iqonic_navigation_menu.menu-no-icon ul li a svg{display:none}footer .widget_iqonic_navigation_menu.menu-no-icon ul li a:hover{padding-left:.5em}footer .widget_iqonic_navigation_menu ul li a .icon svg{height:1.428em;width:1.428em;max-width:1.428em}:is(.widget,.widget_block) :is(.widget-title,.footer-title,h1,h2,h3,h4,h5){position:relative;margin-bottom:1em;padding-bottom:1em;margin-top:0;color:var(--global-font-title);font-size:var(--font-size-h5);line-height:var(--font-line-height-h5);letter-spacing:var(--font-letter-spacing-h5);font-weight:var(--font-weight-h5);border-bottom:.0625em solid var(--border-color-light)}.widget .logo-title{border:none;padding-bottom:0;font-size:var(--font-size-h4);line-height:var(--font-line-height-h4);letter-spacing:var(--font-letter-spacing-h4);font-weight:var(--font-weight-h4)}.widget .footer-title{font-size:.875em;text-transform:uppercase;letter-spacing:var(--letter-spacing-two);border:none;padding-bottom:0}.widget.widget_rss .widget-title span{display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-align:center;-ms-flex-align:center;align-items:center}.widget.widget_rss .widget-title span:before{top:0}.widget.widget_rss .widget-title span a.rsswidget{display:inline-block;line-height:0;margin-right:.313em;color:var(--global-font-title)}ol.wp-block-latest-comments{padding-left:0}.wp-block-latest-comments__comment{display:block}.wp-block-latest-comments__comment-meta{color:var(--color-theme-primary)}.wp-block-latest-comments__comment-meta a.wp-block-latest-comments__comment-author{color:var(--global-font-title)}.wp-block-latest-comments__comment-meta a{color:var(--global-font-color);line-height:var(--global-font-line-height)}.has-dates .wp-block-latest-comments__comment:last-child,.has-excerpts .wp-block-latest-comments__comment:last-child{margin-bottom:0}.wp-block-latest-comments__comment-excerpt p{margin:0}.socialv-image-content-wrap{display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-align:center;-ms-flex-align:center;align-items:center;grid-gap:1em;gap:1em;margin-bottom:1.5em}.socialv-image-content-wrap:last-child{margin-bottom:0}.socialv-image-content-wrap .post-img-blog,.wp-block-latest-posts__featured-image{height:4.375em;width:4.375em;min-width:4.375em}.socialv-recentpost.widget .post-img .post-img-blog img,.wp-block-latest-posts__featured-image img{border-radius:var(--border-radius-box);-o-object-fit:cover;object-fit:cover;height:100%;width:100%}.socialv-post-title:hover .socialv-heading-title{color:var(--color-theme-primary)}.socialv-image-content-wrap .post-blog-deatil .blog-category a,.socialv-recentpost.widget .blog-date a,.wp-block-latest-posts__post-date{font-size:var(--font-size-small);color:var(--global-font-color);font-weight:var(--font-weight-semi-bold);letter-spacing:var(--letter-spacing-one)}.post-blog-deatil .socialv-btn-container,.post-blog-deatil .socialv-widget-content{margin-top:.5em}.socialv-widget-image-content-wrap{display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-align:center;-ms-flex-align:center;align-items:center;grid-gap:1em;gap:1em;margin-bottom:1em}.socialv-widget-image-content-wrap:last-child{margin-bottom:0}.widget ul li .avtar-details a{color:var(--global-font-title)}.socialv-widget-image-content-wrap .avtar-details .socialv-e-last-activity{font-size:var(--font-size-small)}.socialv-widget-image-content-wrap .avtar-details{line-height:normal}.socialv-widget-image-content-wrap .item-avatar{position:relative}.socialv-widget-image-content-wrap .avtar-details .title{display:inline-block;vertical-align:middle}.socialv-user-status.offline,.socialv-user-status.online{height:.375em;width:.375em;min-width:.375em;background:var(--color-theme-offline);border-radius:50%;display:inline-block;vertical-align:middle;margin-left:.5em;outline:.125em solid var(--color-theme-white-box)}.socialv-user-status.online{background:var(--color-theme-online)}.socialv-user-status span{font-size:0}.socialv-suggested-friends-widget .item-detail-data .socialv-nik-name,.socialv-widget-image-content-wrap .avtar-details .socialv-nik-name{color:var(--global-font-color);font-size:var(--font-size-small);font-weight:var(--font-weight-regular)}.widget_iqonic_latest_activity_feed .socialv-activity-item{margin-bottom:1em;border-bottom:.0625em solid var(--border-color-light);padding-bottom:1em;display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-align:center;-ms-flex-align:center;align-items:center;grid-gap:1em;gap:1em}.widget_iqonic_latest_activity_feed .socialv-activity-item a>img{margin:0 .3em}.widget_iqonic_latest_activity_feed .socialv-activity-items-list .socialv-activity-item:last-child{margin-bottom:0;padding-bottom:0;border:none}.widget_iqonic_latest_activity_feed .socialv-activity-item p{font-size:var(--font-size-normal);line-height:1.7em;font-family:var(--highlight-font-family);margin:0;position:relative}.widget_iqonic_latest_activity_feed .socialv-activity-item a{color:var(--global-font-title);font-weight:var(--font-weight-medium)}.widget_iqonic_latest_activity_feed .socialv-activity-item .activity-time-since{display:block;color:var(--global-font-color);font-size:var(--font-size-small);font-weight:var(--font-weight-regular);line-height:normal}.widget_iqonic_latest_activity_feed [data-bp-tooltip]:hover:after{content:none}.socialv-items-list-widget .socialv-friend-request{margin-top:1em}.socialv-items-list-widget .socialv-friend-request .item-details{margin:.625em 0}.socialv-items-list-widget .socialv-friend-request:first-child{margin-top:0}.widget_iqonic_group_suggestions .socialv-group-type>h5{display:none}@media (max-width:1400px) and (min-width:1200px){.socialv-suggested-friends-widget .socialv-friend-request>.d-flex{display:block!important}.request-button{margin-top:.5em}.request-button .btn:first-child,.request-button .socialv-button:first-child{margin-left:0}}.banner-text{margin:-2em}.image-box-content{position:absolute;top:3em;left:0;right:0;text-align:center;padding:0 2em}.image-box-content p{color:var(--color-theme-white);margin-top:2em;padding:0 2em}.wp-block-image.logo-image img{width:9.368em}@media (min-width:1200px){.banner-text>.wp-block-group__inner-container>.wp-block-image>img{width:100%;border-radius:var(--border-radius-box)}}@media (max-width:1199px){.banner-text{background:var(--global-body-bgcolor);text-align:center}.banner-text .wp-block-group__inner-container{display:inline-block;position:relative}}@media (max-width:767px){.widget{margin-bottom:1em;padding:1em}.banner-text{margin:-1em}}@media(max-width:29.938em){.widget .widget-title .title-border{margin:0 0 0 .625em}.widget ul ul.children{padding-left:.938em}}
/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */