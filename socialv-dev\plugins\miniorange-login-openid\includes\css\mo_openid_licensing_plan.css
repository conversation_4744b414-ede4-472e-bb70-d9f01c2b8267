/* Newly Added for Licensing Plan*/

.mo_openid-check-tooltip {
	position: relative;
	display: inline-block;
	padding: 0px 8px 5px 8px;
	margin: -10px 0;
	cursor: pointer;
}
.mo_openid-quote .mo_openid-check-tooltip:hover .mo_openid-info, .mo_openid-quote .mo_openid-check-tooltip:focus .mo_openid-info {
	visibility: visible;
	opacity: 1;
	-webkit-transform: translate3d(0, 0, 0);
	transform: translate3d(0, 0, 0);
}
.mo_openid-quote .mo_openid-check-tooltip .mo_openid-info {
	box-sizing: border-box;
	position: absolute;
	top: -15px;
	left: 105%;
	display: block;
	background: #ffffff;
	width: 300px;
	font-size: 16px;
	line-height: 24px;
	cursor: text;
	visibility: hidden;
	opacity: 0;
	transition: all .5s ease-out;
}

.mo_openid-quote .mo_openid-check-tooltip .mo_openid-info .mo_openid-pronounce {
	display: block;
	background: #e97d68;
	color: #ffffff;
	padding: 8px 17px 10px 17px;
	line-height: 16px;
	border-radius: 8px 8px 0px 0px;
}

.mo_openid-quote .mo_openid-check-tooltip .mo_openid-info .mo_openid-text {
	display: block;
	padding: 13px 17px;
}

.mo-openid-tab-content {
	margin-left: 0%!important;
	margin-top: 0%!important;

}
.mo-openid-tab-content>.active {
	width: 100% !important;
}

.mo-openid-cd-pricing-container {
	width: 100%;
	max-width: 1460px;
	margin: 4em auto;
}
@media only screen and (min-width: 768px) {
	.mo-openid-cd-pricing-container {
		margin: auto;
	}
	.mo-openid-cd-pricing-container.cd-full-width {
		width: 100%;
		max-width: none;
	}
}

.mo-open-id-cd-pricing-switcher {
	text-align: center;
}
.mo-open-id-cd-pricing-switcher .mo-open-id-fieldset {
	display: inline-block;
	position: relative;
	border-radius: 50em;
	border: 1px solid #e97d68;
}
.mo-open-id-cd-pricing-switcher input[type="radio"] {
	position: absolute;
	opacity: 0;
}
.mo-open-id-cd-pricing-switcher label {
	position: relative;
	z-index: 1;
	display: inline-block;
	float: left;
	width: 160px;
	height: 40px;
	line-height: 40px;
	cursor: pointer;
	font-size: 1.4rem;
	color: #FFFFFF;
	font-size:18px;
}

.mo-open-id-cd-pricing-switcher .mo-open-id-cd-switch {
	/* floating background */
	position: absolute;
	top: 2px;
	left: 2px;
	height: 36px;
	width: 162px;
	background-color: black;
	border-radius: 50em;
	-webkit-transition: -webkit-transform 0.5s;
	-moz-transition: -moz-transform 0.5s;
	transition: transform 0.5s;
}

.mo-open-id-cd-pricing-switcher .mo-open-id-cd-switch-2 {
	/* floating background */
	position: absolute;
	top: 2px;
	left: 2px;
	height: 36px;
	width: 162px;
	background-color: black;
	border-radius: 50em;
	-webkit-transition: -webkit-transform 0.5s;
	-moz-transition: -moz-transform 0.5s;
	transition: transform 0.5s;
}

html {
	font-size: 62.5%;
}

html * {
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
}

.mo-open-id-cd-pricing-switcher input[type="radio"]:checked + label + .mo-open-id-cd-switch,
.mo-open-id-cd-pricing-switcher input[type="radio"]:checked + label:nth-of-type(n) + .mo-open-id-cd-switch {
	/* use label:nth-of-type(n) to fix a bug on safari with multiple adjacent-sibling selectors*/
	-webkit-transform: translateX(155px);
	-moz-transform: translateX(155px);
	-ms-transform: translateX(155px);
	-o-transform: translateX(155px);
	transform: translateX(155px);
}

.mo-open-id-cd-pricing-switcher input[type="radio"]:checked + label + .mo-open-id-cd-switch-2 {
	/* use label:nth-of-type(n) to fix a bug on safari with multiple adjacent-sibling selectors*/
	-webkit-transform: translateX(310px);
	-moz-transform: translateX(310px);
	-ms-transform: translateX(310px);
	-o-transform: translateX(310px);
	transform: translateX(310px);
}

.mo-openid-cd-pricing-list {
	margin: 2em 0 0;
	/*transform-style: preserve-3d;*/
	transition: transform 1s;
}
.mo-openid-cd-pricing-list > li {
	position: relative;
	margin-bottom: 1em;
}
@media only screen and (min-width: 768px) {
	.mo-openid-cd-pricing-list {
		margin: 3em 0 0;
	}
	.mo-openid-cd-pricing-list:after {
		content: "";
		display: table;
		clear: both;
	}
	.mo-openid-cd-pricing-list > li {
		width: 35.3333333333%;
		float: left;
	}
	.mo-openid-cd-has-margins .mo-openid-cd-pricing-list > li {
		width:23.6%;
		float: left;
		margin-right: 1.5%;
	}
	.mo-openid-cd-has-margins .mo-openid-cd-pricing-list > li:last-of-type {
		margin-right: 0;
	}
}

.mo-openid-cd-pricing-wrapper {
	/* this is the item that rotates */
	overflow: show;
	position: relative;
	transform-style: preserve-3d;
	transition: transform 1s;
}

.mo-openid-cd-pricing-wrapper > li {
	background-color: #FFFFFF;
	-webkit-backface-visibility: hidden;
	backface-visibility: hidden;
	/* Firefox bug - 3D CSS transform, jagged edges */
	outline: 1px solid transparent;
}
.mo-openid-cd-pricing-wrapper > li::after {
	/* subtle gradient layer on the right - to indicate it's possible to scroll */
	content: '';
	position: absolute;
	top: 0;
	right: 0;
	height: 100%;
	width: 50px;
	pointer-events: none;
	background: -webkit-linear-gradient( right , #FFFFFF, rgba(255, 255, 255, 0));
	background: linear-gradient(to left, #FFFFFF, rgba(255, 255, 255, 0));
}
.mo-openid-cd-pricing-wrapper > li.is-ended::after {
	/* class added in jQuery - remove the gradient layer when it's no longer possible to scroll */
	display: none;
}
.mo-openid-cd-pricing-wrapper .is-visible {
	/* the front item, visible by default */
	position: relative;
	background-color: #f2f5f8;
}
.mo-openid-cd-pricing-wrapper .is-hidden {
	/* the hidden items, right behind the front one */
	position: absolute;
	top: 0;
	left: 0;
	height: 100%;
	width: 100%;
	z-index: 1;
	-webkit-transform: rotateY(180deg);
	-moz-transform: rotateY(180deg);
	-ms-transform: rotateY(180deg);
	-o-transform: rotateY(180deg);
	transform: rotateY(180deg);
}
.mo-openid-cd-pricing-wrapper .is-selected {
	/* the next item that will be visible */
	z-index: 3 !important;
}
@media only screen and (min-width: 768px) {
	.mo-openid-cd-pricing-wrapper > li::before {
		/* separator between pricing tables - visible when number of tables > 3 */
		content: '';
		position: absolute;
		z-index: 6;
		left: -1px;
		top: 50%;
		bottom: auto;
		-webkit-transform: translateY(-50%);
		-moz-transform: translateY(-50%);
		-ms-transform: translateY(-50%);
		-o-transform: translateY(-50%);
		transform: translateY(-50%);
		height: 50%;
		width: 1px;
		background-color: #b1d6e8;
	}
	.mo-openid-cd-pricing-wrapper > li::after {
		/* hide gradient layer */
		display: none;
	}
	.mo-openid-cd-popular .mo-openid-cd-pricing-wrapper > li {
		box-shadow: inset 0 0 0 3px #e97d68;
	}
	.mo-openid-cd-has-margins .mo-openid-cd-pricing-wrapper > li, .mo-openid-cd-has-margins .mo-openid-cd-popular .mo-openid-cd-pricing-wrapper > li {
		box-shadow: 0 1px 5px rgba(0, 0, 0, 0.1);
	}
	.cd-secondary-theme .mo-openid-cd-pricing-wrapper > li {
		background: #3aa0d1;
		background: -webkit-linear-gradient( bottom , #3aa0d1, #3ad2d1);
		background: linear-gradient(to top, #3aa0d1, #3ad2d1);
	}
	.cd-secondary-theme .mo-openid-cd-popular .mo-openid-cd-pricing-wrapper > li {
		background: #e97d68;
		background: -webkit-linear-gradient( bottom , #e97d68, #e99b68);
		background: linear-gradient(to top, #e97d68, #e99b68);
		box-shadow: none;
	}
	:nth-of-type(1) > .mo-openid-cd-pricing-wrapper > li::before {
		/* hide table separator for the first table */
		display: none;
	}
	.mo-openid-cd-has-margins .mo-openid-cd-pricing-wrapper > li {
		border-radius: 4px 4px 6px 6px;
	}
	.mo-openid-cd-has-margins .mo-openid-cd-pricing-wrapper > li::before {
		display: none;
	}
}
@media only screen and (min-width: 1500px) {
	.cd-full-width .mo-openid-cd-pricing-wrapper > li {
		padding: 2.5em 0;
	}
}

@media only screen and (min-width: 768px) {
	.mo-openid-cd-popular .mo-openid-cd-pricing-wrapper > li::before {
		/* hide table separator for .mo-openid-cd-popular table */
		display: none;
	}

	.mo-openid-cd-popular + li .mo-openid-cd-pricing-wrapper > li::before {
		/* hide table separator for tables following .mo-openid-cd-popular table */
		display: none;
	}
}

.mo-openid-cd-pricing-header {
	position: relative;

	height: 80px;
	padding: 1em;
	pointer-events: none;
	background-color: #3aa0d1;
	color: #FFFFFF;
}
.mo-openid-cd-pricing-header h2 {
	margin-bottom: 3px;
	font-weight: 700;
	text-transform: uppercase;
}
.mo-openid-cd-popular .mo-openid-cd-pricing-header {
	background-color: #e97d68;
}
@media only screen and (min-width: 768px) {
	.mo-openid-cd-pricing-header {
		height: 200px;
		padding: 1.9em 0.9em 1.6em;
		pointer-events: auto;
		text-align: center;
		color: #2f6062;
		background-color: transparent;
	}
	.mo-openid-cd-popular .mo-openid-cd-pricing-header {
		color: #e97d68;
		background-color: transparent;
	}
	.cd-secondary-theme .mo-openid-cd-pricing-header {
		color: #FFFFFF;
	}
	.mo-openid-cd-pricing-header h2 {
		font-size: 1.8rem;
		letter-spacing: 2px;
	}
}

.mo-openid-cd-currency, .mo-openid-cd-value {
	font-size: 4rem;
	font-weight: 300;
}

.mo-openid-cd-duration {
	font-weight: 800;
	font-size: 1.3rem;
	color: #8dc8e4;
	text-transform: uppercase;
}
.mo-openid-cd-popular .mo-openid-cd-duration {
	color: #f3b6ab;
}
.mo-openid-cd-duration::before {
	content: '/';
	margin-right: 2px;
}

@media only screen and (min-width: 768px) {
	.mo-openid-cd-value {
		font-size: 4rem;
		font-weight: 300;
	}

	.mo-openid-cd-currency, .mo-openid-cd-duration {
		color: rgba(23, 61, 80, 0.4);
	}
	.mo-openid-cd-popular .mo-openid-cd-currency, .mo-openid-cd-popular .mo-openid-cd-duration {
		color: #e97d68;
	}
	.cd-secondary-theme .mo-openid-cd-currency, .cd-secondary-theme .mo-openid-cd-duration {
		color: #2e80a7;
	}
	.cd-secondary-theme .mo-openid-cd-popular .mo-openid-cd-currency, .cd-secondary-theme .mo-openid-cd-popular .mo-openid-cd-duration {
		color: #ba6453;
	}

	.mo-openid-cd-currency {
		display: inline-block;
		margin-top: 10px;
		vertical-align: top;
		font-size: 2rem;
		font-weight: 700;
	}

	.mo-openid-cd-duration {
		font-size: 1.4rem;
	}
}
.mo-openid-cd-pricing-body {
	overflow-x: auto;
	-webkit-overflow-scrolling: touch;
}
.is-switched .mo-openid-cd-pricing-body {
	/* fix a bug on Chrome Android */
	overflow: hidden;
}
@media only screen and (min-width: 768px) {
	.mo-openid-cd-pricing-body {
		overflow-x: visible;
	}
}

.mo-openid-cd-pricing-features {
	width: 600px;
}
.mo-openid-cd-pricing-features:after {
	content: "";
	display: table;
	clear: both;
}
.mo-openid-cd-pricing-features li {
	width: 100px;
	float: left;
	padding: 1.6em 1em;
	font-size: 1.4rem;
	text-align: center;
	white-space: initial;

	line-height:1.4em;

	text-overflow: ellipsis;
	color: black;
	overflow-wrap: break-word;
	margin: 0 !important;

}
.mo-openid-cd-pricing-features em {
	display: block;
	margin-bottom: 5px;
	font-weight: 600;
	color: black;
}
@media only screen and (min-width: 768px) {
	.mo-openid-cd-pricing-features {
		width: auto;
		word-wrap: break-word;
	}
	.mo-openid-cd-pricing-features li {
		float: none;
		width: auto;
		padding: 0.7em;
		word-wrap: break-word;
		font-size:1.3em;
	}
	.mo-openid-cd-popular .mo-openid-cd-pricing-features li {
		margin: 0 3px;
	}
	.mo-openid-cd-pricing-features li:nth-of-type(2n+1) {
		background-color: rgba(23, 61, 80, 0.06);
	}
	.mo-openid-cd-pricing-features em {
		display: inline-block;
		margin-bottom: 0;
		word-wrap: break-word;
	}
	.mo-openid-cd-has-margins .mo-openid-cd-popular .mo-openid-cd-pricing-features li, .cd-secondary-theme .mo-openid-cd-popular .mo-openid-cd-pricing-features li {
		margin: 0;
	}
	.cd-secondary-theme .mo-openid-cd-pricing-features li {
		color: #FFFFFF;
	}
	.cd-secondary-theme .mo-openid-cd-pricing-features li:nth-of-type(2n+1) {
		background-color: transparent;
	}
}

.mo-openid-cd-pricing-footer {
	position: absolute;
	z-index: 1;
	top: 0;
	left: 0;
	/* on mobile it covers the .cd-pricing-header */
	height: 80px;
	width: 100%;
}
.mo-openid-cd-pricing-footer::after {
	/* right arrow visible on mobile */
	content: '';
	position: absolute;
	right: 1em;
	top: 50%;
	bottom: auto;
	-webkit-transform: translateY(-50%);
	-moz-transform: translateY(-50%);
	-ms-transform: translateY(-50%);
	-o-transform: translateY(-50%);
	transform: translateY(-50%);
	height: 20px;
	width: 20px;
	background: url(../img/cd-icon-small-arrow.svg);
}
@media only screen and (min-width: 768px) {
	.mo-openid-cd-pricing-footer {
		position: relative;
		height: auto;
		padding: 1.8em 0;
		text-align: center;
	}
	.mo-openid-cd-pricing-footer::after {
		/* hide arrow */
		display: none;
	}
	.mo-openid-cd-has-margins .mo-openid-cd-pricing-footer {
		padding-bottom: 0;
	}
}

.mo-openid-cd-select {
	position: relative;
	z-index: 1;
	display: block;
	height: 100%;
	/* hide button text on mobile */
	overflow: hidden;
	text-indent: 100%;
	white-space: nowrap;
	color: transparent;
}
@media only screen and (min-width: 768px) {
	.mo-openid-cd-select {
		position: static;
		display: inline-block;
		height: auto;
		padding: 1.3em 3em;
		color: #FFFFFF;
		border-radius: 2px;
		background-color: #0c1f28;
		font-size: 1.4rem;
		text-indent: 0;
		text-transform: uppercase;
		letter-spacing: 2px;
		text-decoration: none;
	}

	.mo-openid-cd-popular .mo-openid-cd-select {
		background-color: #e97d68;
	}

	.cd-secondary-theme .mo-openid-cd-popular .mo-openid-cd-select {
		background-color: #0c1f28;
	}

	.mo-openid-cd-has-margins .mo-openid-cd-select {
		display: block;
		padding: 1.7em 0;
		border-radius: 0 0 4px 4px;
	}
}


#col1.flipped {
	transform: rotateY( 360deg);
}
#col2.flipped {
	transform: rotateY( 360deg);
}
#col3.flipped {
	transform: rotateY( 360deg);
}
#col4.flipped {
	transform: rotateY( 360deg);
}
#allfeature:hover{
	color: #0d6aad;
	cursor: pointer ;
}

.mo-openid-cd-pricing-wrapper {
	/* this is the item that rotates */
	overflow: show;
	position: relative;
	transform-style: preserve-3d;
	transition: transform 1s;
}

/*Newly Edited for Lic Plans*/
