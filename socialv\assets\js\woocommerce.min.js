"use strict";var sideToggler=document.querySelector(".shop-filter-sidebar")?document.querySelector(".shop-filter-sidebar"):null,sideCloseToggler=document.querySelector(".socialv-filter-close")?document.querySelector(".socialv-filter-close"):null,can_loaded_product_view=(null!==sideToggler&&sideToggler.addEventListener("click",function(){document.querySelector("body .socialv-woo-sidebar").classList.toggle("woo-sidebar-open"),document.querySelector("body").classList.toggle("overflow-hidden")}),null!==sideCloseToggler&&sideCloseToggler.addEventListener("click",function(){document.querySelector("body .socialv-woo-sidebar").classList.remove("woo-sidebar-open"),document.querySelector("body").classList.remove("overflow-hidden")}),document.addEventListener("click",function(e){null!=document.querySelector(".socialv-woo-sidebar.woo-sidebar-open")&&null==e.target.closest(".socialv-woo-sidebar.woo-sidebar-open")&&document.querySelector(".socialv-woo-sidebar").classList.contains("woo-sidebar-open")&&(document.querySelector("body .socialv-woo-sidebar").classList.remove("woo-sidebar-open"),document.querySelector("body").classList.remove("overflow-hidden"))},!0),!function(r){r(document).on("woof_ajax_done",woof_ajax_done_handler),r(document).ready(function(){r(document).on("click","button.plus, button.minus",function(){r('button[name="update_cart"]').removeAttr("disabled");var e=r(this).closest(".quantity").find(".qty"),o=(""==e.val()&&e.val(0),parseFloat(e.val())),t=parseFloat(e.attr("max")),a=parseFloat(e.attr("min")),s=parseFloat(e.attr("step"));r(this).is(".plus")?t&&t<=o?e.val(t):e.val(o+s):a&&o<=a?e.val(a):1<=o&&e.val(o-s)}),change_view_btn_event(),null!=socialv_loadmore_params.ajaxurl&&r.ajax({url:socialv_loadmore_params.ajaxurl,data:{action:"load_skeleton"},type:"GET",success:function(e){localStorage.setItem("product_grid_skeleton",e.data["skeleton-grid"]),localStorage.setItem("product_list_skeleton",e.data["skeleton-list"])}});var o=r.fn.css,t=new r.Event("stylechanged"),o=r.fn.css;r.fn.css=function(){var e=o.apply(this,arguments);return r(this).trigger(t),e},setTimeout(function(){r(".woof_info_popup").on("stylechanged",function(){r(this).append('<div class="socialv-show-loader-overlay"></div>')})},500)})}(jQuery),!0);function ajax_product(s,r){jQuery.ajax({url:window.location.href,data:{loaded_paged:socialv_loadmore_params.current_page},type:"POST",beforeSend:function(e){if("product_grid_skeleton"==r)for(var o=window.IQUtils.getCookie("product_view[col_no]"),t=jQuery(localStorage.getItem(r)).siblings(".column-"+o),a=0;a<o;a++)s.append(t.clone());else s.append(jQuery(localStorage.getItem(r)));jQuery(".loader-container").hide(0),can_loaded_product_view=!1},success:function(e){e&&(e=jQuery(e),jQuery(".products").replaceWith(e.find(".products")),s.find(".skeleton-main").remove(),loadmore_product(),can_loaded_product_view=!0)}})}var isSubdomain=function(){var e=0<arguments.length&&void 0!==arguments[0]?arguments[0]:window.location.hostname,o=new RegExp(/^([a-z]+\:\/{2})?([\w-]+\.[\w-]+\.\w+)$/);return!!e.match(o)};function change_view_btn_event(){jQuery(".socialv-view-grid,.socialv-listing").on("click",function(){var e=jQuery(this),o=e.parents(".sorting-wrapper").next(".products");e.hasClass("active")||jQuery("#woof_html_buffer").is(":visible")||!can_loaded_product_view||(window.IQUtils.setCookie("product_view[col_no]",e.hasClass("socialv-view-grid")?e.data("grid"):"1"),window.IQUtils.setCookie("product_view[is_grid]",e.hasClass("socialv-view-grid")?"2":"1"),jQuery(".socialv-product-view-buttons .btn").removeClass("active"),e.addClass("active"),e.hasClass("socialv-listing")?(o.find(".product").fadeOut(0,function(){jQuery(this).remove()}),e.parents(".product-grid-style").removeClass("product-grid-style").addClass("product-list-style"),ajax_product(o,"product_list_skeleton")):1!=e.parents(".product-grid-style").length&&(o.find(".product").fadeOut(0,function(){jQuery(this).remove()}),e.parents(".product-list-style").removeClass("product-list-style").addClass("product-grid-style"),ajax_product(o,"product_grid_skeleton")),setTimeout(function(){void 0!==e.data("grid")?o.removeClass("columns-2 columns-3 columns-4").addClass("columns-"+e.data("grid")):o.removeClass(" columns-2 columns-3 columns-4"),o.addClass("animated-product")},100),o.removeClass("animated-product"))})}function woof_ajax_done_handler(e){change_view_btn_event(),loadmore_product(),socialv_loadmore_params.current_page=1,jQuery.ajax({url:socialv_loadmore_params.ajaxurl,data:{action:"fetch_woof_filter_ajax_query"},type:"POST",success:function(e){(e=JSON.parse(e))&&(socialv_loadmore_params.posts=e.query,socialv_loadmore_params.max_page=e.max_page)}})}