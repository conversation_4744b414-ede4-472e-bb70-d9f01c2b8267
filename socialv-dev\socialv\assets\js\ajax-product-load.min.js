"use strict";function loadmore_product(){var a=!0;jQ<PERSON>y(window).unbind("scroll").scroll(function(){var o;jQuery(document).scrollTop()>jQuery(document).height()-2e3&&1==a&&(a=!1,o={action:"loadmore_product",query:socialv_loadmore_params.posts,page:socialv_loadmore_params.current_page,is_grid:jQuery(".socialv-product-view-buttons").find(".btn.active").hasClass("socialv-view-grid")},jQuery.ajax({url:socialv_loadmore_params.ajaxurl,data:o,type:"POST",beforeSend:function(o){jQuery(".loader-container .load-more").addClass("loading")},success:function(o){(!o||(jQuery(".socialv-product-main-list").find(".products").append(o),socialv_loadmore_params.current_page++,a=!0,jQuery(".socialv-product-main-list").attr("data-pagedno",socialv_loadmore_params.current_page),socialv_loadmore_params.current_page==socialv_loadmore_params.max_page))&&jQuery(".loader-container").html("")}}))})}function update_product_count(){var o=0<arguments.length&&void 0!==arguments[0]?arguments[0]:jQuery(".woocommerce-result-count"),a=1<arguments.length&&void 0!==arguments[1]?arguments[1]:jQuery(".woocommerce-result-count").data("product-per-page"),r=o.text().trim().split(" "),e=r[1].split("–");e[1]=Number(e[1])+Number(a),e[1]>r[3]&&(e[1]=r[3]),r[1]=e.join("–"),o.html(r.join(" "))}jQuery,loadmore_product(),update_product_count();