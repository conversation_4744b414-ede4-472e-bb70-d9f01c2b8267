#!/bin/bash

# WordPress 权限修复脚本
# 使用方法: ./scripts/fix-permissions.sh

echo "🔧 修复 WordPress 文件权限..."
echo

# 检查容器是否运行
if ! docker-compose ps | grep -q "wordpress.*Up"; then
    echo "❌ WordPress 容器未运行，请先启动: docker-compose up -d"
    exit 1
fi

echo "📁 修复目录权限..."

# 修复 wp-content 目录权限
docker-compose exec -T wordpress bash -c "
    echo '正在修复 wp-content 权限...'
    chown -R www-data:www-data /var/www/html/wp-content/
    
    echo '正在设置目录权限...'
    find /var/www/html/wp-content/ -type d -exec chmod 755 {} \;
    
    echo '正在设置文件权限...'
    find /var/www/html/wp-content/ -type f -exec chmod 644 {} \;
    
    echo '正在修复特殊目录权限...'
    chmod 755 /var/www/html/wp-content/themes/
    chmod 755 /var/www/html/wp-content/plugins/
    chmod 755 /var/www/html/wp-content/uploads/
    
    echo '正在修复上传目录权限...'
    chmod -R 755 /var/www/html/wp-content/uploads/
    
    echo '权限修复完成！'
"

echo
echo "✅ WordPress 权限修复完成！"
echo
echo "📋 权限设置说明:"
echo "   - 目录权限: 755 (rwxr-xr-x)"
echo "   - 文件权限: 644 (rw-r--r--)"
echo "   - 所有者: www-data:www-data"
echo
echo "💡 提示:"
echo "   - 如果仍有权限问题，请重新运行此脚本"
echo "   - 上传主题/插件时如遇问题，可运行此脚本修复"
echo
