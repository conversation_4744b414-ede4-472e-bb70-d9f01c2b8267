{"last_tab": "1", "grid_container": {"width": "84.433em", "units": "em"}, "body_back_option": "2", "body_color": "", "body_image": {"url": "", "id": "", "height": "", "width": "", "thumbnail": ""}, "is_page_spacing": "default", "page_spacing": {"units": "em", "top": "5em", "bottom": "5em"}, "tablet_page_spacing": {"units": "em", "top": "2em", "bottom": "2em"}, "mobile_page_spacing": {"units": "em", "top": "1em", "bottom": "1em"}, "back_to_top_btn": "yes", "socialv_enable_switcher": "yes", "socialv_layout_mode_options": "light", "socialv_frontside_switcher": "yes", "is_admin_switcher": "0", "customizer_non_selected_page": ["3838", "5658"], "is_language_direction": "1", "display_resticated_page": "yes", "default_page_link": "234", "nonrestricted_page": ["10469", "5658", "3331", "2015", "2013", "253", "248"], "nonrestricted_url": "", "display_after_login_redirect": "false", "display_after_login_page": "28", "verticle_header_text": "SocialV", "socialv_text_options": "light", "verticle_header_color": "", "verticle_dark_header_color": "", "socialv_logo_options": "light", "socialv_verticle_logo": {"url": "https:/wp-content/themes/socialv-themes/assets/images/logo-mini.svg", "id": "", "height": "", "width": "", "thumbnail": ""}, "socialv_verticle_dark_logo": {"url": "https:/wp-content/themes/socialv-themes/assets/images/logo-mini.svg", "id": "", "height": "", "width": "", "thumbnail": ""}, "logo_position": "yes", "display_full_logo": "0", "logo-dimensions": {"width": "", "height": "", "units": "px"}, "display_loader": "no", "loader_bg_color": "#ffffff", "loader_gif": {"url": "https:/wp-content/themes/socialv-theme/assets/images/redux/loader.gif", "id": "", "height": "", "width": "", "thumbnail": ""}, "loader-dimensions": {"width": "", "height": "", "units": "px"}, "custom_color_switch": "no", "success_color": "", "danger_color": "", "warning_color": "", "info_color": "", "orange_color": "", "custom_switch_mode": "1", "title_color": "", "text_color": "", "parent_bg_color": "", "child_bg_color": "", "light_comment_color": "", "dark_title_color": "", "dark_text_color": "", "dark_parent_bg_color": "", "dark_child_bg_color": "", "dark_comment_color": "", "change_font": "0", "body_font": {"font-family": "", "font-options": "", "google": "1", "font-backup": "", "font-weight": "", "font-style": "", "subsets": "", "font-size": ""}, "h1_font": {"font-family": "", "font-options": "", "google": "1", "font-weight": "", "font-style": "", "subsets": "", "font-size": ""}, "h2_font": {"font-family": "", "font-options": "", "google": "1", "font-weight": "", "font-style": "", "subsets": "", "font-size": ""}, "h3_font": {"font-family": "", "font-options": "", "google": "1", "font-weight": "", "font-style": "", "subsets": "", "font-size": ""}, "h4_font": {"font-family": "", "font-options": "", "google": "1", "font-weight": "", "font-style": "", "subsets": "", "font-size": ""}, "h5_font": {"font-family": "", "font-options": "", "google": "1", "font-weight": "", "font-style": "", "subsets": "", "font-size": ""}, "h6_font": {"font-family": "", "font-options": "", "google": "1", "font-weight": "", "font-style": "", "subsets": "", "font-size": ""}, "header_layout": "2", "is_header_spacing": "container-fluid", "socialv_header_background_type": "default", "socialv_header_background_color": "", "socialv_header_background_image": {"url": "", "id": "", "height": "", "width": "", "thumbnail": ""}, "header_menu_limit": "6", "header_language_switch": "yes", "header_display_frndreq": "yes", "header_display_messages": "yes", "header_display_notification": "yes", "header_notification_limit": "10", "display_header_cart_button": "yes", "header_display_login": "yes", "site_login_title": "<PERSON><PERSON> ", "is_socialv_site_login_icon_desktop": "", "socialv_site_login_logo": {"url": "https:/wp-content/themes/socialv-themes/assets/images/redux/login-icon.svg", "id": "", "height": "", "width": "", "thumbnail": ""}, "registration_process": "default", "resend_email_verify": "0", "resend_email_verify_desc": "Welcome to socialV, a platform to connect with the social world.", "resend_email_verify_link": "", "manually_proccess": "text", "manually_proccess_text": "Please wait until your account has been verified by the admin.", "manually_proccess_page": "", "site_login": "0", "site_login_link": "234", "site_login_shortcode": "[iqonic-login]", "site_login_desc": "Welcome to socialV, a platform to connect with the social world", "site_forgetpwd_link": "253", "site_forgetpwd_shortcode": "[iqonic-lost-pass]", "site_forgetpwd_desc": "Welcome to socialV, a platform to connect with the social world", "site_resend_verification_email_desc": "Welcome to socialV, a platform to connect with the social world", "site_register_link": "248", "site_register_desc": "Welcome to socialV, a platform to connect with the social world", "socialv_sticky_header_background_type": "default", "socialv_sticky_header_background_color": "", "socialv_sticky_header_background_image": {"url": "", "id": "", "height": "", "width": "", "thumbnail": ""}, "header_display_search": "yes", "header_search_text": "Search Here", "header_search_limit": "5", "socialv_search_content_list": {"group": "1", "member": "1", "activity": "1", "post": "1", "product": "1", "course": "1", "page": "1", "forum": "1", "topic": "1", "reply": "1"}, "change_footer_background": "default", "footer_bg_color": "", "footer_bg_image": {"url": "https:/wp-content/themes/socialv-theme/assets/images/redux/footer-img.jpg", "id": "", "height": "", "width": "", "thumbnail": ""}, "footer_top": "yes", "socialv_footer_column_layout": "5", "footer_one": "1", "footer_two": "1", "footer_three": "1", "footer_four": "1", "display_copyright": "yes", "footer_copyright_align": "center", "footer_copyright": "© {{year}} SocialV. All Rights Reserved.", "search_page": "1", "display_search_pagination": "yes", "searchpage_pagination_limit": "5", "header_display_side_area": "yes", "sidearea_background_type": "default", "sidearea_background_color": "", "sidearea_background_image": {"url": "", "id": "", "height": "", "width": "", "thumbnail": ""}, "sidearea_width": {"width": "", "units": "px"}, "display_banner": "yes", "breadcrumb_style": "1", "page_default_breadcrumb_image": {"url": "", "id": "", "height": "", "width": "", "thumbnail": ""}, "display_breadcrumb_title": "yes", "breadcrumb_title_tag": "h2", "breadcrumb_title_color": "", "display_breadcrumb_nav": "yes", "breadcrumb_back_type": "2", "breadcrumb_back_color": "", "breadcrumb_back_image": {"url": "https:/wp-content/themes/socialv-themes/assets/images/redux/banner.jpg", "id": "", "height": "", "width": "", "thumbnail": ""}, "blog_setting": "4", "blog_default_banner_image": {"url": "https:/wp-content/themes/socialv-themes/assets/images/redux/banner.jpg", "id": "", "height": "", "width": "", "thumbnail": ""}, "display_pagination": "yes", "blog_single_page_setting": "4", "display_comment": "yes", "posts_select": ["gallery", "link", "quote", "video", "audio"], "404_banner_image": {"url": "https:/wp-content/themes/socialv-themes/assets/images/redux/404.png", "id": "", "height": "", "width": "", "thumbnail": ""}, "404_title": "Page Not Found.", "404_description": "The requested page does not exist.", "404_backtohome_title": "Back to Home", "header_on_404": "1", "footer_on_404": "1", "bp_page": "4", "default_post_per_page": "20", "display_default_login_access": "no", "socialv_default_avatar": {"url": "", "id": "", "height": "", "width": "", "thumbnail": ""}, "socialv_default_cover_image": {"url": "", "id": "", "height": "", "width": "", "thumbnail": ""}, "bp_display_banner": "yes", "bp_page_default_banner_image": {"url": "", "id": "", "height": "", "width": "", "thumbnail": ""}, "bp_display_banner_title": "yes", "bp_banner_title_tag": "h1", "bp_banner_title_color": "", "bp_banner_subtitle_text": "Good Communication is the key to cop-up with good ideas", "display_activity_showing_story": "off", "display_activity_showing_friends": "no", "display_comments_order": "ASC", "display_activities_posts_style": "grid", "is_post_blur_style": "no", "display_blog_post_type": "1", "is_socialv_enable_hide_post": "1", "socialv_stop_action": {"register": "", "avatar": "", "group_activity": ""}, "is_socialv_enable_share_post": "1", "is_socialv_enable_share_post_on_activity": "1", "socialv_share_post_options": {"facebook": "1", "twitter": "1", "linkedin": "1", "pinterest": "", "whatsppp": "", "yahoo": "", "skype": "", "telegram": ""}, "socialv_copy_url": "0", "display_user_post": "yes", "display_user_comments": "yes", "display_user_views": "yes", "display_user_request_btn": "yes", "display_user_message_btn": "yes", "is_socialv_group_enable_list_grid": "0", "show_rss_group_field": "1", "socialv_enable_profile_forum_tab": "1", "lp_page": "4", "socialv_enable_profile_courses_tab": "1", "woocommerce_shop": "2", "woocommerce_shop_grid": "4", "woocommerce_shop_list": "5", "woocommerce_product_per_page": "10", "socialv_show_related_product": "yes", "desk_number": "4", "lap_number": "3", "tab_number": "2", "mob_number": "2", "related_autoplay": "false", "related_loop": "true", "related_dots": "true", "related_nav": "true", "socialv_display_product_name": "yes", "socialv_display_price": "yes", "socialv_display_product_rating": "yes", "socialv_display_product_addtocart_icon": "yes", "socialv_display_product_wishlist_icon": "yes", "socialv_display_product_quickview_icon": "yes", "is_pmp_cancel_logo": "yes", "pmp_page_default_cancel_logo": {"url": "", "id": "", "height": "", "width": "", "thumbnail": ""}, "social_media_options": {"facebook": "https://www.facebook.com/", "twitter": "", "instagram": "https://www.instagram.com/", "linkedin": "", "pinterest": "", "dribbble": "https://www.dribbble.com", "flickr": "", "skype": "", "youtube": "", "rss": "", "behance": ""}, "css_code": "", "js_code": "", "related_speed": 2000, "redux-backup": 1}