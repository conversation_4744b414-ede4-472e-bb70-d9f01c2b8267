@echo off
echo ========================================
echo    SocialV WordPress Development
echo ========================================
echo.

echo Checking Docker...
docker ps >nul 2>&1
if %errorlevel% equ 0 (
    echo Docker is running. Starting Docker environment...
    cd socialv-dev
    docker-compose up -d
    echo.
    echo Docker environment started!
    echo WordPress: http://localhost:8080
    echo Admin: http://localhost:8080/wp-admin
    echo phpMyAdmin: http://localhost:8081
    echo Username: admin / Password: admin123
    echo.
    pause
) else (
    echo Docker not running. Checking PHP...
    php --version >nul 2>&1
    if %errorlevel% equ 0 (
        echo PHP found. Starting local server...
        if not exist "local-server\wordpress" (
            echo Please download WordPress first by running:
            echo cd local-server
            echo download-wordpress.bat
            pause
            goto end
        )
        
        echo Copying theme...
        if exist "socialv" (
            xcopy "socialv\*" "local-server\wordpress\wp-content\themes\socialv\" /E /I /Y >nul 2>&1
        )
        
        echo Starting server on http://localhost:8000
        cd local-server\wordpress
        start http://localhost:8000
        php -S localhost:8000
    ) else (
        echo Neither Docker nor PHP found.
        echo Please install one of the following:
        echo 1. Docker Desktop
        echo 2. PHP or XAMPP
        pause
    )
)

:end
pause
