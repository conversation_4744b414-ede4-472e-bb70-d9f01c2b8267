<?xml version="1.0" encoding="UTF-8" ?>
<!-- This is a WordPress eXtended RSS file generated by WordPress as an export of your site. -->
<!-- It contains information about your site's posts, pages, comments, categories, and other content. -->
<!-- You may use this file to transfer that content from one site to another. -->
<!-- This file is not intended to serve as a complete backup of your site. -->

<!-- To import this information into a WordPress site follow these steps: -->
<!-- 1. Log in to that site as an administrator. -->
<!-- 2. Go to Tools: Import in the WordPress admin panel. -->
<!-- 3. Install the "WordPress" importer from the list. -->
<!-- 4. Activate & Run Importer. -->
<!-- 5. Upload this file using the form provided on that page. -->
<!-- 6. You will first be asked to map the authors in this export file to users -->
<!--    on the site. For each author, you may choose to map to an -->
<!--    existing user on the site or to create a new user. -->
<!-- 7. WordPress will then import each of the posts, pages, comments, categories, etc. -->
<!--    contained in this file into your site. -->

	<!-- generator="WordPress/6.8.1" created="2025-05-01 06:40" -->
<rss version="2.0"
	xmlns:excerpt="http://wordpress.org/export/1.2/excerpt/"
	xmlns:content="http://purl.org/rss/1.0/modules/content/"
	xmlns:wfw="http://wellformedweb.org/CommentAPI/"
	xmlns:dc="http://purl.org/dc/elements/1.1/"
	xmlns:wp="http://wordpress.org/export/1.2/"
>

<channel>
	<title>SocialV - Social Network and Community BuddyPress Theme</title>
	<link>https://socialv-wordpress.iqonic.design</link>
	<description>Just another WordPress site</description>
	<pubDate>Thu, 01 May 2025 06:40:04 +0000</pubDate>
	<language>en-US</language>
	<wp:wxr_version>1.2</wp:wxr_version>
	<wp:base_site_url>https://socialv-wordpress.iqonic.design</wp:base_site_url>
	<wp:base_blog_url>https://socialv-wordpress.iqonic.design</wp:base_blog_url>

		<wp:author><wp:author_id>1</wp:author_id><wp:author_login><![CDATA[jenny]]></wp:author_login><wp:author_email><![CDATA[<EMAIL>]]></wp:author_email><wp:author_display_name><![CDATA[Jenny Wilson]]></wp:author_display_name><wp:author_first_name><![CDATA[Jenny]]></wp:author_first_name><wp:author_last_name><![CDATA[Wilson]]></wp:author_last_name></wp:author>

				
	<generator>https://wordpress.org/?v=6.8.1</generator>

		<item>
		<title><![CDATA[SocialV - BuddyPress Theme to Create Your Own Social Networking & Community Site]]></title>
		<link>https://socialv-wordpress.iqonic.design/lessons/content-better-media-queries-in-sass/</link>
		<pubDate>Tue, 16 Jun 2015 19:08:52 +0000</pubDate>
		<dc:creator><![CDATA[jenny]]></dc:creator>
		<guid isPermaLink="false">http://demo.thimpress.com/elearningwp/lessons/content-better-media-queries-in-sass/</guid>
		<description></description>
		<content:encoded><![CDATA[
<iframe width="560" height="315" src="https://www.youtube.com/embed/ArD9LEH8zRY" title="YouTube video player" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" allowfullscreen></iframe>]]></content:encoded>
		<excerpt:encoded><![CDATA[			]]></excerpt:encoded>
		<wp:post_id>6855</wp:post_id>
		<wp:post_date><![CDATA[2015-06-16 19:08:52]]></wp:post_date>
		<wp:post_date_gmt><![CDATA[2015-06-16 19:08:52]]></wp:post_date_gmt>
		<wp:post_modified><![CDATA[2022-09-19 09:32:29]]></wp:post_modified>
		<wp:post_modified_gmt><![CDATA[2022-09-19 09:32:29]]></wp:post_modified_gmt>
		<wp:comment_status><![CDATA[closed]]></wp:comment_status>
		<wp:ping_status><![CDATA[closed]]></wp:ping_status>
		<wp:post_name><![CDATA[content-better-media-queries-in-sass]]></wp:post_name>
		<wp:status><![CDATA[publish]]></wp:status>
		<wp:post_parent>0</wp:post_parent>
		<wp:menu_order>0</wp:menu_order>
		<wp:post_type><![CDATA[lp_lesson]]></wp:post_type>
		<wp:post_password><![CDATA[]]></wp:post_password>
		<wp:is_sticky>0</wp:is_sticky>
														<wp:postmeta>
		<wp:meta_key><![CDATA[_vc_post_settings]]></wp:meta_key>
		<wp:meta_value><![CDATA[a:1:{s:10:"vc_grid_id";a:0:{}}]]></wp:meta_value>
		</wp:postmeta>
							<wp:postmeta>
		<wp:meta_key><![CDATA[_lp_duration]]></wp:meta_key>
		<wp:meta_value><![CDATA[30 minute]]></wp:meta_value>
		</wp:postmeta>
							<wp:postmeta>
		<wp:meta_key><![CDATA[_lp_preview]]></wp:meta_key>
		<wp:meta_value><![CDATA[no]]></wp:meta_value>
		</wp:postmeta>
							<wp:postmeta>
		<wp:meta_key><![CDATA[_edit_last]]></wp:meta_key>
		<wp:meta_value><![CDATA[1]]></wp:meta_value>
		</wp:postmeta>
							<wp:postmeta>
		<wp:meta_key><![CDATA[_oembed_6e6e05f7c38d7e1e39c3f7cf0df64171]]></wp:meta_key>
		<wp:meta_value><![CDATA[<iframe title="SocialV - Live Style Customizer Demo" width="720" height="405" src="https://www.youtube.com/embed/1EF_Y6UOWsQ?feature=oembed" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" referrerpolicy="strict-origin-when-cross-origin" allowfullscreen></iframe>]]></wp:meta_value>
		</wp:postmeta>
							<wp:postmeta>
		<wp:meta_key><![CDATA[_oembed_time_6e6e05f7c38d7e1e39c3f7cf0df64171]]></wp:meta_key>
		<wp:meta_value><![CDATA[1719058949]]></wp:meta_value>
		</wp:postmeta>
							<wp:postmeta>
		<wp:meta_key><![CDATA[_oembed_95a4df1c2a3936c9e1739aea1d93246e]]></wp:meta_key>
		<wp:meta_value><![CDATA[<iframe title="SocialV - Live Style Customizer Demo" width="720" height="405" src="https://www.youtube.com/embed/1EF_Y6UOWsQ?feature=oembed" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" referrerpolicy="strict-origin-when-cross-origin" allowfullscreen></iframe>]]></wp:meta_value>
		</wp:postmeta>
							<wp:postmeta>
		<wp:meta_key><![CDATA[_oembed_time_95a4df1c2a3936c9e1739aea1d93246e]]></wp:meta_key>
		<wp:meta_value><![CDATA[1739636979]]></wp:meta_value>
		</wp:postmeta>
							</item>
					<item>
		<title><![CDATA[Introduction to the course]]></title>
		<link>https://socialv-wordpress.iqonic.design/lessons/introduction-to-the-course/</link>
		<pubDate>Tue, 16 Jun 2020 07:23:37 +0000</pubDate>
		<dc:creator><![CDATA[jenny]]></dc:creator>
		<guid isPermaLink="false">http://computer-fundamentals-copy-2</guid>
		<description></description>
		<content:encoded><![CDATA[We and our partners are using technologies like cookies and process personal data like the IP-address or browser information in order to personalize the advertising that you see. This helps us to show you more relevant ads and improves your internet experience. We also use it in order to measure results or align our website content. Because we value your privacy, we are herewith asking for your permission to use these technologies. You can always change/withdraw your consent later by clicking on the settings button on the left lower corner of the page.

&nbsp;
<blockquote>This helps us to show you more relevant ads and improves your internet experience. We also use it in order to measure results or align our website content. Because we value your privacy, we are herewith asking for your permission to use these technologies.</blockquote>
&nbsp;

IP-address or browser information in order to personalize the advertising that you see. This helps us to show you more relevant ads and improves your internet experience. We also use it in order to measure results or align our website content. Because we value your privacy, we are herewith asking for your permission to use these technologies. You can always change/withdraw your consent later by clicking on the settings button on the left lower corner of the page.

&nbsp;

We and our partners are using technologies like cookies and process personal data like the IP-address or browser information in order to personalize the advertising that you see. This helps us to show you more relevant ads and improves your internet experience. We also use it in order to measure results or align our website content. Because we value your privacy, we are herewith asking for your permission to use these technologies. You can always change/withdraw your consent later by clicking on the settings button on the left lower corner of the page.

&nbsp;	]]></content:encoded>
		<excerpt:encoded><![CDATA[	]]></excerpt:encoded>
		<wp:post_id>6859</wp:post_id>
		<wp:post_date><![CDATA[2020-06-16 07:23:37]]></wp:post_date>
		<wp:post_date_gmt><![CDATA[2020-06-16 07:23:37]]></wp:post_date_gmt>
		<wp:post_modified><![CDATA[2022-09-19 10:04:47]]></wp:post_modified>
		<wp:post_modified_gmt><![CDATA[2022-09-19 10:04:47]]></wp:post_modified_gmt>
		<wp:comment_status><![CDATA[open]]></wp:comment_status>
		<wp:ping_status><![CDATA[closed]]></wp:ping_status>
		<wp:post_name><![CDATA[introduction-to-the-course]]></wp:post_name>
		<wp:status><![CDATA[publish]]></wp:status>
		<wp:post_parent>0</wp:post_parent>
		<wp:menu_order>0</wp:menu_order>
		<wp:post_type><![CDATA[lp_lesson]]></wp:post_type>
		<wp:post_password><![CDATA[]]></wp:post_password>
		<wp:is_sticky>0</wp:is_sticky>
														<wp:postmeta>
		<wp:meta_key><![CDATA[count_items]]></wp:meta_key>
		<wp:meta_value><![CDATA[0]]></wp:meta_value>
		</wp:postmeta>
							<wp:postmeta>
		<wp:meta_key><![CDATA[slide_template]]></wp:meta_key>
		<wp:meta_value><![CDATA[]]></wp:meta_value>
		</wp:postmeta>
							<wp:postmeta>
		<wp:meta_key><![CDATA[rs_page_bg_color]]></wp:meta_key>
		<wp:meta_value><![CDATA[]]></wp:meta_value>
		</wp:postmeta>
							<wp:postmeta>
		<wp:meta_key><![CDATA[_lp_duration]]></wp:meta_key>
		<wp:meta_value><![CDATA[20 minute]]></wp:meta_value>
		</wp:postmeta>
							<wp:postmeta>
		<wp:meta_key><![CDATA[_lp_preview]]></wp:meta_key>
		<wp:meta_value><![CDATA[yes]]></wp:meta_value>
		</wp:postmeta>
							<wp:postmeta>
		<wp:meta_key><![CDATA[_edit_last]]></wp:meta_key>
		<wp:meta_value><![CDATA[1]]></wp:meta_value>
		</wp:postmeta>
							</item>
					<item>
		<title><![CDATA[Why do we have to balance the recipe]]></title>
		<link>https://socialv-wordpress.iqonic.design/lessons/why-do-we-have-to-balance-the-recipe/</link>
		<pubDate>Wed, 16 Sep 2020 05:05:54 +0000</pubDate>
		<dc:creator><![CDATA[jenny]]></dc:creator>
		<guid isPermaLink="false">http://business-language-development-copy-6</guid>
		<description></description>
		<content:encoded><![CDATA[We and our partners are using technologies like cookies and process personal data like the IP-address or browser information in order to personalize the advertising that you see. This helps us to show you more relevant ads and improves your internet experience. We also use it in order to measure results or align our website content. Because we value your privacy, we are herewith asking for your permission to use these technologies. You can always change/withdraw your consent later by clicking on the settings button on the left lower corner of the page.

&nbsp;
<blockquote>This helps us to show you more relevant ads and improves your internet experience. We also use it in order to measure results or align our website content. Because we value your privacy, we are herewith asking for your permission to use these technologies.</blockquote>
&nbsp;

IP-address or browser information in order to personalize the advertising that you see. This helps us to show you more relevant ads and improves your internet experience. We also use it in order to measure results or align our website content. Because we value your privacy, we are herewith asking for your permission to use these technologies. You can always change/withdraw your consent later by clicking on the settings button on the left lower corner of the page.

&nbsp;

We and our partners are using technologies like cookies and process personal data like the IP-address or browser information in order to personalize the advertising that you see. This helps us to show you more relevant ads and improves your internet experience. We also use it in order to measure results or align our website content. Because we value your privacy, we are herewith asking for your permission to use these technologies. You can always change/withdraw your consent later by clicking on the settings button on the left lower corner of the page.

&nbsp;	]]></content:encoded>
		<excerpt:encoded><![CDATA[	]]></excerpt:encoded>
		<wp:post_id>6860</wp:post_id>
		<wp:post_date><![CDATA[2020-09-16 05:05:54]]></wp:post_date>
		<wp:post_date_gmt><![CDATA[2020-09-16 05:05:54]]></wp:post_date_gmt>
		<wp:post_modified><![CDATA[2022-09-19 10:05:26]]></wp:post_modified>
		<wp:post_modified_gmt><![CDATA[2022-09-19 10:05:26]]></wp:post_modified_gmt>
		<wp:comment_status><![CDATA[open]]></wp:comment_status>
		<wp:ping_status><![CDATA[closed]]></wp:ping_status>
		<wp:post_name><![CDATA[why-do-we-have-to-balance-the-recipe]]></wp:post_name>
		<wp:status><![CDATA[publish]]></wp:status>
		<wp:post_parent>0</wp:post_parent>
		<wp:menu_order>0</wp:menu_order>
		<wp:post_type><![CDATA[lp_lesson]]></wp:post_type>
		<wp:post_password><![CDATA[]]></wp:post_password>
		<wp:is_sticky>0</wp:is_sticky>
														<wp:postmeta>
		<wp:meta_key><![CDATA[count_items]]></wp:meta_key>
		<wp:meta_value><![CDATA[0]]></wp:meta_value>
		</wp:postmeta>
							<wp:postmeta>
		<wp:meta_key><![CDATA[slide_template]]></wp:meta_key>
		<wp:meta_value><![CDATA[]]></wp:meta_value>
		</wp:postmeta>
							<wp:postmeta>
		<wp:meta_key><![CDATA[rs_page_bg_color]]></wp:meta_key>
		<wp:meta_value><![CDATA[]]></wp:meta_value>
		</wp:postmeta>
							<wp:postmeta>
		<wp:meta_key><![CDATA[_lp_duration]]></wp:meta_key>
		<wp:meta_value><![CDATA[20 minute]]></wp:meta_value>
		</wp:postmeta>
							<wp:postmeta>
		<wp:meta_key><![CDATA[_lp_preview]]></wp:meta_key>
		<wp:meta_value><![CDATA[yes]]></wp:meta_value>
		</wp:postmeta>
							<wp:postmeta>
		<wp:meta_key><![CDATA[_edit_last]]></wp:meta_key>
		<wp:meta_value><![CDATA[1]]></wp:meta_value>
		</wp:postmeta>
							</item>
					<item>
		<title><![CDATA[Essential Cooking Skills]]></title>
		<link>https://socialv-wordpress.iqonic.design/lessons/essential-cooking-skills/</link>
		<pubDate>Wed, 16 Sep 2020 06:41:44 +0000</pubDate>
		<dc:creator><![CDATA[jenny]]></dc:creator>
		<guid isPermaLink="false">http://business-fundamental-development-copy-6</guid>
		<description></description>
		<content:encoded><![CDATA[When one takes up cooking, the amount of information, recipes and suggestions can be overwhelming. It's easy to get lost in a sea of recipes, to spend time pondering WHAT to cook and miss one of the most important aspects when it comes to preparing a great meal - the HOW. The real success of a dish comes from correct preparation techniques just as much as it comes from high quality ingredients, flavour combinations or creative plating. 

In this course I gathered some of the most important 17 techniques that any cook, anywhere in the world, be they amateur or professional, absolutely needs to use in his kitchen. They have been developed and perfected over generations by professional chefs and, for many years, have been set as golden standards in international cuisine. 

I have done my utmost to make this an effective and pleasant experience for every student by combining clear practical demonstrations and useful information. This is just the first of a series of courses to follow, which I will develop taking into account all the feedback from my students and the topics they are interested in. ]]></content:encoded>
		<excerpt:encoded><![CDATA[	]]></excerpt:encoded>
		<wp:post_id>6867</wp:post_id>
		<wp:post_date><![CDATA[2020-09-16 06:41:44]]></wp:post_date>
		<wp:post_date_gmt><![CDATA[2020-09-16 06:41:44]]></wp:post_date_gmt>
		<wp:post_modified><![CDATA[2022-09-19 10:35:57]]></wp:post_modified>
		<wp:post_modified_gmt><![CDATA[2022-09-19 10:35:57]]></wp:post_modified_gmt>
		<wp:comment_status><![CDATA[open]]></wp:comment_status>
		<wp:ping_status><![CDATA[closed]]></wp:ping_status>
		<wp:post_name><![CDATA[essential-cooking-skills]]></wp:post_name>
		<wp:status><![CDATA[publish]]></wp:status>
		<wp:post_parent>0</wp:post_parent>
		<wp:menu_order>0</wp:menu_order>
		<wp:post_type><![CDATA[lp_lesson]]></wp:post_type>
		<wp:post_password><![CDATA[]]></wp:post_password>
		<wp:is_sticky>0</wp:is_sticky>
														<wp:postmeta>
		<wp:meta_key><![CDATA[count_items]]></wp:meta_key>
		<wp:meta_value><![CDATA[0]]></wp:meta_value>
		</wp:postmeta>
							<wp:postmeta>
		<wp:meta_key><![CDATA[slide_template]]></wp:meta_key>
		<wp:meta_value><![CDATA[]]></wp:meta_value>
		</wp:postmeta>
							<wp:postmeta>
		<wp:meta_key><![CDATA[rs_page_bg_color]]></wp:meta_key>
		<wp:meta_value><![CDATA[]]></wp:meta_value>
		</wp:postmeta>
							<wp:postmeta>
		<wp:meta_key><![CDATA[_lp_duration]]></wp:meta_key>
		<wp:meta_value><![CDATA[20 minute]]></wp:meta_value>
		</wp:postmeta>
							<wp:postmeta>
		<wp:meta_key><![CDATA[_lp_preview]]></wp:meta_key>
		<wp:meta_value><![CDATA[yes]]></wp:meta_value>
		</wp:postmeta>
							<wp:postmeta>
		<wp:meta_key><![CDATA[_edit_last]]></wp:meta_key>
		<wp:meta_value><![CDATA[1]]></wp:meta_value>
		</wp:postmeta>
							</item>
					<item>
		<title><![CDATA[Welcome to the course & what's in store]]></title>
		<link>https://socialv-wordpress.iqonic.design/lessons/welcome-to-the-course-whats-in-store/</link>
		<pubDate>Tue, 16 Jun 2015 07:40:57 +0000</pubDate>
		<dc:creator><![CDATA[jenny]]></dc:creator>
		<guid isPermaLink="false">http://demo.thimpress.com/elearningwp/lessons/import-and-parent-reference/</guid>
		<description></description>
		<content:encoded><![CDATA[<iframe width="560" height="315" src="https://www.youtube.com/embed/ArD9LEH8zRY" title="YouTube video player" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" allowfullscreen></iframe>	]]></content:encoded>
		<excerpt:encoded><![CDATA[			]]></excerpt:encoded>
		<wp:post_id>6870</wp:post_id>
		<wp:post_date><![CDATA[2015-06-16 07:40:57]]></wp:post_date>
		<wp:post_date_gmt><![CDATA[2015-06-16 07:40:57]]></wp:post_date_gmt>
		<wp:post_modified><![CDATA[2022-09-19 10:30:53]]></wp:post_modified>
		<wp:post_modified_gmt><![CDATA[2022-09-19 10:30:53]]></wp:post_modified_gmt>
		<wp:comment_status><![CDATA[closed]]></wp:comment_status>
		<wp:ping_status><![CDATA[closed]]></wp:ping_status>
		<wp:post_name><![CDATA[welcome-to-the-course-whats-in-store]]></wp:post_name>
		<wp:status><![CDATA[publish]]></wp:status>
		<wp:post_parent>0</wp:post_parent>
		<wp:menu_order>0</wp:menu_order>
		<wp:post_type><![CDATA[lp_lesson]]></wp:post_type>
		<wp:post_password><![CDATA[]]></wp:post_password>
		<wp:is_sticky>0</wp:is_sticky>
														<wp:postmeta>
		<wp:meta_key><![CDATA[_vc_post_settings]]></wp:meta_key>
		<wp:meta_value><![CDATA[a:1:{s:10:"vc_grid_id";a:0:{}}]]></wp:meta_value>
		</wp:postmeta>
							<wp:postmeta>
		<wp:meta_key><![CDATA[_lp_duration]]></wp:meta_key>
		<wp:meta_value><![CDATA[30 minute]]></wp:meta_value>
		</wp:postmeta>
							<wp:postmeta>
		<wp:meta_key><![CDATA[_lp_preview]]></wp:meta_key>
		<wp:meta_value><![CDATA[yes]]></wp:meta_value>
		</wp:postmeta>
							<wp:postmeta>
		<wp:meta_key><![CDATA[_edit_last]]></wp:meta_key>
		<wp:meta_value><![CDATA[1]]></wp:meta_value>
		</wp:postmeta>
							</item>
					<item>
		<title><![CDATA[Introduction to UX]]></title>
		<link>https://socialv-wordpress.iqonic.design/lessons/introduction-to-ux/</link>
		<pubDate>Wed, 16 Sep 2020 07:11:33 +0000</pubDate>
		<dc:creator><![CDATA[jenny]]></dc:creator>
		<guid isPermaLink="false">http://data-science-development-copy</guid>
		<description></description>
		<content:encoded><![CDATA[We and our partners are using technologies like cookies and process personal data like the IP-address or browser information in order to personalize the advertising that you see. This helps us to show you more relevant ads and improves your internet experience. We also use it in order to measure results or align our website content. Because we value your privacy, we are herewith asking for your permission to use these technologies. You can always change/withdraw your consent later by clicking on the settings button on the left lower corner of the page.

&nbsp;
<blockquote>This helps us to show you more relevant ads and improves your internet experience. We also use it in order to measure results or align our website content. Because we value your privacy, we are herewith asking for your permission to use these technologies.</blockquote>
&nbsp;

IP-address or browser information in order to personalize the advertising that you see. This helps us to show you more relevant ads and improves your internet experience. We also use it in order to measure results or align our website content. Because we value your privacy, we are herewith asking for your permission to use these technologies. You can always change/withdraw your consent later by clicking on the settings button on the left lower corner of the page.

&nbsp;

We and our partners are using technologies like cookies and process personal data like the IP-address or browser information in order to personalize the advertising that you see. This helps us to show you more relevant ads and improves your internet experience. We also use it in order to measure results or align our website content. Because we value your privacy, we are herewith asking for your permission to use these technologies. You can always change/withdraw your consent later by clicking on the settings button on the left lower corner of the page.

&nbsp;	]]></content:encoded>
		<excerpt:encoded><![CDATA[	]]></excerpt:encoded>
		<wp:post_id>6889</wp:post_id>
		<wp:post_date><![CDATA[2020-09-16 07:11:33]]></wp:post_date>
		<wp:post_date_gmt><![CDATA[2020-09-16 07:11:33]]></wp:post_date_gmt>
		<wp:post_modified><![CDATA[2022-09-19 12:39:46]]></wp:post_modified>
		<wp:post_modified_gmt><![CDATA[2022-09-19 12:39:46]]></wp:post_modified_gmt>
		<wp:comment_status><![CDATA[open]]></wp:comment_status>
		<wp:ping_status><![CDATA[closed]]></wp:ping_status>
		<wp:post_name><![CDATA[introduction-to-ux]]></wp:post_name>
		<wp:status><![CDATA[publish]]></wp:status>
		<wp:post_parent>0</wp:post_parent>
		<wp:menu_order>0</wp:menu_order>
		<wp:post_type><![CDATA[lp_lesson]]></wp:post_type>
		<wp:post_password><![CDATA[]]></wp:post_password>
		<wp:is_sticky>0</wp:is_sticky>
														<wp:postmeta>
		<wp:meta_key><![CDATA[count_items]]></wp:meta_key>
		<wp:meta_value><![CDATA[0]]></wp:meta_value>
		</wp:postmeta>
							<wp:postmeta>
		<wp:meta_key><![CDATA[slide_template]]></wp:meta_key>
		<wp:meta_value><![CDATA[]]></wp:meta_value>
		</wp:postmeta>
							<wp:postmeta>
		<wp:meta_key><![CDATA[rs_page_bg_color]]></wp:meta_key>
		<wp:meta_value><![CDATA[]]></wp:meta_value>
		</wp:postmeta>
							<wp:postmeta>
		<wp:meta_key><![CDATA[_lp_duration]]></wp:meta_key>
		<wp:meta_value><![CDATA[20 minute]]></wp:meta_value>
		</wp:postmeta>
							<wp:postmeta>
		<wp:meta_key><![CDATA[_lp_preview]]></wp:meta_key>
		<wp:meta_value><![CDATA[yes]]></wp:meta_value>
		</wp:postmeta>
							<wp:postmeta>
		<wp:meta_key><![CDATA[_edit_last]]></wp:meta_key>
		<wp:meta_value><![CDATA[1]]></wp:meta_value>
		</wp:postmeta>
							</item>
					<item>
		<title><![CDATA[Introduction and The Goal of This Course]]></title>
		<link>https://socialv-wordpress.iqonic.design/lessons/introduction-and-the-goal-of-this-course/</link>
		<pubDate>Wed, 16 Sep 2020 07:11:27 +0000</pubDate>
		<dc:creator><![CDATA[jenny]]></dc:creator>
		<guid isPermaLink="false">http://computer-fundamental-science-copy</guid>
		<description></description>
		<content:encoded><![CDATA[<iframe title="YouTube video player" src="https://www.youtube.com/embed/ArD9LEH8zRY" width="560" height="315" frameborder="0" allowfullscreen="allowfullscreen"></iframe>]]></content:encoded>
		<excerpt:encoded><![CDATA[	]]></excerpt:encoded>
		<wp:post_id>6890</wp:post_id>
		<wp:post_date><![CDATA[2020-09-16 07:11:27]]></wp:post_date>
		<wp:post_date_gmt><![CDATA[2020-09-16 07:11:27]]></wp:post_date_gmt>
		<wp:post_modified><![CDATA[2022-09-19 12:49:50]]></wp:post_modified>
		<wp:post_modified_gmt><![CDATA[2022-09-19 12:49:50]]></wp:post_modified_gmt>
		<wp:comment_status><![CDATA[open]]></wp:comment_status>
		<wp:ping_status><![CDATA[closed]]></wp:ping_status>
		<wp:post_name><![CDATA[introduction-and-the-goal-of-this-course]]></wp:post_name>
		<wp:status><![CDATA[publish]]></wp:status>
		<wp:post_parent>0</wp:post_parent>
		<wp:menu_order>0</wp:menu_order>
		<wp:post_type><![CDATA[lp_lesson]]></wp:post_type>
		<wp:post_password><![CDATA[]]></wp:post_password>
		<wp:is_sticky>0</wp:is_sticky>
														<wp:postmeta>
		<wp:meta_key><![CDATA[count_items]]></wp:meta_key>
		<wp:meta_value><![CDATA[0]]></wp:meta_value>
		</wp:postmeta>
							<wp:postmeta>
		<wp:meta_key><![CDATA[slide_template]]></wp:meta_key>
		<wp:meta_value><![CDATA[]]></wp:meta_value>
		</wp:postmeta>
							<wp:postmeta>
		<wp:meta_key><![CDATA[rs_page_bg_color]]></wp:meta_key>
		<wp:meta_value><![CDATA[]]></wp:meta_value>
		</wp:postmeta>
							<wp:postmeta>
		<wp:meta_key><![CDATA[_lp_duration]]></wp:meta_key>
		<wp:meta_value><![CDATA[20 minute]]></wp:meta_value>
		</wp:postmeta>
							<wp:postmeta>
		<wp:meta_key><![CDATA[_lp_preview]]></wp:meta_key>
		<wp:meta_value><![CDATA[yes]]></wp:meta_value>
		</wp:postmeta>
							<wp:postmeta>
		<wp:meta_key><![CDATA[_edit_last]]></wp:meta_key>
		<wp:meta_value><![CDATA[1]]></wp:meta_value>
		</wp:postmeta>
							</item>
					<item>
		<title><![CDATA[Why We Use a Keyboard]]></title>
		<link>https://socialv-wordpress.iqonic.design/lessons/why-we-use-a-keyboard/</link>
		<pubDate>Thu, 18 Jun 2020 07:05:54 +0000</pubDate>
		<dc:creator><![CDATA[jenny]]></dc:creator>
		<guid isPermaLink="false">http://data-language-development-copy-copy</guid>
		<description></description>
		<content:encoded><![CDATA[In modern music, keyboards come in all sorts of shapes and sizes. MIDI capabilities, or musical instrument digital technology, have made recording synthesized music with computers inexpensive and easily editable. Music producers heavily rely on keyboards to produce synths to use during the recording process

The keyboard is a multi dimensional instrument. It can mimic any instrument. That includes guitars, saxophone, trumpet and the piano. Keyboardists are adept at changing instruments and adding a whole new aspect to the orchestra. Adding color with quick and unpredictable changes is the job of a keyboardist. The keyboard can also produce sound effects, which are used in dramas and movies. For example the sound of some one falling or fighting.

The earliest keyboard appeared in the third century BC. It was known as the organ. The organ was the only type of keyboard till the 14th century AD. It did not have keys like today but it had levers, which had to be manipulated with the whole hand. As time went on, keyboards achieved better and better shape. Considerable work was done on the keyboard in the 20th century. Synthesizers developed from 1960's and the moog synthesizers became popular. These gave a boost to the keyboards.

The keyboard was produced through working on a small piano. People wanted a smaller piano with its sound and quality but without its weight and bulk. This led to the electronic piano, which did not succeeded. A modern day keyboard consists of electronic circuits and is able to change and reproduce various instruments.]]></content:encoded>
		<excerpt:encoded><![CDATA[	]]></excerpt:encoded>
		<wp:post_id>6891</wp:post_id>
		<wp:post_date><![CDATA[2020-06-18 07:05:54]]></wp:post_date>
		<wp:post_date_gmt><![CDATA[2020-06-18 07:05:54]]></wp:post_date_gmt>
		<wp:post_modified><![CDATA[2022-09-19 12:28:04]]></wp:post_modified>
		<wp:post_modified_gmt><![CDATA[2022-09-19 12:28:04]]></wp:post_modified_gmt>
		<wp:comment_status><![CDATA[open]]></wp:comment_status>
		<wp:ping_status><![CDATA[closed]]></wp:ping_status>
		<wp:post_name><![CDATA[why-we-use-a-keyboard]]></wp:post_name>
		<wp:status><![CDATA[publish]]></wp:status>
		<wp:post_parent>0</wp:post_parent>
		<wp:menu_order>0</wp:menu_order>
		<wp:post_type><![CDATA[lp_lesson]]></wp:post_type>
		<wp:post_password><![CDATA[]]></wp:post_password>
		<wp:is_sticky>0</wp:is_sticky>
														<wp:postmeta>
		<wp:meta_key><![CDATA[count_items]]></wp:meta_key>
		<wp:meta_value><![CDATA[0]]></wp:meta_value>
		</wp:postmeta>
							<wp:postmeta>
		<wp:meta_key><![CDATA[slide_template]]></wp:meta_key>
		<wp:meta_value><![CDATA[]]></wp:meta_value>
		</wp:postmeta>
							<wp:postmeta>
		<wp:meta_key><![CDATA[rs_page_bg_color]]></wp:meta_key>
		<wp:meta_value><![CDATA[]]></wp:meta_value>
		</wp:postmeta>
							<wp:postmeta>
		<wp:meta_key><![CDATA[_lp_duration]]></wp:meta_key>
		<wp:meta_value><![CDATA[20 minute]]></wp:meta_value>
		</wp:postmeta>
							<wp:postmeta>
		<wp:meta_key><![CDATA[_lp_preview]]></wp:meta_key>
		<wp:meta_value><![CDATA[yes]]></wp:meta_value>
		</wp:postmeta>
							<wp:postmeta>
		<wp:meta_key><![CDATA[_edit_last]]></wp:meta_key>
		<wp:meta_value><![CDATA[1]]></wp:meta_value>
		</wp:postmeta>
							</item>
					<item>
		<title><![CDATA[Benefits of kids Yoga]]></title>
		<link>https://socialv-wordpress.iqonic.design/lessons/benefits-of-kids-yoga/</link>
		<pubDate>Wed, 17 Jun 2020 02:35:51 +0000</pubDate>
		<dc:creator><![CDATA[jenny]]></dc:creator>
		<guid isPermaLink="false">http://computer-language-development-copy-3</guid>
		<description></description>
		<content:encoded><![CDATA[<h5>7 Benefits of Yoga for Young Kids</h5>
<ul>
	<li><b>Yoga helps children manage their anxiety.</b> The breathing exercises and relaxation techniques learned from practicing yoga can help children with stress management. Teaching children how to reduce stress in a healthy way is an important life skill that will help them as children and as adults.</li>
	<li><b>Yoga improves children’s emotional regulation.</b>Another benefit of yoga for children is that it helps children learn to be in the present moment while relaxing and gaining a peaceful state of mind, which ultimately improves their emotional regulation.</li>
	<li><b>Yoga boosts children’s self-esteem.</b>Yoga for kids can do wonders for their self-esteem. Perfecting a pose or improving their balance and flexibility can give young children a sense of personal empowerment.</li>
	<li><b>Yoga increases children’s body awareness and mindfulness.</b>Going through a variety of yoga poses helps children learn about their bodies and the movements they’re capable of doing.</li>
	<li><b>Yoga enhances children’s concentration and memory.One of the top benefits of kids’ yoga is that the different types of moves requires children to focus and work on their memorization skills—both of which can translate over into their academic performance.</b></li>
	<li><b>Yoga develops children’s strength and flexibility.</b>Yoga helps strengthen children’s growing bodies and helps them improve their flexibility, which can reduce their chance of injury.</li>
	<li><b>Yoga teaches discipline and reduces impulsivity.</b>Yoga can reduce challenging behaviors in the classroom by providing a physical outlet for children to express themselves. It also teaches children about discipline as they work on clearing their minds and perfecting their poses</li>
</ul>
]]></content:encoded>
		<excerpt:encoded><![CDATA[	]]></excerpt:encoded>
		<wp:post_id>6892</wp:post_id>
		<wp:post_date><![CDATA[2020-06-17 02:35:51]]></wp:post_date>
		<wp:post_date_gmt><![CDATA[2020-06-17 02:35:51]]></wp:post_date_gmt>
		<wp:post_modified><![CDATA[2022-09-20 05:00:19]]></wp:post_modified>
		<wp:post_modified_gmt><![CDATA[2022-09-20 05:00:19]]></wp:post_modified_gmt>
		<wp:comment_status><![CDATA[open]]></wp:comment_status>
		<wp:ping_status><![CDATA[closed]]></wp:ping_status>
		<wp:post_name><![CDATA[benefits-of-kids-yoga]]></wp:post_name>
		<wp:status><![CDATA[publish]]></wp:status>
		<wp:post_parent>0</wp:post_parent>
		<wp:menu_order>0</wp:menu_order>
		<wp:post_type><![CDATA[lp_lesson]]></wp:post_type>
		<wp:post_password><![CDATA[]]></wp:post_password>
		<wp:is_sticky>0</wp:is_sticky>
														<wp:postmeta>
		<wp:meta_key><![CDATA[count_items]]></wp:meta_key>
		<wp:meta_value><![CDATA[0]]></wp:meta_value>
		</wp:postmeta>
							<wp:postmeta>
		<wp:meta_key><![CDATA[slide_template]]></wp:meta_key>
		<wp:meta_value><![CDATA[]]></wp:meta_value>
		</wp:postmeta>
							<wp:postmeta>
		<wp:meta_key><![CDATA[rs_page_bg_color]]></wp:meta_key>
		<wp:meta_value><![CDATA[]]></wp:meta_value>
		</wp:postmeta>
							<wp:postmeta>
		<wp:meta_key><![CDATA[_lp_duration]]></wp:meta_key>
		<wp:meta_value><![CDATA[20 minute]]></wp:meta_value>
		</wp:postmeta>
							<wp:postmeta>
		<wp:meta_key><![CDATA[_lp_preview]]></wp:meta_key>
		<wp:meta_value><![CDATA[yes]]></wp:meta_value>
		</wp:postmeta>
							<wp:postmeta>
		<wp:meta_key><![CDATA[_edit_last]]></wp:meta_key>
		<wp:meta_value><![CDATA[1]]></wp:meta_value>
		</wp:postmeta>
							</item>
					<item>
		<title><![CDATA[Execution Contexts and Lexical Environments]]></title>
		<link>https://socialv-wordpress.iqonic.design/lessons/execution-contexts-and-lexical-environments/</link>
		<pubDate>Wed, 16 Sep 2020 05:05:53 +0000</pubDate>
		<dc:creator><![CDATA[jenny]]></dc:creator>
		<guid isPermaLink="false">http://business-fundamental-development-copy-3</guid>
		<description></description>
		<content:encoded><![CDATA[Execution Context Execution Context is created every time, the JS engine calls a function. They stays in the call stack. Lexical Environment The lexical environment includes the scopes that are present at a certain point in time. Every execution context has its own lexical environment.

In this piece I want to talk about three advanced JavaScript concepts: execution context, lexical environment, and closure.

This will be a long post. If you want to skip to the summary, scroll to the bottom of the page.

What is an Execution Context?
So, what is an execution context? Whenever you write some code, your code is in a space — that space is called the “execution context”. Imagine you wrote a simple calculator:

In JavaScript, whenever a function is invoked, a new execution context is created on the execution context currently running. A newly created one is stored in the stack for execution contexts.

So, if we call cal(), the new context will be created and it will be pushed to the context stack. But, by default, there’s an already existing context in the stack — a global execution context.

First the function cal is called and run so the new execution context is created. Then it is stored in the context stack. Then the control of the current execution context is transferred to the newly created one from, in this case, the global context.

An execution context is created whenever you call a function. When developing in JavaScript, you might have seen this error:
The function a keeps calling itself recursively. Every time a calls itself, a new execution context about a will be created and stored in the stack. Since the memory stack’s storage space isn’t infinite, it overflows.

Now you know what an execution context is, but there is more to learn. We’ll talk about it further shortly.]]></content:encoded>
		<excerpt:encoded><![CDATA[	]]></excerpt:encoded>
		<wp:post_id>6893</wp:post_id>
		<wp:post_date><![CDATA[2020-09-16 05:05:53]]></wp:post_date>
		<wp:post_date_gmt><![CDATA[2020-09-16 05:05:53]]></wp:post_date_gmt>
		<wp:post_modified><![CDATA[2022-09-19 12:52:14]]></wp:post_modified>
		<wp:post_modified_gmt><![CDATA[2022-09-19 12:52:14]]></wp:post_modified_gmt>
		<wp:comment_status><![CDATA[open]]></wp:comment_status>
		<wp:ping_status><![CDATA[closed]]></wp:ping_status>
		<wp:post_name><![CDATA[execution-contexts-and-lexical-environments]]></wp:post_name>
		<wp:status><![CDATA[publish]]></wp:status>
		<wp:post_parent>0</wp:post_parent>
		<wp:menu_order>0</wp:menu_order>
		<wp:post_type><![CDATA[lp_lesson]]></wp:post_type>
		<wp:post_password><![CDATA[]]></wp:post_password>
		<wp:is_sticky>0</wp:is_sticky>
														<wp:postmeta>
		<wp:meta_key><![CDATA[count_items]]></wp:meta_key>
		<wp:meta_value><![CDATA[0]]></wp:meta_value>
		</wp:postmeta>
							<wp:postmeta>
		<wp:meta_key><![CDATA[slide_template]]></wp:meta_key>
		<wp:meta_value><![CDATA[]]></wp:meta_value>
		</wp:postmeta>
							<wp:postmeta>
		<wp:meta_key><![CDATA[rs_page_bg_color]]></wp:meta_key>
		<wp:meta_value><![CDATA[]]></wp:meta_value>
		</wp:postmeta>
							<wp:postmeta>
		<wp:meta_key><![CDATA[_lp_duration]]></wp:meta_key>
		<wp:meta_value><![CDATA[20 minute]]></wp:meta_value>
		</wp:postmeta>
							<wp:postmeta>
		<wp:meta_key><![CDATA[_lp_preview]]></wp:meta_key>
		<wp:meta_value><![CDATA[yes]]></wp:meta_value>
		</wp:postmeta>
							<wp:postmeta>
		<wp:meta_key><![CDATA[_edit_last]]></wp:meta_key>
		<wp:meta_value><![CDATA[1]]></wp:meta_value>
		</wp:postmeta>
							</item>
					<item>
		<title><![CDATA[Markup: Describing Content]]></title>
		<link>https://socialv-wordpress.iqonic.design/lessons/markup-describing-content/</link>
		<pubDate>Wed, 16 Sep 2020 10:53:42 +0000</pubDate>
		<dc:creator><![CDATA[jenny]]></dc:creator>
		<guid isPermaLink="false">http://chemestry-basic</guid>
		<description></description>
		<content:encoded><![CDATA[<iframe width="560" height="315" src="https://www.youtube.com/embed/TZE5y-7-roU" title="YouTube video player" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" allowfullscreen></iframe>]]></content:encoded>
		<excerpt:encoded><![CDATA[	]]></excerpt:encoded>
		<wp:post_id>6903</wp:post_id>
		<wp:post_date><![CDATA[2020-09-16 10:53:42]]></wp:post_date>
		<wp:post_date_gmt><![CDATA[2020-09-16 10:53:42]]></wp:post_date_gmt>
		<wp:post_modified><![CDATA[2022-09-20 04:40:45]]></wp:post_modified>
		<wp:post_modified_gmt><![CDATA[2022-09-20 04:40:45]]></wp:post_modified_gmt>
		<wp:comment_status><![CDATA[closed]]></wp:comment_status>
		<wp:ping_status><![CDATA[closed]]></wp:ping_status>
		<wp:post_name><![CDATA[markup-describing-content]]></wp:post_name>
		<wp:status><![CDATA[publish]]></wp:status>
		<wp:post_parent>0</wp:post_parent>
		<wp:menu_order>0</wp:menu_order>
		<wp:post_type><![CDATA[lp_lesson]]></wp:post_type>
		<wp:post_password><![CDATA[]]></wp:post_password>
		<wp:is_sticky>0</wp:is_sticky>
														<wp:postmeta>
		<wp:meta_key><![CDATA[_lp_duration]]></wp:meta_key>
		<wp:meta_value><![CDATA[29 minute]]></wp:meta_value>
		</wp:postmeta>
							<wp:postmeta>
		<wp:meta_key><![CDATA[_lp_preview]]></wp:meta_key>
		<wp:meta_value><![CDATA[yes]]></wp:meta_value>
		</wp:postmeta>
							<wp:postmeta>
		<wp:meta_key><![CDATA[count_items]]></wp:meta_key>
		<wp:meta_value><![CDATA[0]]></wp:meta_value>
		</wp:postmeta>
							<wp:postmeta>
		<wp:meta_key><![CDATA[slide_template]]></wp:meta_key>
		<wp:meta_value><![CDATA[]]></wp:meta_value>
		</wp:postmeta>
							<wp:postmeta>
		<wp:meta_key><![CDATA[rs_page_bg_color]]></wp:meta_key>
		<wp:meta_value><![CDATA[]]></wp:meta_value>
		</wp:postmeta>
							<wp:postmeta>
		<wp:meta_key><![CDATA[_edit_last]]></wp:meta_key>
		<wp:meta_value><![CDATA[1]]></wp:meta_value>
		</wp:postmeta>
							</item>
					<item>
		<title><![CDATA[The Elements of the Score]]></title>
		<link>https://socialv-wordpress.iqonic.design/lessons/the-elements-of-the-score/</link>
		<pubDate>Mon, 26 Oct 2020 10:02:19 +0000</pubDate>
		<dc:creator><![CDATA[jenny]]></dc:creator>
		<guid isPermaLink="false">https://keenitsolutions.com/products/wordpress/educavo/lessons/checkbox/</guid>
		<description></description>
		<content:encoded><![CDATA[To get started, I want to just walk through a score and point out the different elements that we are seeing. We will learn what all of these mean soon.

The <score-instrument> element represents a single instrument within a <score-part>. As with the <score-part> element, each <score-instrument> has a required ID attribute, a name, and an optional abbreviation.

A <score-instrument> element is also required if the score specifies MIDI 1.0 channels, banks, or programs. An initial <midi-instrument> assignment can also be made here. MusicXML software should be able to automatically assign reasonable channels and instruments without these elements in simple cases, such as where part names match General MIDI instrument names.

The <score-instrument> element can also distinguish multiple instruments of the same type that are on the same part, such as Clarinet 1 and Clarinet 2 instruments within a Clarinets 1 and 2 part.

The <midi-instrument> element defines MIDI 1.0 instrument playback. The <midi-instrument> element can be a part of either the <score-instrument> element at the start of a part, or the <sound> element within a part.]]></content:encoded>
		<excerpt:encoded><![CDATA[	]]></excerpt:encoded>
		<wp:post_id>6904</wp:post_id>
		<wp:post_date><![CDATA[2020-10-26 10:02:19]]></wp:post_date>
		<wp:post_date_gmt><![CDATA[2020-10-26 10:02:19]]></wp:post_date_gmt>
		<wp:post_modified><![CDATA[2022-09-19 12:25:22]]></wp:post_modified>
		<wp:post_modified_gmt><![CDATA[2022-09-19 12:25:22]]></wp:post_modified_gmt>
		<wp:comment_status><![CDATA[closed]]></wp:comment_status>
		<wp:ping_status><![CDATA[closed]]></wp:ping_status>
		<wp:post_name><![CDATA[the-elements-of-the-score]]></wp:post_name>
		<wp:status><![CDATA[publish]]></wp:status>
		<wp:post_parent>0</wp:post_parent>
		<wp:menu_order>0</wp:menu_order>
		<wp:post_type><![CDATA[lp_lesson]]></wp:post_type>
		<wp:post_password><![CDATA[]]></wp:post_password>
		<wp:is_sticky>0</wp:is_sticky>
														<wp:postmeta>
		<wp:meta_key><![CDATA[_lp_duration]]></wp:meta_key>
		<wp:meta_value><![CDATA[20 minute]]></wp:meta_value>
		</wp:postmeta>
							<wp:postmeta>
		<wp:meta_key><![CDATA[_lp_preview]]></wp:meta_key>
		<wp:meta_value><![CDATA[yes]]></wp:meta_value>
		</wp:postmeta>
							<wp:postmeta>
		<wp:meta_key><![CDATA[count_items]]></wp:meta_key>
		<wp:meta_value><![CDATA[0]]></wp:meta_value>
		</wp:postmeta>
							<wp:postmeta>
		<wp:meta_key><![CDATA[slide_template]]></wp:meta_key>
		<wp:meta_value><![CDATA[]]></wp:meta_value>
		</wp:postmeta>
							<wp:postmeta>
		<wp:meta_key><![CDATA[rs_page_bg_color]]></wp:meta_key>
		<wp:meta_value><![CDATA[]]></wp:meta_value>
		</wp:postmeta>
							<wp:postmeta>
		<wp:meta_key><![CDATA[_edit_last]]></wp:meta_key>
		<wp:meta_value><![CDATA[1]]></wp:meta_value>
		</wp:postmeta>
							</item>
					<item>
		<title><![CDATA[Program Monitor Basics]]></title>
		<link>https://socialv-wordpress.iqonic.design/lessons/program-monitor-basics/</link>
		<pubDate>Wed, 16 Sep 2020 05:12:10 +0000</pubDate>
		<dc:creator><![CDATA[jenny]]></dc:creator>
		<guid isPermaLink="false">http://business-fundamental-development-copy-4</guid>
		<description></description>
		<content:encoded><![CDATA[A monitor is itself a program that provides an interface between the user and computer at the low level. In general, monitors provide a way of loading a program to primary memory (RAM), test memory, examine registers, move or copy memory, and run programs. It also provides all basic input and output (BIOS) functions.]]></content:encoded>
		<excerpt:encoded><![CDATA[	]]></excerpt:encoded>
		<wp:post_id>6911</wp:post_id>
		<wp:post_date><![CDATA[2020-09-16 05:12:10]]></wp:post_date>
		<wp:post_date_gmt><![CDATA[2020-09-16 05:12:10]]></wp:post_date_gmt>
		<wp:post_modified><![CDATA[2022-09-20 04:56:42]]></wp:post_modified>
		<wp:post_modified_gmt><![CDATA[2022-09-20 04:56:42]]></wp:post_modified_gmt>
		<wp:comment_status><![CDATA[open]]></wp:comment_status>
		<wp:ping_status><![CDATA[closed]]></wp:ping_status>
		<wp:post_name><![CDATA[program-monitor-basics]]></wp:post_name>
		<wp:status><![CDATA[publish]]></wp:status>
		<wp:post_parent>0</wp:post_parent>
		<wp:menu_order>0</wp:menu_order>
		<wp:post_type><![CDATA[lp_lesson]]></wp:post_type>
		<wp:post_password><![CDATA[]]></wp:post_password>
		<wp:is_sticky>0</wp:is_sticky>
														<wp:postmeta>
		<wp:meta_key><![CDATA[count_items]]></wp:meta_key>
		<wp:meta_value><![CDATA[0]]></wp:meta_value>
		</wp:postmeta>
							<wp:postmeta>
		<wp:meta_key><![CDATA[slide_template]]></wp:meta_key>
		<wp:meta_value><![CDATA[]]></wp:meta_value>
		</wp:postmeta>
							<wp:postmeta>
		<wp:meta_key><![CDATA[rs_page_bg_color]]></wp:meta_key>
		<wp:meta_value><![CDATA[]]></wp:meta_value>
		</wp:postmeta>
							<wp:postmeta>
		<wp:meta_key><![CDATA[_lp_duration]]></wp:meta_key>
		<wp:meta_value><![CDATA[20 minute]]></wp:meta_value>
		</wp:postmeta>
							<wp:postmeta>
		<wp:meta_key><![CDATA[_lp_preview]]></wp:meta_key>
		<wp:meta_value><![CDATA[yes]]></wp:meta_value>
		</wp:postmeta>
							<wp:postmeta>
		<wp:meta_key><![CDATA[_edit_last]]></wp:meta_key>
		<wp:meta_value><![CDATA[1]]></wp:meta_value>
		</wp:postmeta>
							</item>
					<item>
		<title><![CDATA[My Approach To Music Theory]]></title>
		<link>https://socialv-wordpress.iqonic.design/lessons/my-approach-to-music-theory/</link>
		<pubDate>Wed, 16 Sep 2020 05:12:10 +0000</pubDate>
		<dc:creator><![CDATA[jenny]]></dc:creator>
		<guid isPermaLink="false">http://business-fundamentals-copy-3</guid>
		<description></description>
		<content:encoded><![CDATA[<iframe width="560" height="315" src="https://www.youtube.com/embed/GUf8E94i5RI" title="YouTube video player" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" allowfullscreen></iframe>]]></content:encoded>
		<excerpt:encoded><![CDATA[	]]></excerpt:encoded>
		<wp:post_id>6912</wp:post_id>
		<wp:post_date><![CDATA[2020-09-16 05:12:10]]></wp:post_date>
		<wp:post_date_gmt><![CDATA[2020-09-16 05:12:10]]></wp:post_date_gmt>
		<wp:post_modified><![CDATA[2022-09-19 12:21:37]]></wp:post_modified>
		<wp:post_modified_gmt><![CDATA[2022-09-19 12:21:37]]></wp:post_modified_gmt>
		<wp:comment_status><![CDATA[open]]></wp:comment_status>
		<wp:ping_status><![CDATA[closed]]></wp:ping_status>
		<wp:post_name><![CDATA[my-approach-to-music-theory]]></wp:post_name>
		<wp:status><![CDATA[publish]]></wp:status>
		<wp:post_parent>0</wp:post_parent>
		<wp:menu_order>0</wp:menu_order>
		<wp:post_type><![CDATA[lp_lesson]]></wp:post_type>
		<wp:post_password><![CDATA[]]></wp:post_password>
		<wp:is_sticky>0</wp:is_sticky>
														<wp:postmeta>
		<wp:meta_key><![CDATA[count_items]]></wp:meta_key>
		<wp:meta_value><![CDATA[0]]></wp:meta_value>
		</wp:postmeta>
							<wp:postmeta>
		<wp:meta_key><![CDATA[slide_template]]></wp:meta_key>
		<wp:meta_value><![CDATA[]]></wp:meta_value>
		</wp:postmeta>
							<wp:postmeta>
		<wp:meta_key><![CDATA[rs_page_bg_color]]></wp:meta_key>
		<wp:meta_value><![CDATA[]]></wp:meta_value>
		</wp:postmeta>
							<wp:postmeta>
		<wp:meta_key><![CDATA[_lp_duration]]></wp:meta_key>
		<wp:meta_value><![CDATA[20 minute]]></wp:meta_value>
		</wp:postmeta>
							<wp:postmeta>
		<wp:meta_key><![CDATA[_lp_preview]]></wp:meta_key>
		<wp:meta_value><![CDATA[yes]]></wp:meta_value>
		</wp:postmeta>
							<wp:postmeta>
		<wp:meta_key><![CDATA[_edit_last]]></wp:meta_key>
		<wp:meta_value><![CDATA[1]]></wp:meta_value>
		</wp:postmeta>
							</item>
					<item>
		<title><![CDATA[How To Build Your Own Workout Routine]]></title>
		<link>https://socialv-wordpress.iqonic.design/lessons/how-to-build-your-own-workout-routine/</link>
		<pubDate>Wed, 16 Sep 2020 05:12:16 +0000</pubDate>
		<dc:creator><![CDATA[jenny]]></dc:creator>
		<guid isPermaLink="false">http://business-startup-copy-2</guid>
		<description></description>
		<content:encoded><![CDATA[We and our partners are using technologies like cookies and process personal data like the IP-address or browser information in order to personalize the advertising that you see. This helps us to show you more relevant ads and improves your internet experience. We also use it in order to measure results or align our website content. Because we value your privacy, we are herewith asking for your permission to use these technologies. You can always change/withdraw your consent later by clicking on the settings button on the left lower corner of the page.

&nbsp;
<blockquote>This helps us to show you more relevant ads and improves your internet experience. We also use it in order to measure results or align our website content. Because we value your privacy, we are herewith asking for your permission to use these technologies.</blockquote>
&nbsp;

IP-address or browser information in order to personalize the advertising that you see. This helps us to show you more relevant ads and improves your internet experience. We also use it in order to measure results or align our website content. Because we value your privacy, we are herewith asking for your permission to use these technologies. You can always change/withdraw your consent later by clicking on the settings button on the left lower corner of the page.

&nbsp;

We and our partners are using technologies like cookies and process personal data like the IP-address or browser information in order to personalize the advertising that you see. This helps us to show you more relevant ads and improves your internet experience. We also use it in order to measure results or align our website content. Because we value your privacy, we are herewith asking for your permission to use these technologies. You can always change/withdraw your consent later by clicking on the settings button on the left lower corner of the page.

&nbsp;	]]></content:encoded>
		<excerpt:encoded><![CDATA[	]]></excerpt:encoded>
		<wp:post_id>6916</wp:post_id>
		<wp:post_date><![CDATA[2020-09-16 05:12:16]]></wp:post_date>
		<wp:post_date_gmt><![CDATA[2020-09-16 05:12:16]]></wp:post_date_gmt>
		<wp:post_modified><![CDATA[2022-09-19 12:57:39]]></wp:post_modified>
		<wp:post_modified_gmt><![CDATA[2022-09-19 12:57:39]]></wp:post_modified_gmt>
		<wp:comment_status><![CDATA[open]]></wp:comment_status>
		<wp:ping_status><![CDATA[closed]]></wp:ping_status>
		<wp:post_name><![CDATA[how-to-build-your-own-workout-routine]]></wp:post_name>
		<wp:status><![CDATA[publish]]></wp:status>
		<wp:post_parent>0</wp:post_parent>
		<wp:menu_order>0</wp:menu_order>
		<wp:post_type><![CDATA[lp_lesson]]></wp:post_type>
		<wp:post_password><![CDATA[]]></wp:post_password>
		<wp:is_sticky>0</wp:is_sticky>
														<wp:postmeta>
		<wp:meta_key><![CDATA[count_items]]></wp:meta_key>
		<wp:meta_value><![CDATA[0]]></wp:meta_value>
		</wp:postmeta>
							<wp:postmeta>
		<wp:meta_key><![CDATA[slide_template]]></wp:meta_key>
		<wp:meta_value><![CDATA[]]></wp:meta_value>
		</wp:postmeta>
							<wp:postmeta>
		<wp:meta_key><![CDATA[rs_page_bg_color]]></wp:meta_key>
		<wp:meta_value><![CDATA[]]></wp:meta_value>
		</wp:postmeta>
							<wp:postmeta>
		<wp:meta_key><![CDATA[_lp_duration]]></wp:meta_key>
		<wp:meta_value><![CDATA[20 minute]]></wp:meta_value>
		</wp:postmeta>
							<wp:postmeta>
		<wp:meta_key><![CDATA[_lp_preview]]></wp:meta_key>
		<wp:meta_value><![CDATA[yes]]></wp:meta_value>
		</wp:postmeta>
							<wp:postmeta>
		<wp:meta_key><![CDATA[_edit_last]]></wp:meta_key>
		<wp:meta_value><![CDATA[1]]></wp:meta_value>
		</wp:postmeta>
							</item>
					<item>
		<title><![CDATA[Welcome & Overview]]></title>
		<link>https://socialv-wordpress.iqonic.design/lessons/welcome-overview/</link>
		<pubDate>Wed, 16 Sep 2020 06:55:11 +0000</pubDate>
		<dc:creator><![CDATA[jenny]]></dc:creator>
		<guid isPermaLink="false">http://compiler-fundamentals-copy</guid>
		<description></description>
		<content:encoded><![CDATA[<iframe width="560" height="315" src="https://www.youtube.com/embed/nH6OlbjGbaw" title="YouTube video player" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" allowfullscreen></iframe>]]></content:encoded>
		<excerpt:encoded><![CDATA[	]]></excerpt:encoded>
		<wp:post_id>6917</wp:post_id>
		<wp:post_date><![CDATA[2020-09-16 06:55:11]]></wp:post_date>
		<wp:post_date_gmt><![CDATA[2020-09-16 06:55:11]]></wp:post_date_gmt>
		<wp:post_modified><![CDATA[2022-09-19 12:20:12]]></wp:post_modified>
		<wp:post_modified_gmt><![CDATA[2022-09-19 12:20:12]]></wp:post_modified_gmt>
		<wp:comment_status><![CDATA[open]]></wp:comment_status>
		<wp:ping_status><![CDATA[closed]]></wp:ping_status>
		<wp:post_name><![CDATA[welcome-overview]]></wp:post_name>
		<wp:status><![CDATA[publish]]></wp:status>
		<wp:post_parent>0</wp:post_parent>
		<wp:menu_order>0</wp:menu_order>
		<wp:post_type><![CDATA[lp_lesson]]></wp:post_type>
		<wp:post_password><![CDATA[]]></wp:post_password>
		<wp:is_sticky>0</wp:is_sticky>
														<wp:postmeta>
		<wp:meta_key><![CDATA[count_items]]></wp:meta_key>
		<wp:meta_value><![CDATA[0]]></wp:meta_value>
		</wp:postmeta>
							<wp:postmeta>
		<wp:meta_key><![CDATA[slide_template]]></wp:meta_key>
		<wp:meta_value><![CDATA[]]></wp:meta_value>
		</wp:postmeta>
							<wp:postmeta>
		<wp:meta_key><![CDATA[rs_page_bg_color]]></wp:meta_key>
		<wp:meta_value><![CDATA[]]></wp:meta_value>
		</wp:postmeta>
							<wp:postmeta>
		<wp:meta_key><![CDATA[_lp_duration]]></wp:meta_key>
		<wp:meta_value><![CDATA[20 minute]]></wp:meta_value>
		</wp:postmeta>
							<wp:postmeta>
		<wp:meta_key><![CDATA[_lp_preview]]></wp:meta_key>
		<wp:meta_value><![CDATA[yes]]></wp:meta_value>
		</wp:postmeta>
							<wp:postmeta>
		<wp:meta_key><![CDATA[_edit_last]]></wp:meta_key>
		<wp:meta_value><![CDATA[1]]></wp:meta_value>
		</wp:postmeta>
							</item>
					<item>
		<title><![CDATA[CSS Frameworks]]></title>
		<link>https://socialv-wordpress.iqonic.design/lessons/css-frameworks/</link>
		<pubDate>Wed, 16 Sep 2020 06:55:18 +0000</pubDate>
		<dc:creator><![CDATA[jenny]]></dc:creator>
		<guid isPermaLink="false">http://compiler-startup-copy</guid>
		<description></description>
		<content:encoded><![CDATA[We and our partners are using technologies like cookies and process personal data like the IP-address or browser information in order to personalize the advertising that you see. This helps us to show you more relevant ads and improves your internet experience. We also use it in order to measure results or align our website content. Because we value your privacy, we are herewith asking for your permission to use these technologies. You can always change/withdraw your consent later by clicking on the settings button on the left lower corner of the page.

&nbsp;
<blockquote>This helps us to show you more relevant ads and improves your internet experience. We also use it in order to measure results or align our website content. Because we value your privacy, we are herewith asking for your permission to use these technologies.</blockquote>
&nbsp;

IP-address or browser information in order to personalize the advertising that you see. This helps us to show you more relevant ads and improves your internet experience. We also use it in order to measure results or align our website content. Because we value your privacy, we are herewith asking for your permission to use these technologies. You can always change/withdraw your consent later by clicking on the settings button on the left lower corner of the page.

&nbsp;

We and our partners are using technologies like cookies and process personal data like the IP-address or browser information in order to personalize the advertising that you see. This helps us to show you more relevant ads and improves your internet experience. We also use it in order to measure results or align our website content. Because we value your privacy, we are herewith asking for your permission to use these technologies. You can always change/withdraw your consent later by clicking on the settings button on the left lower corner of the page.

&nbsp;	]]></content:encoded>
		<excerpt:encoded><![CDATA[	]]></excerpt:encoded>
		<wp:post_id>6918</wp:post_id>
		<wp:post_date><![CDATA[2020-09-16 06:55:18]]></wp:post_date>
		<wp:post_date_gmt><![CDATA[2020-09-16 06:55:18]]></wp:post_date_gmt>
		<wp:post_modified><![CDATA[2022-09-20 04:45:20]]></wp:post_modified>
		<wp:post_modified_gmt><![CDATA[2022-09-20 04:45:20]]></wp:post_modified_gmt>
		<wp:comment_status><![CDATA[open]]></wp:comment_status>
		<wp:ping_status><![CDATA[closed]]></wp:ping_status>
		<wp:post_name><![CDATA[css-frameworks]]></wp:post_name>
		<wp:status><![CDATA[publish]]></wp:status>
		<wp:post_parent>0</wp:post_parent>
		<wp:menu_order>0</wp:menu_order>
		<wp:post_type><![CDATA[lp_lesson]]></wp:post_type>
		<wp:post_password><![CDATA[]]></wp:post_password>
		<wp:is_sticky>0</wp:is_sticky>
														<wp:postmeta>
		<wp:meta_key><![CDATA[count_items]]></wp:meta_key>
		<wp:meta_value><![CDATA[0]]></wp:meta_value>
		</wp:postmeta>
							<wp:postmeta>
		<wp:meta_key><![CDATA[slide_template]]></wp:meta_key>
		<wp:meta_value><![CDATA[]]></wp:meta_value>
		</wp:postmeta>
							<wp:postmeta>
		<wp:meta_key><![CDATA[rs_page_bg_color]]></wp:meta_key>
		<wp:meta_value><![CDATA[]]></wp:meta_value>
		</wp:postmeta>
							<wp:postmeta>
		<wp:meta_key><![CDATA[_lp_duration]]></wp:meta_key>
		<wp:meta_value><![CDATA[20 minute]]></wp:meta_value>
		</wp:postmeta>
							<wp:postmeta>
		<wp:meta_key><![CDATA[_lp_preview]]></wp:meta_key>
		<wp:meta_value><![CDATA[yes]]></wp:meta_value>
		</wp:postmeta>
							<wp:postmeta>
		<wp:meta_key><![CDATA[_edit_last]]></wp:meta_key>
		<wp:meta_value><![CDATA[1]]></wp:meta_value>
		</wp:postmeta>
							</item>
					<item>
		<title><![CDATA[Overview – Yoga for Kids Aged 2-5 Years]]></title>
		<link>https://socialv-wordpress.iqonic.design/lessons/overview-yoga-for-kids-aged-2-5-years-2/</link>
		<pubDate>Tue, 20 Sep 2022 04:49:09 +0000</pubDate>
		<dc:creator><![CDATA[jenny]]></dc:creator>
		<guid isPermaLink="false">https:/lessons/overview-yoga-for-kids-aged-2-5-years-2/</guid>
		<description></description>
		<content:encoded><![CDATA[The Benefits of Yoga for Preschoolers and Toddlers
Yoga at this age (toddler and preschool) is about nurturing and supporting a child’s natural progression through developmental milestones, and engaging their innate curiosity and playfulness while affirming security, safety and belonging. 

Yoga helps children experiment with breath, stillness and self-regulation while the physical poses assist in building gross and fine motor skills, coordination, balance and strength.

Through specific preschooler and toddler yoga poses, we can also address any potential developmental delays.

Studies Show The Benefits Of Yoga For School-Aged Children Include:
Enhanced Learning

Improved Behavior and Self-Regulation

Heightened Mental, Social and Emotional Health

Improved Sensory Processing

Relieved Stress

Increased Feelings of Well-Being

Community

Increased Self-Awareness

Developing Fine Motor Skills

6 Simple and Fun Yoga Poses for Preschoolers and Toddlers 
Here are yoga poses that are accessible for children ages 2-5 (toddler and preschool). These poses support child development by building self-regulation skills and social emotional learning (SEL), reducing tension, promoting strength and balance, and fostering improved 

]]></content:encoded>
		<excerpt:encoded><![CDATA[]]></excerpt:encoded>
		<wp:post_id>7092</wp:post_id>
		<wp:post_date><![CDATA[2022-09-20 04:49:09]]></wp:post_date>
		<wp:post_date_gmt><![CDATA[2022-09-20 04:49:09]]></wp:post_date_gmt>
		<wp:post_modified><![CDATA[2022-09-20 05:05:02]]></wp:post_modified>
		<wp:post_modified_gmt><![CDATA[2022-09-20 05:05:02]]></wp:post_modified_gmt>
		<wp:comment_status><![CDATA[open]]></wp:comment_status>
		<wp:ping_status><![CDATA[closed]]></wp:ping_status>
		<wp:post_name><![CDATA[overview-yoga-for-kids-aged-2-5-years-2]]></wp:post_name>
		<wp:status><![CDATA[publish]]></wp:status>
		<wp:post_parent>0</wp:post_parent>
		<wp:menu_order>0</wp:menu_order>
		<wp:post_type><![CDATA[lp_lesson]]></wp:post_type>
		<wp:post_password><![CDATA[]]></wp:post_password>
		<wp:is_sticky>0</wp:is_sticky>
														<wp:postmeta>
		<wp:meta_key><![CDATA[_lp_duration]]></wp:meta_key>
		<wp:meta_value><![CDATA[0 minute]]></wp:meta_value>
		</wp:postmeta>
							<wp:postmeta>
		<wp:meta_key><![CDATA[_lp_preview]]></wp:meta_key>
		<wp:meta_value><![CDATA[no]]></wp:meta_value>
		</wp:postmeta>
							<wp:postmeta>
		<wp:meta_key><![CDATA[_edit_last]]></wp:meta_key>
		<wp:meta_value><![CDATA[1]]></wp:meta_value>
		</wp:postmeta>
							</item>
					<item>
		<title><![CDATA[Development for Ages 5-8]]></title>
		<link>https://socialv-wordpress.iqonic.design/lessons/development-for-ages-5-8/</link>
		<pubDate>Tue, 20 Sep 2022 04:49:40 +0000</pubDate>
		<dc:creator><![CDATA[jenny]]></dc:creator>
		<guid isPermaLink="false">https:/lessons/development-for-ages-5-8/</guid>
		<description></description>
		<content:encoded><![CDATA[This blog simply busts the myths that the term yoga is a healthy practice for adults only. People of all age groups including kids can engage in yoga and maintain a healthy lifestyle. Not just physical health, most yoga asanas also support the mental health of individuals. An ideal yoga session usually lasts for 45-60 minutes and consists of 5-7 asanas.

In total, 84 traditional yoga asanas have been carried forward for centuries. Many traditional yoga asanas have been molded according to the present situations and introduced as new variants. Yoga for kids is termed a healthy practice that helps in maintaining well-being. This blog focuses on introducing the importance and advantages of yoga for kids from the age group of 6-14 years.

<h5>Heart Health</h5>
Since it is well-known that yoga reduces the level of stress, it also focuses on maintaining the ideal blood pressure and pulse rate. Most heart diseases and issues in human beings generally arise due to imbalances in pulse rates and blood pressure.

<h5>Stronger Spine</h5>
Most of the poses of yoga require lengthening and stretching the body. It reduces the tension and the stiffness in the muscles that surround the spinal cord. Therefore, yoga poses are generally advised as a remedy for most back problems.

<h5>Increased Strength</h5>
Perfection in yoga poses arrives after perfect balancing and flexibility. People that practice the poses of yoga regularly are comparatively more flexible than people that do not. It also increases the overall strength and stamina of the body.

<h5>Maintenance of Ideal BMI</h5>
BMI refers to the Body Mass Index that is calculated after a comparative analysis of the height and bodyweight of the individual. It is proven that yoga helps in maintaining a healthy BMI that ranges from 18.9kg/m2 to 24.5kg/m2.

<h5>Joint Health</h5>
Most people face joint issues and problems like arthritis after the age of 40. A human being that practices yoga asanas daily is lesser prone to such joint-related issues as some poses also play a vital role in bone health.]]></content:encoded>
		<excerpt:encoded><![CDATA[]]></excerpt:encoded>
		<wp:post_id>7094</wp:post_id>
		<wp:post_date><![CDATA[2022-09-20 04:49:40]]></wp:post_date>
		<wp:post_date_gmt><![CDATA[2022-09-20 04:49:40]]></wp:post_date_gmt>
		<wp:post_modified><![CDATA[2022-09-20 05:09:26]]></wp:post_modified>
		<wp:post_modified_gmt><![CDATA[2022-09-20 05:09:26]]></wp:post_modified_gmt>
		<wp:comment_status><![CDATA[open]]></wp:comment_status>
		<wp:ping_status><![CDATA[closed]]></wp:ping_status>
		<wp:post_name><![CDATA[development-for-ages-5-8]]></wp:post_name>
		<wp:status><![CDATA[publish]]></wp:status>
		<wp:post_parent>0</wp:post_parent>
		<wp:menu_order>0</wp:menu_order>
		<wp:post_type><![CDATA[lp_lesson]]></wp:post_type>
		<wp:post_password><![CDATA[]]></wp:post_password>
		<wp:is_sticky>0</wp:is_sticky>
														<wp:postmeta>
		<wp:meta_key><![CDATA[_lp_duration]]></wp:meta_key>
		<wp:meta_value><![CDATA[0 minute]]></wp:meta_value>
		</wp:postmeta>
							<wp:postmeta>
		<wp:meta_key><![CDATA[_lp_preview]]></wp:meta_key>
		<wp:meta_value><![CDATA[no]]></wp:meta_value>
		</wp:postmeta>
							<wp:postmeta>
		<wp:meta_key><![CDATA[_edit_last]]></wp:meta_key>
		<wp:meta_value><![CDATA[1]]></wp:meta_value>
		</wp:postmeta>
							</item>
					<item>
		<title><![CDATA[Yoga poses for ages 9-12]]></title>
		<link>https://socialv-wordpress.iqonic.design/lessons/yoga-poses-for-ages-9-12/</link>
		<pubDate>Tue, 20 Sep 2022 04:50:04 +0000</pubDate>
		<dc:creator><![CDATA[jenny]]></dc:creator>
		<guid isPermaLink="false">https:/lessons/yoga-poses-for-ages-9-12/</guid>
		<description></description>
		<content:encoded><![CDATA[<h5>Yoga Sequence for Kids: Yoga for Kids - 7 to 11 age group</h5>

In teaching yoga to children of age 7-11, the yoga sequence should be practiced in a playful way. The yoga sequence should be sensitive to the physical and mental development of the kids of this age category. Yoga poses that are primarily meant for adults should not form part of the practice like arm balances, overstretching of the hamstrings, overstretching of the hips, or even headstands. Their bones are still soft and tender, hence care should be taken while creating a yoga sequence for kids (7-11).

The focus of the below yoga sequence is to encourage balance and love. Loving not just themselves, but their partners in practice. The yoga sequence is done in pairs creating a playful flow with energy and balance. The kids sit in a circle making eye contact as they spread joy and happiness while they also get physically active.

Yoga introduced to kids early in their lives not only encourages physical fitness but the natural creativity in them will be displayed as they express themselves.]]></content:encoded>
		<excerpt:encoded><![CDATA[]]></excerpt:encoded>
		<wp:post_id>7095</wp:post_id>
		<wp:post_date><![CDATA[2022-09-20 04:50:04]]></wp:post_date>
		<wp:post_date_gmt><![CDATA[2022-09-20 04:50:04]]></wp:post_date_gmt>
		<wp:post_modified><![CDATA[2022-09-20 05:13:19]]></wp:post_modified>
		<wp:post_modified_gmt><![CDATA[2022-09-20 05:13:19]]></wp:post_modified_gmt>
		<wp:comment_status><![CDATA[open]]></wp:comment_status>
		<wp:ping_status><![CDATA[closed]]></wp:ping_status>
		<wp:post_name><![CDATA[yoga-poses-for-ages-9-12]]></wp:post_name>
		<wp:status><![CDATA[publish]]></wp:status>
		<wp:post_parent>0</wp:post_parent>
		<wp:menu_order>0</wp:menu_order>
		<wp:post_type><![CDATA[lp_lesson]]></wp:post_type>
		<wp:post_password><![CDATA[]]></wp:post_password>
		<wp:is_sticky>0</wp:is_sticky>
														<wp:postmeta>
		<wp:meta_key><![CDATA[_lp_duration]]></wp:meta_key>
		<wp:meta_value><![CDATA[0 minute]]></wp:meta_value>
		</wp:postmeta>
							<wp:postmeta>
		<wp:meta_key><![CDATA[_lp_preview]]></wp:meta_key>
		<wp:meta_value><![CDATA[no]]></wp:meta_value>
		</wp:postmeta>
							<wp:postmeta>
		<wp:meta_key><![CDATA[_edit_last]]></wp:meta_key>
		<wp:meta_value><![CDATA[1]]></wp:meta_value>
		</wp:postmeta>
							</item>
					<item>
		<title><![CDATA[Yoga and teen mental health]]></title>
		<link>https://socialv-wordpress.iqonic.design/lessons/yoga-and-teen-mental-health/</link>
		<pubDate>Tue, 20 Sep 2022 04:50:33 +0000</pubDate>
		<dc:creator><![CDATA[jenny]]></dc:creator>
		<guid isPermaLink="false">https:/lessons/yoga-and-teen-mental-health/</guid>
		<description></description>
		<content:encoded><![CDATA[Today’s modern life between all the hustle and bustle of new trends or fads, our  Teenagers or Generation Z usually fail to prioritize their wellbeing. The cherry on the top is the pandemic which has brought various changes to our regular routine and isolation at home and absence of the active environment of an academic institute, giving birth to increased rate of anxiety, stress and depression. Teenagers today are facing challenges which were very rare and unusual 30-40 years back. So the question is how can we help and guide our teens?

Voilà! The solution to this problem is YOGA. Yoga is not only performing asanas, it is much more than that. Yoga is a simple and wholesome process which keeps our physical, mental, emotional, social and spiritual wellbeing in shape.

So, in this blog I’ll be sharing with you about some common teenage problems and how a little bit about yoga for children in this technology centric lifestyle to bring happiness & peace.

<h5>Teenage World and Their Problems</h5>
Now, let’s take a quick glimpse into the Teenage world. Teenagers between 13 and 19 years of age go through a turbulent phase both internally and externally. They deal with hormonal changes, social changes, academic, competitive exam preparations and peer pressure and on and on which leads to various problems.

<h5>Most Common Problems for Today’s Teenagers are</h5>
<ul>
<li><b>Anxiety and Depression:</b> According to some researches 75% of our teenagers cope with various mental health problems unknowingly. Teenagers are more concerned about their physical appearances and as a result they ignore their mental health.</li>
<li><b>Self Esteem:</b> Children with early childhood trauma lack in confidence, they may feel victimized, feel lonely and isolated and also might have negative thoughts about themselves.</li>
<li><b>Body Image:</b> Body image issue can begin in teenagers in response to the changes in body during puberty. Individuals who are obese and dissatisfied with their physical appearance are prone to depression.</li>
<li><b>Cyber Addiction:</b> Today’s generation Z a.k.a. “Loneliest generation” in the race to follow new trends spend endless hours online and on smartphones inadvertently moving them away from human interaction and sending them into isolation with no real people to talk to or have conversations. They Crave for social acceptance.</li>
<li><b>Bullying and Peer Pressure:</b> Bullying and negative peer pressure can cause teens to suffer from low self esteem, anxiety and depression.</li>
<li><b>Lack of Concentration:</b> Physical and emotional changes, lack of sleep, a poor routine, excessive screen time or a family trauma can cause lack of concentration in teenagers. Unfocussed, confused teens are unable to find happiness and purpose in life.</li>
</ul>]]></content:encoded>
		<excerpt:encoded><![CDATA[]]></excerpt:encoded>
		<wp:post_id>7096</wp:post_id>
		<wp:post_date><![CDATA[2022-09-20 04:50:33]]></wp:post_date>
		<wp:post_date_gmt><![CDATA[2022-09-20 04:50:33]]></wp:post_date_gmt>
		<wp:post_modified><![CDATA[2022-09-20 05:17:55]]></wp:post_modified>
		<wp:post_modified_gmt><![CDATA[2022-09-20 05:17:55]]></wp:post_modified_gmt>
		<wp:comment_status><![CDATA[open]]></wp:comment_status>
		<wp:ping_status><![CDATA[closed]]></wp:ping_status>
		<wp:post_name><![CDATA[yoga-and-teen-mental-health]]></wp:post_name>
		<wp:status><![CDATA[publish]]></wp:status>
		<wp:post_parent>0</wp:post_parent>
		<wp:menu_order>0</wp:menu_order>
		<wp:post_type><![CDATA[lp_lesson]]></wp:post_type>
		<wp:post_password><![CDATA[]]></wp:post_password>
		<wp:is_sticky>0</wp:is_sticky>
														<wp:postmeta>
		<wp:meta_key><![CDATA[_lp_duration]]></wp:meta_key>
		<wp:meta_value><![CDATA[0 minute]]></wp:meta_value>
		</wp:postmeta>
							<wp:postmeta>
		<wp:meta_key><![CDATA[_lp_preview]]></wp:meta_key>
		<wp:meta_value><![CDATA[no]]></wp:meta_value>
		</wp:postmeta>
							<wp:postmeta>
		<wp:meta_key><![CDATA[_edit_last]]></wp:meta_key>
		<wp:meta_value><![CDATA[1]]></wp:meta_value>
		</wp:postmeta>
							</item>
					<item>
		<title><![CDATA[Clip - key framing & audio level animation]]></title>
		<link>https://socialv-wordpress.iqonic.design/lessons/clip-key-framing-audio-level-animation/</link>
		<pubDate>Tue, 20 Sep 2022 04:57:36 +0000</pubDate>
		<dc:creator><![CDATA[jenny]]></dc:creator>
		<guid isPermaLink="false">https:/lessons/clip-key-framing-audio-level-animation/</guid>
		<description></description>
		<content:encoded><![CDATA[Keyframe audio effects in Final Cut Pro
With Final Cut Pro, you can use keyframes to create simple changes to audio over time, such as fading the volume or an effect in or out in the middle of a clip.

You place keyframes at specific points in a clip to change the parameter value of an audio enhancement or effect at those points. For example, you can keyframe specific points for volume or for an effect such as reverb or distortion.

You can set keyframes to adjust a clip’s volume directly in the timeline or the Audio inspector. To see keyframes in the timeline for all other effects, you need to display the Audio Animation editor.]]></content:encoded>
		<excerpt:encoded><![CDATA[]]></excerpt:encoded>
		<wp:post_id>7099</wp:post_id>
		<wp:post_date><![CDATA[2022-09-20 04:57:36]]></wp:post_date>
		<wp:post_date_gmt><![CDATA[2022-09-20 04:57:36]]></wp:post_date_gmt>
		<wp:post_modified><![CDATA[2022-09-20 04:58:58]]></wp:post_modified>
		<wp:post_modified_gmt><![CDATA[2022-09-20 04:58:58]]></wp:post_modified_gmt>
		<wp:comment_status><![CDATA[open]]></wp:comment_status>
		<wp:ping_status><![CDATA[closed]]></wp:ping_status>
		<wp:post_name><![CDATA[clip-key-framing-audio-level-animation]]></wp:post_name>
		<wp:status><![CDATA[publish]]></wp:status>
		<wp:post_parent>0</wp:post_parent>
		<wp:menu_order>0</wp:menu_order>
		<wp:post_type><![CDATA[lp_lesson]]></wp:post_type>
		<wp:post_password><![CDATA[]]></wp:post_password>
		<wp:is_sticky>0</wp:is_sticky>
														<wp:postmeta>
		<wp:meta_key><![CDATA[_lp_duration]]></wp:meta_key>
		<wp:meta_value><![CDATA[0 minute]]></wp:meta_value>
		</wp:postmeta>
							<wp:postmeta>
		<wp:meta_key><![CDATA[_lp_preview]]></wp:meta_key>
		<wp:meta_value><![CDATA[no]]></wp:meta_value>
		</wp:postmeta>
							<wp:postmeta>
		<wp:meta_key><![CDATA[_edit_last]]></wp:meta_key>
		<wp:meta_value><![CDATA[1]]></wp:meta_value>
		</wp:postmeta>
							</item>
					<item>
		<title><![CDATA[Complete Chess Training by a Chess Grandmaster]]></title>
		<link>https://socialv-wordpress.iqonic.design/lessons/complete-chess-training-by-a-chess-grandmaster/</link>
		<pubDate>Tue, 20 Sep 2022 06:15:52 +0000</pubDate>
		<dc:creator><![CDATA[jenny]]></dc:creator>
		<guid isPermaLink="false">https:/lessons/complete-chess-training-by-a-chess-grandmaster/</guid>
		<description></description>
		<content:encoded><![CDATA[<iframe width="560" height="315" src="https://www.youtube.com/embed/6XWnVI6nhXA" title="YouTube video player" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" allowfullscreen></iframe>]]></content:encoded>
		<excerpt:encoded><![CDATA[]]></excerpt:encoded>
		<wp:post_id>7114</wp:post_id>
		<wp:post_date><![CDATA[2022-09-20 06:15:52]]></wp:post_date>
		<wp:post_date_gmt><![CDATA[2022-09-20 06:15:52]]></wp:post_date_gmt>
		<wp:post_modified><![CDATA[2022-09-20 06:19:53]]></wp:post_modified>
		<wp:post_modified_gmt><![CDATA[2022-09-20 06:19:53]]></wp:post_modified_gmt>
		<wp:comment_status><![CDATA[open]]></wp:comment_status>
		<wp:ping_status><![CDATA[closed]]></wp:ping_status>
		<wp:post_name><![CDATA[complete-chess-training-by-a-chess-grandmaster]]></wp:post_name>
		<wp:status><![CDATA[publish]]></wp:status>
		<wp:post_parent>0</wp:post_parent>
		<wp:menu_order>0</wp:menu_order>
		<wp:post_type><![CDATA[lp_lesson]]></wp:post_type>
		<wp:post_password><![CDATA[]]></wp:post_password>
		<wp:is_sticky>0</wp:is_sticky>
														<wp:postmeta>
		<wp:meta_key><![CDATA[_lp_duration]]></wp:meta_key>
		<wp:meta_value><![CDATA[0 minute]]></wp:meta_value>
		</wp:postmeta>
							<wp:postmeta>
		<wp:meta_key><![CDATA[_lp_preview]]></wp:meta_key>
		<wp:meta_value><![CDATA[no]]></wp:meta_value>
		</wp:postmeta>
							<wp:postmeta>
		<wp:meta_key><![CDATA[_edit_last]]></wp:meta_key>
		<wp:meta_value><![CDATA[1]]></wp:meta_value>
		</wp:postmeta>
							</item>
					<item>
		<title><![CDATA[Grandmaster Chess Training Plan for Rapid Chess Improvement]]></title>
		<link>https://socialv-wordpress.iqonic.design/lessons/grandmaster-chess-training-plan-for-rapid-chess-improvement/</link>
		<pubDate>Tue, 20 Sep 2022 06:16:07 +0000</pubDate>
		<dc:creator><![CDATA[jenny]]></dc:creator>
		<guid isPermaLink="false">https:/lessons/grandmaster-chess-training-plan-for-rapid-chess-improvement/</guid>
		<description></description>
		<content:encoded><![CDATA[<iframe width="560" height="315" src="https://www.youtube.com/embed/qmNzKgnsWBc" title="YouTube video player" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" allowfullscreen></iframe>]]></content:encoded>
		<excerpt:encoded><![CDATA[]]></excerpt:encoded>
		<wp:post_id>7115</wp:post_id>
		<wp:post_date><![CDATA[2022-09-20 06:16:07]]></wp:post_date>
		<wp:post_date_gmt><![CDATA[2022-09-20 06:16:07]]></wp:post_date_gmt>
		<wp:post_modified><![CDATA[2022-09-20 06:20:37]]></wp:post_modified>
		<wp:post_modified_gmt><![CDATA[2022-09-20 06:20:37]]></wp:post_modified_gmt>
		<wp:comment_status><![CDATA[open]]></wp:comment_status>
		<wp:ping_status><![CDATA[closed]]></wp:ping_status>
		<wp:post_name><![CDATA[grandmaster-chess-training-plan-for-rapid-chess-improvement]]></wp:post_name>
		<wp:status><![CDATA[publish]]></wp:status>
		<wp:post_parent>0</wp:post_parent>
		<wp:menu_order>0</wp:menu_order>
		<wp:post_type><![CDATA[lp_lesson]]></wp:post_type>
		<wp:post_password><![CDATA[]]></wp:post_password>
		<wp:is_sticky>0</wp:is_sticky>
														<wp:postmeta>
		<wp:meta_key><![CDATA[_lp_duration]]></wp:meta_key>
		<wp:meta_value><![CDATA[0 minute]]></wp:meta_value>
		</wp:postmeta>
							<wp:postmeta>
		<wp:meta_key><![CDATA[_lp_preview]]></wp:meta_key>
		<wp:meta_value><![CDATA[no]]></wp:meta_value>
		</wp:postmeta>
							<wp:postmeta>
		<wp:meta_key><![CDATA[_edit_last]]></wp:meta_key>
		<wp:meta_value><![CDATA[1]]></wp:meta_value>
		</wp:postmeta>
							</item>
					<item>
		<title><![CDATA[Chess Opening Gambit 2022 - Complete Chess Training Course]]></title>
		<link>https://socialv-wordpress.iqonic.design/lessons/chess-opening-gambit-2022-complete-chess-training-course/</link>
		<pubDate>Tue, 20 Sep 2022 06:16:17 +0000</pubDate>
		<dc:creator><![CDATA[jenny]]></dc:creator>
		<guid isPermaLink="false">https:/lessons/chess-opening-gambit-2022-complete-chess-training-course/</guid>
		<description></description>
		<content:encoded><![CDATA[<iframe width="560" height="315" src="https://www.youtube.com/embed/Wk48a1fHRCk" title="YouTube video player" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" allowfullscreen></iframe>]]></content:encoded>
		<excerpt:encoded><![CDATA[]]></excerpt:encoded>
		<wp:post_id>7116</wp:post_id>
		<wp:post_date><![CDATA[2022-09-20 06:16:17]]></wp:post_date>
		<wp:post_date_gmt><![CDATA[2022-09-20 06:16:17]]></wp:post_date_gmt>
		<wp:post_modified><![CDATA[2022-09-20 06:20:20]]></wp:post_modified>
		<wp:post_modified_gmt><![CDATA[2022-09-20 06:20:20]]></wp:post_modified_gmt>
		<wp:comment_status><![CDATA[open]]></wp:comment_status>
		<wp:ping_status><![CDATA[closed]]></wp:ping_status>
		<wp:post_name><![CDATA[chess-opening-gambit-2022-complete-chess-training-course]]></wp:post_name>
		<wp:status><![CDATA[publish]]></wp:status>
		<wp:post_parent>0</wp:post_parent>
		<wp:menu_order>0</wp:menu_order>
		<wp:post_type><![CDATA[lp_lesson]]></wp:post_type>
		<wp:post_password><![CDATA[]]></wp:post_password>
		<wp:is_sticky>0</wp:is_sticky>
														<wp:postmeta>
		<wp:meta_key><![CDATA[_lp_duration]]></wp:meta_key>
		<wp:meta_value><![CDATA[0 minute]]></wp:meta_value>
		</wp:postmeta>
							<wp:postmeta>
		<wp:meta_key><![CDATA[_lp_preview]]></wp:meta_key>
		<wp:meta_value><![CDATA[no]]></wp:meta_value>
		</wp:postmeta>
							<wp:postmeta>
		<wp:meta_key><![CDATA[_edit_last]]></wp:meta_key>
		<wp:meta_value><![CDATA[1]]></wp:meta_value>
		</wp:postmeta>
							<wp:postmeta>
		<wp:meta_key><![CDATA[_oembed_6e6e05f7c38d7e1e39c3f7cf0df64171]]></wp:meta_key>
		<wp:meta_value><![CDATA[<iframe title="SocialV - Live Style Customizer Demo" width="720" height="405" src="https://www.youtube.com/embed/1EF_Y6UOWsQ?feature=oembed" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" referrerpolicy="strict-origin-when-cross-origin" allowfullscreen></iframe>]]></wp:meta_value>
		</wp:postmeta>
							<wp:postmeta>
		<wp:meta_key><![CDATA[_oembed_time_6e6e05f7c38d7e1e39c3f7cf0df64171]]></wp:meta_key>
		<wp:meta_value><![CDATA[1715847102]]></wp:meta_value>
		</wp:postmeta>
							</item>
					<item>
		<title><![CDATA[Straight Lines & the Shapes They Make]]></title>
		<link>https://socialv-wordpress.iqonic.design/lessons/day-3-straight-lines-the-shapes-they-make/</link>
		<pubDate>Tue, 20 Sep 2022 06:28:23 +0000</pubDate>
		<dc:creator><![CDATA[jenny]]></dc:creator>
		<guid isPermaLink="false">https:/lessons/day-3-straight-lines-the-shapes-they-make/</guid>
		<description></description>
		<content:encoded><![CDATA[There are three different types of straight lines: (i) Horizontal lines: The lines drawn horizontally are called horizontal lines. (ii) Vertical lines: The lines drawn vertically are called vertical lines. (iii) Oblique or slanting lines: The lines drawn in a slanting position are called oblique or slanting lines.

What is a straight line class 2?
A straight line is just a line with no curves. So, a line that extends to both sides till infinity and has no curves is called a straight line.

What are the 4 types of straight lines?
In Four Basic Kinds of Straight Lines, Sol LeWitt focuses on four simple variations of straight lines: vertical, horizontal, diagonal left to right and diagonal right to left, and the possible groupings of each together.

What are the different types of lines and shapes?
Table of Contents:
Line. Point, Lines and Angles.
Line Segment.
Ray.
Types of lines. Horizontal Lines. Vertical Lines. Parallel Lines. Perpendicular Lines. Some other lines.
FAQs.


The different types of lines are as mentioned below:
Straight line.
Curved line.
Horizontal line.
Vertical line.
Parallel lines.
Intersecting lines.
Perpendicular lines.
Transversal line.

What are the 10 types of line?
Terms in this set (10)
straight.
curved.
dotted.
dashed.
contour.
variable.
zig zag.
implied.]]></content:encoded>
		<excerpt:encoded><![CDATA[]]></excerpt:encoded>
		<wp:post_id>7122</wp:post_id>
		<wp:post_date><![CDATA[2022-09-20 06:28:23]]></wp:post_date>
		<wp:post_date_gmt><![CDATA[2022-09-20 06:28:23]]></wp:post_date_gmt>
		<wp:post_modified><![CDATA[2022-09-20 06:31:47]]></wp:post_modified>
		<wp:post_modified_gmt><![CDATA[2022-09-20 06:31:47]]></wp:post_modified_gmt>
		<wp:comment_status><![CDATA[open]]></wp:comment_status>
		<wp:ping_status><![CDATA[closed]]></wp:ping_status>
		<wp:post_name><![CDATA[day-3-straight-lines-the-shapes-they-make]]></wp:post_name>
		<wp:status><![CDATA[publish]]></wp:status>
		<wp:post_parent>0</wp:post_parent>
		<wp:menu_order>0</wp:menu_order>
		<wp:post_type><![CDATA[lp_lesson]]></wp:post_type>
		<wp:post_password><![CDATA[]]></wp:post_password>
		<wp:is_sticky>0</wp:is_sticky>
														<wp:postmeta>
		<wp:meta_key><![CDATA[_lp_duration]]></wp:meta_key>
		<wp:meta_value><![CDATA[0 minute]]></wp:meta_value>
		</wp:postmeta>
							<wp:postmeta>
		<wp:meta_key><![CDATA[_edit_last]]></wp:meta_key>
		<wp:meta_value><![CDATA[1]]></wp:meta_value>
		</wp:postmeta>
							</item>
					<item>
		<title><![CDATA[Demonstration Drawings]]></title>
		<link>https://socialv-wordpress.iqonic.design/lessons/demonstration-drawings/</link>
		<pubDate>Tue, 20 Sep 2022 06:34:46 +0000</pubDate>
		<dc:creator><![CDATA[jenny]]></dc:creator>
		<guid isPermaLink="false">https:/lessons/demonstration-drawings/</guid>
		<description></description>
		<content:encoded><![CDATA[Do you ever struggle with backgrounds and perspective?

Maybe you're not completely sure how or where to start? Your fundamentals are shaky, and could really use some instruction. Or you admire a well drawn scene, and wish you could capture that skill.



If that's you, Ed Foychuk's new course "How To Draw Comics | Perspective and Backgrounds" might just be just what you need to help you solve those s obstacles once and for all.



When it comes to drawing characters in comic books, animation, story boarding or concept design, understanding backgrounds is critical to drawing scenes that look accurate.



Every shape, every structure, every part of the scene is deserving of accuracy. Sure, you can bend and break rules at times, but you have to KNOW them before you do that.



It's tough, to say the least. But learning it is essential to mastering your illustration skills.

If you can hack the intricate, underlying rules that govern perspective and backgrounds, you'll be able to produce pieces that can convey any message you want to bring across. And story you need to be told.



Imagine, not being afraid of backgrounds, but actually ENJOYING them!



That's what this course aims to give you in 22 lessons, spanning over 6 and a half hours of intensive Perspective training. When you get this course you'll watch example after example as to how each stage is deconstructed, then reconstructed.



Each demonstration is specifically designed for you to follow along with, with easy to follow steps that clearly show you how to give the basic foundations for your background construction..





How To Draw Comics | Perspective and Backgrounds covers:

1 Point Perspective

2 Point Perspective

3 Point Perspective

4 Point Perspective

5 Point Perspective

Changing Vanishing Points and Varying Horizon Lines

Digital Tips and Tricks

Basic Background Rules

and many Bonus Units



Perspective and backgrounds is often an area that many artists overlook. It's one they think they can skip over... until they realize they can't. So whether you're a beginner, or further along on your artistic path, if you're looking to improve your craft, this is the course for you.]]></content:encoded>
		<excerpt:encoded><![CDATA[]]></excerpt:encoded>
		<wp:post_id>7125</wp:post_id>
		<wp:post_date><![CDATA[2022-09-20 06:34:46]]></wp:post_date>
		<wp:post_date_gmt><![CDATA[2022-09-20 06:34:46]]></wp:post_date_gmt>
		<wp:post_modified><![CDATA[2022-09-20 06:35:00]]></wp:post_modified>
		<wp:post_modified_gmt><![CDATA[2022-09-20 06:35:00]]></wp:post_modified_gmt>
		<wp:comment_status><![CDATA[open]]></wp:comment_status>
		<wp:ping_status><![CDATA[closed]]></wp:ping_status>
		<wp:post_name><![CDATA[demonstration-drawings]]></wp:post_name>
		<wp:status><![CDATA[publish]]></wp:status>
		<wp:post_parent>0</wp:post_parent>
		<wp:menu_order>0</wp:menu_order>
		<wp:post_type><![CDATA[lp_lesson]]></wp:post_type>
		<wp:post_password><![CDATA[]]></wp:post_password>
		<wp:is_sticky>0</wp:is_sticky>
														<wp:postmeta>
		<wp:meta_key><![CDATA[_lp_duration]]></wp:meta_key>
		<wp:meta_value><![CDATA[0 minute]]></wp:meta_value>
		</wp:postmeta>
							<wp:postmeta>
		<wp:meta_key><![CDATA[_lp_preview]]></wp:meta_key>
		<wp:meta_value><![CDATA[no]]></wp:meta_value>
		</wp:postmeta>
							<wp:postmeta>
		<wp:meta_key><![CDATA[_edit_last]]></wp:meta_key>
		<wp:meta_value><![CDATA[1]]></wp:meta_value>
		</wp:postmeta>
							</item>
					<item>
		<title><![CDATA[Important Notes on Tuning]]></title>
		<link>https://socialv-wordpress.iqonic.design/lessons/important-notes-on-tuning/</link>
		<pubDate>Tue, 20 Sep 2022 06:43:49 +0000</pubDate>
		<dc:creator><![CDATA[jenny]]></dc:creator>
		<guid isPermaLink="false">https:/lessons/important-notes-on-tuning/</guid>
		<description></description>
		<content:encoded><![CDATA[BASICS OF GUITAR TUNING
First, let’s start with some of the basic parts of how to tune a guitar. Guitar tuning is controlled by the tuning pegs on the headstock of the guitar. Turning the pegs changes the pitch of the strings up or down. Tightening the strings makes the pitch go up; loosening the strings makes the pitch go down.

HOW OFTEN SHOULD YOU TUNE YOUR GUITAR?
You should tune your guitar every time you play it. You can’t expect your guitar to stay in tune between practice sessions. Guitars also go out tune from playing, especially if you are bending strings or playing for extended periods of time. As you are playing, it is a good idea to check your guitar tuning often. If you are playing a chord and it doesn’t sound quite right even when you know you are playing the correct notes, your guitar has gone out of tune. 

HOW TO TUNE YOUR GUITAR
Electronic tuners have made tuning guitar notes quick and easy.  You can also use a reference note (from another instrument, pitch pipe or tuning fork) to tune one of your strings and then tune “by ear”.]]></content:encoded>
		<excerpt:encoded><![CDATA[]]></excerpt:encoded>
		<wp:post_id>7132</wp:post_id>
		<wp:post_date><![CDATA[2022-09-20 06:43:49]]></wp:post_date>
		<wp:post_date_gmt><![CDATA[2022-09-20 06:43:49]]></wp:post_date_gmt>
		<wp:post_modified><![CDATA[2022-09-20 06:44:54]]></wp:post_modified>
		<wp:post_modified_gmt><![CDATA[2022-09-20 06:44:54]]></wp:post_modified_gmt>
		<wp:comment_status><![CDATA[open]]></wp:comment_status>
		<wp:ping_status><![CDATA[closed]]></wp:ping_status>
		<wp:post_name><![CDATA[important-notes-on-tuning]]></wp:post_name>
		<wp:status><![CDATA[publish]]></wp:status>
		<wp:post_parent>0</wp:post_parent>
		<wp:menu_order>0</wp:menu_order>
		<wp:post_type><![CDATA[lp_lesson]]></wp:post_type>
		<wp:post_password><![CDATA[]]></wp:post_password>
		<wp:is_sticky>0</wp:is_sticky>
														<wp:postmeta>
		<wp:meta_key><![CDATA[_lp_duration]]></wp:meta_key>
		<wp:meta_value><![CDATA[0 minute]]></wp:meta_value>
		</wp:postmeta>
							<wp:postmeta>
		<wp:meta_key><![CDATA[_lp_preview]]></wp:meta_key>
		<wp:meta_value><![CDATA[no]]></wp:meta_value>
		</wp:postmeta>
							<wp:postmeta>
		<wp:meta_key><![CDATA[_edit_last]]></wp:meta_key>
		<wp:meta_value><![CDATA[1]]></wp:meta_value>
		</wp:postmeta>
							</item>
					<item>
		<title><![CDATA[About me - A quick hello]]></title>
		<link>https://socialv-wordpress.iqonic.design/lessons/about-me-a-quick-hello/</link>
		<pubDate>Tue, 20 Sep 2022 06:45:35 +0000</pubDate>
		<dc:creator><![CDATA[jenny]]></dc:creator>
		<guid isPermaLink="false">https:/lessons/about-me-a-quick-hello/</guid>
		<description></description>
		<content:encoded><![CDATA[<iframe width="560" height="315" src="https://www.youtube.com/embed/A_m3k26Ul5E" title="YouTube video player" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" allowfullscreen></iframe>]]></content:encoded>
		<excerpt:encoded><![CDATA[]]></excerpt:encoded>
		<wp:post_id>7134</wp:post_id>
		<wp:post_date><![CDATA[2022-09-20 06:45:35]]></wp:post_date>
		<wp:post_date_gmt><![CDATA[2022-09-20 06:45:35]]></wp:post_date_gmt>
		<wp:post_modified><![CDATA[2022-09-20 06:46:14]]></wp:post_modified>
		<wp:post_modified_gmt><![CDATA[2022-09-20 06:46:14]]></wp:post_modified_gmt>
		<wp:comment_status><![CDATA[open]]></wp:comment_status>
		<wp:ping_status><![CDATA[closed]]></wp:ping_status>
		<wp:post_name><![CDATA[about-me-a-quick-hello]]></wp:post_name>
		<wp:status><![CDATA[publish]]></wp:status>
		<wp:post_parent>0</wp:post_parent>
		<wp:menu_order>0</wp:menu_order>
		<wp:post_type><![CDATA[lp_lesson]]></wp:post_type>
		<wp:post_password><![CDATA[]]></wp:post_password>
		<wp:is_sticky>0</wp:is_sticky>
														<wp:postmeta>
		<wp:meta_key><![CDATA[_lp_duration]]></wp:meta_key>
		<wp:meta_value><![CDATA[0 minute]]></wp:meta_value>
		</wp:postmeta>
							<wp:postmeta>
		<wp:meta_key><![CDATA[_lp_preview]]></wp:meta_key>
		<wp:meta_value><![CDATA[no]]></wp:meta_value>
		</wp:postmeta>
							<wp:postmeta>
		<wp:meta_key><![CDATA[_edit_last]]></wp:meta_key>
		<wp:meta_value><![CDATA[1]]></wp:meta_value>
		</wp:postmeta>
							</item>
					<item>
		<title><![CDATA[How to Make Yourself Indispensable as a Bassist]]></title>
		<link>https://socialv-wordpress.iqonic.design/lessons/how-to-make-yourself-indispensable-as-a-bassist/</link>
		<pubDate>Tue, 20 Sep 2022 06:46:18 +0000</pubDate>
		<dc:creator><![CDATA[jenny]]></dc:creator>
		<guid isPermaLink="false">https:/lessons/how-to-make-yourself-indispensable-as-a-bassist/</guid>
		<description></description>
		<content:encoded><![CDATA[Four Ways to Make Yourself Indispensable as a Bassist
In our ever-expanding search to bring value to the settings we choose to contribute to, here are 4 ways to make yourself indispensable as a bassist and contribute on multiple fronts.  These are the elements I’ve seen over 25 years that have helped bassists succeed, be in demand, and thrive.

1. Sing (even just a little)

This may be the element that I have seen separate more bassists in auditions, in the studio, or on stage than any other.  Bassists who can sing often get gigs over those who can’t.  And it’s not a matter of singling lead (more power to you if you can) it’s about providing solid backup vocals.

This doesn’t require a great voice, but it does require a few skills: producing a decent tone, singing harmony (so much fun) and blending (getting your vocal sound to fit in smoothly with other voices).  These can all be accomplished with practice, the development of vocal confidence, and guidance.

What can you do to add this tremendous skill?

Pick a few songs and learn to sing and play them simultaneously.  If you’ve never done this before it can take time to develop the coordination to play a bass line while singing. If you take the time, it’s worth it!

It’s also never been easier to find vocal coaching as there are some well-designed courses online including The Perfect Voice and Superior Singing Method. You can also find some insightful free lessons from Felicia Ricci.

2. Bring Multi-Instrumental Skills

One of the most enjoyable and beneficial ways I’ve grown as a bassist and as musician is through learning new instruments.  For me, this process isn’t about mastery, it’s really about perspective.  When I pick up a new instrument I get to see music from a slightly different perspective: like Picasso taking in multiple simultaneous perspectives we can see a song in a different way, in a new way. 

And this, my friends, leads to being a better bassist.  We expand our understanding of music and of a song by understanding how others see it.  This new sight increases also our understanding of how to contribute better bass parts since we know what others need.  And, we develop a clearer understanding of our role and ultimately, if we choose, of how to both fulfill and challenge that role.

You can get expansive music training online offering professional instruction on a wide variety of instruments from bass to cello to flute and more at Artistworks, Inc.

Piano for All is one of the leading online piano courses and they are song-focused so you can jump into playing songs.

One of the highest quality online platforms for learning guitar, bass, and even ukulele is Fender’s new Fender Play.

Being a bass player, we have serious skills that can be transferred over to other instruments.  And if we learn the basics of another instrument this often offers wonderful returns that help us grow as bassists and as musicians.  Even learning to play just a few songs on a new instrument can get you a long way.

3. Learn a Little About How Drummers Think

Bassists and drummers form the core of the rhythm section in many music forms. Knowing how our musical siblings approach music can be tremendously beneficial to our own ability to communicate and lock in with drummers by better understanding their unique perspective on music.

If you’re interested in learning more about the other half of the rhythm section, two solid free resources are youtube channels for Drumeo and 180 Drums.

4. Always Be Learning

Bass players are the salt of the earth.  We are the binding force in a band in more ways than the obvious one as we are the bridge between rhythm and harmony.  We have one foot placed squarely in the rhythm section and the other in the harmony section.  (Well done us!)

In our efforts to always be learning, as is our way, there are some high-quality resources available.  Three excellent resources available online include the Fender Play program, Artistworks, and, yes, my own course, The Professional Bass Masterclass (of which I’m very proud).

With some extra work, we can develop the skills to contribute in ways that help us better understand our own instrument and express ourselves even more fully as well-rounded contributors to the song, to the sound, and to the band.

]]></content:encoded>
		<excerpt:encoded><![CDATA[]]></excerpt:encoded>
		<wp:post_id>7136</wp:post_id>
		<wp:post_date><![CDATA[2022-09-20 06:46:18]]></wp:post_date>
		<wp:post_date_gmt><![CDATA[2022-09-20 06:46:18]]></wp:post_date_gmt>
		<wp:post_modified><![CDATA[2022-09-20 06:47:49]]></wp:post_modified>
		<wp:post_modified_gmt><![CDATA[2022-09-20 06:47:49]]></wp:post_modified_gmt>
		<wp:comment_status><![CDATA[open]]></wp:comment_status>
		<wp:ping_status><![CDATA[closed]]></wp:ping_status>
		<wp:post_name><![CDATA[how-to-make-yourself-indispensable-as-a-bassist]]></wp:post_name>
		<wp:status><![CDATA[publish]]></wp:status>
		<wp:post_parent>0</wp:post_parent>
		<wp:menu_order>0</wp:menu_order>
		<wp:post_type><![CDATA[lp_lesson]]></wp:post_type>
		<wp:post_password><![CDATA[]]></wp:post_password>
		<wp:is_sticky>0</wp:is_sticky>
														<wp:postmeta>
		<wp:meta_key><![CDATA[_lp_duration]]></wp:meta_key>
		<wp:meta_value><![CDATA[0 minute]]></wp:meta_value>
		</wp:postmeta>
							<wp:postmeta>
		<wp:meta_key><![CDATA[_lp_preview]]></wp:meta_key>
		<wp:meta_value><![CDATA[no]]></wp:meta_value>
		</wp:postmeta>
							<wp:postmeta>
		<wp:meta_key><![CDATA[_edit_last]]></wp:meta_key>
		<wp:meta_value><![CDATA[1]]></wp:meta_value>
		</wp:postmeta>
							</item>
					<item>
		<title><![CDATA[How To's - Cutting Onions]]></title>
		<link>https://socialv-wordpress.iqonic.design/lessons/how-tos-cutting-onions/</link>
		<pubDate>Tue, 20 Sep 2022 06:48:03 +0000</pubDate>
		<dc:creator><![CDATA[jenny]]></dc:creator>
		<guid isPermaLink="false">https:/lessons/how-tos-cutting-onions/</guid>
		<description></description>
		<content:encoded><![CDATA[<iframe width="560" height="315" src="https://www.youtube.com/embed/GUf8E94i5RI" title="YouTube video player" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" allowfullscreen></iframe>]]></content:encoded>
		<excerpt:encoded><![CDATA[]]></excerpt:encoded>
		<wp:post_id>7138</wp:post_id>
		<wp:post_date><![CDATA[2022-09-20 06:48:03]]></wp:post_date>
		<wp:post_date_gmt><![CDATA[2022-09-20 06:48:03]]></wp:post_date_gmt>
		<wp:post_modified><![CDATA[2022-09-20 06:49:58]]></wp:post_modified>
		<wp:post_modified_gmt><![CDATA[2022-09-20 06:49:58]]></wp:post_modified_gmt>
		<wp:comment_status><![CDATA[open]]></wp:comment_status>
		<wp:ping_status><![CDATA[closed]]></wp:ping_status>
		<wp:post_name><![CDATA[how-tos-cutting-onions]]></wp:post_name>
		<wp:status><![CDATA[publish]]></wp:status>
		<wp:post_parent>0</wp:post_parent>
		<wp:menu_order>0</wp:menu_order>
		<wp:post_type><![CDATA[lp_lesson]]></wp:post_type>
		<wp:post_password><![CDATA[]]></wp:post_password>
		<wp:is_sticky>0</wp:is_sticky>
														<wp:postmeta>
		<wp:meta_key><![CDATA[_lp_duration]]></wp:meta_key>
		<wp:meta_value><![CDATA[0 minute]]></wp:meta_value>
		</wp:postmeta>
							<wp:postmeta>
		<wp:meta_key><![CDATA[_lp_preview]]></wp:meta_key>
		<wp:meta_value><![CDATA[no]]></wp:meta_value>
		</wp:postmeta>
							<wp:postmeta>
		<wp:meta_key><![CDATA[_edit_last]]></wp:meta_key>
		<wp:meta_value><![CDATA[1]]></wp:meta_value>
		</wp:postmeta>
							</item>
					<item>
		<title><![CDATA[How to make Ginger Garlic Paste]]></title>
		<link>https://socialv-wordpress.iqonic.design/lessons/how-to-make-ginger-garlic-paste/</link>
		<pubDate>Tue, 20 Sep 2022 06:48:23 +0000</pubDate>
		<dc:creator><![CDATA[jenny]]></dc:creator>
		<guid isPermaLink="false">https:/lessons/how-to-make-ginger-garlic-paste/</guid>
		<description></description>
		<content:encoded><![CDATA[<iframe width="560" height="315" src="https://www.youtube.com/embed/YAkvfTSzqkc" title="YouTube video player" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" allowfullscreen></iframe>]]></content:encoded>
		<excerpt:encoded><![CDATA[]]></excerpt:encoded>
		<wp:post_id>7139</wp:post_id>
		<wp:post_date><![CDATA[2022-09-20 06:48:23]]></wp:post_date>
		<wp:post_date_gmt><![CDATA[2022-09-20 06:48:23]]></wp:post_date_gmt>
		<wp:post_modified><![CDATA[2022-09-20 06:49:38]]></wp:post_modified>
		<wp:post_modified_gmt><![CDATA[2022-09-20 06:49:38]]></wp:post_modified_gmt>
		<wp:comment_status><![CDATA[open]]></wp:comment_status>
		<wp:ping_status><![CDATA[closed]]></wp:ping_status>
		<wp:post_name><![CDATA[how-to-make-ginger-garlic-paste]]></wp:post_name>
		<wp:status><![CDATA[publish]]></wp:status>
		<wp:post_parent>0</wp:post_parent>
		<wp:menu_order>0</wp:menu_order>
		<wp:post_type><![CDATA[lp_lesson]]></wp:post_type>
		<wp:post_password><![CDATA[]]></wp:post_password>
		<wp:is_sticky>0</wp:is_sticky>
														<wp:postmeta>
		<wp:meta_key><![CDATA[_lp_duration]]></wp:meta_key>
		<wp:meta_value><![CDATA[0 minute]]></wp:meta_value>
		</wp:postmeta>
							<wp:postmeta>
		<wp:meta_key><![CDATA[_lp_preview]]></wp:meta_key>
		<wp:meta_value><![CDATA[no]]></wp:meta_value>
		</wp:postmeta>
							<wp:postmeta>
		<wp:meta_key><![CDATA[_edit_last]]></wp:meta_key>
		<wp:meta_value><![CDATA[1]]></wp:meta_value>
		</wp:postmeta>
							</item>
					<item>
		<title><![CDATA[Making Yogurt at home]]></title>
		<link>https://socialv-wordpress.iqonic.design/lessons/making-yogurt-at-home/</link>
		<pubDate>Tue, 20 Sep 2022 06:48:40 +0000</pubDate>
		<dc:creator><![CDATA[jenny]]></dc:creator>
		<guid isPermaLink="false">https:/lessons/making-yogurt-at-home/</guid>
		<description></description>
		<content:encoded><![CDATA[<iframe width="560" height="315" src="https://www.youtube.com/embed/GDAgYWWfp2U" title="YouTube video player" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" allowfullscreen></iframe>]]></content:encoded>
		<excerpt:encoded><![CDATA[]]></excerpt:encoded>
		<wp:post_id>7140</wp:post_id>
		<wp:post_date><![CDATA[2022-09-20 06:48:40]]></wp:post_date>
		<wp:post_date_gmt><![CDATA[2022-09-20 06:48:40]]></wp:post_date_gmt>
		<wp:post_modified><![CDATA[2022-09-20 06:49:08]]></wp:post_modified>
		<wp:post_modified_gmt><![CDATA[2022-09-20 06:49:08]]></wp:post_modified_gmt>
		<wp:comment_status><![CDATA[open]]></wp:comment_status>
		<wp:ping_status><![CDATA[closed]]></wp:ping_status>
		<wp:post_name><![CDATA[making-yogurt-at-home]]></wp:post_name>
		<wp:status><![CDATA[publish]]></wp:status>
		<wp:post_parent>0</wp:post_parent>
		<wp:menu_order>0</wp:menu_order>
		<wp:post_type><![CDATA[lp_lesson]]></wp:post_type>
		<wp:post_password><![CDATA[]]></wp:post_password>
		<wp:is_sticky>0</wp:is_sticky>
														<wp:postmeta>
		<wp:meta_key><![CDATA[_lp_duration]]></wp:meta_key>
		<wp:meta_value><![CDATA[0 minute]]></wp:meta_value>
		</wp:postmeta>
							<wp:postmeta>
		<wp:meta_key><![CDATA[_lp_preview]]></wp:meta_key>
		<wp:meta_value><![CDATA[no]]></wp:meta_value>
		</wp:postmeta>
							<wp:postmeta>
		<wp:meta_key><![CDATA[_edit_last]]></wp:meta_key>
		<wp:meta_value><![CDATA[1]]></wp:meta_value>
		</wp:postmeta>
							<wp:postmeta>
		<wp:meta_key><![CDATA[_oembed_6e6e05f7c38d7e1e39c3f7cf0df64171]]></wp:meta_key>
		<wp:meta_value><![CDATA[<iframe title="SocialV - Live Style Customizer Demo" width="720" height="405" src="https://www.youtube.com/embed/1EF_Y6UOWsQ?feature=oembed" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" referrerpolicy="strict-origin-when-cross-origin" allowfullscreen></iframe>]]></wp:meta_value>
		</wp:postmeta>
							<wp:postmeta>
		<wp:meta_key><![CDATA[_oembed_time_6e6e05f7c38d7e1e39c3f7cf0df64171]]></wp:meta_key>
		<wp:meta_value><![CDATA[1721140542]]></wp:meta_value>
		</wp:postmeta>
							</item>
					<item>
		<title><![CDATA[Preparing for your first meditation]]></title>
		<link>https://socialv-wordpress.iqonic.design/lessons/preparing-for-a-meditation/</link>
		<pubDate>Tue, 20 Sep 2022 06:53:23 +0000</pubDate>
		<dc:creator><![CDATA[jenny]]></dc:creator>
		<guid isPermaLink="false">https:/lessons/preparing-for-a-meditation/</guid>
		<description></description>
		<content:encoded><![CDATA[Are you feeling that urge to get away from it all? Longing for a reset? Do you have an interest in beginning or deepening your meditation practice? If your answer to any of those questions is yes, you may be considering attending a meditation retreat. A meditation retreat is a vacation during which you dedicate some, most, or all of your time to your meditation practice. You may find guided group sessions, informal sittings, educational talks, mindful practices (such as silence, mindful eating, or mindful walking), and/or adventurous excursions. There are as many styles of meditation retreats as there are styles of meditation (there are a lot!) and it’s worth doing some research to see what you’re in for.

Learn to Stay
On a retreat you will find inspiration in the location, accommodations, food, professional guidance, and activities offered. Whatever style retreat you choose, you can expect to learn, experience, and practice what American Buddhist Pema Chodron calls the art of “learning to stay.” In The Places That Scare You, Chodron says, “In meditation we discover our inherent restlessness. Sometimes we get up and leave. Sometimes we sit there for long periods of time but our bodies wiggle and squirm and our minds go far away. This feeling can be so uncomfortable and challenging that we feel it’s impossible to stay. Yet this feeling can teach us not just about ourselves but also about what it is to be human.”

Benefits of Meditation
Whether you are a long-time practitioner or are interested in trying that oft-lauded hippie route to mellow out, surely you’ve heard the many benefits of meditation. Science backs what devotees have known for centuries: meditation is good for you! Meditation is the practice of sitting still and breathing mindfully to calm the body, mind, and spirit. There are many activities that can be meditative, though a formal meditation practice is characterized by finding a comfortable seat, allowing stillness, focusing on the breath or a mantra, and remaining there for a period of time. Some of the benefits of a regular meditation practice include:

Lowered anxiety
Depression
Pain
Positive changes in the neuroplasticity of the brain areas that regulate attention, emotion, and self-awareness
Benefits of a Meditation Retreat
So, even though it is possible to practice meditation entirely on your own (in fact, you don’t actually need anything or anyone in order to meditate—what’s stopping you?!), it can be helpful to be guided. It is valuable to have the inspiration and accountability of a group and a teacher. On a meditation retreat you will have the opportunity to put your phone away, immerse yourself in self-study, get out of your regular rut or routine, meet like-minded folks interested in personal growth, and learn insightful techniques that you can take home with you to bolster your personal practice. There are many reasons to head out on retreat and many potential benefits including:

Stress relief
Immersion in nature
Healthy and nourishing food
Long-term healing
Communal time
Solitude
Improved fitness]]></content:encoded>
		<excerpt:encoded><![CDATA[]]></excerpt:encoded>
		<wp:post_id>7144</wp:post_id>
		<wp:post_date><![CDATA[2022-09-20 06:53:23]]></wp:post_date>
		<wp:post_date_gmt><![CDATA[2022-09-20 06:53:23]]></wp:post_date_gmt>
		<wp:post_modified><![CDATA[2022-09-20 06:53:58]]></wp:post_modified>
		<wp:post_modified_gmt><![CDATA[2022-09-20 06:53:58]]></wp:post_modified_gmt>
		<wp:comment_status><![CDATA[open]]></wp:comment_status>
		<wp:ping_status><![CDATA[closed]]></wp:ping_status>
		<wp:post_name><![CDATA[preparing-for-a-meditation]]></wp:post_name>
		<wp:status><![CDATA[publish]]></wp:status>
		<wp:post_parent>0</wp:post_parent>
		<wp:menu_order>0</wp:menu_order>
		<wp:post_type><![CDATA[lp_lesson]]></wp:post_type>
		<wp:post_password><![CDATA[]]></wp:post_password>
		<wp:is_sticky>0</wp:is_sticky>
														<wp:postmeta>
		<wp:meta_key><![CDATA[_lp_duration]]></wp:meta_key>
		<wp:meta_value><![CDATA[0 minute]]></wp:meta_value>
		</wp:postmeta>
							<wp:postmeta>
		<wp:meta_key><![CDATA[_edit_last]]></wp:meta_key>
		<wp:meta_value><![CDATA[1]]></wp:meta_value>
		</wp:postmeta>
							<wp:postmeta>
		<wp:meta_key><![CDATA[_lp_preview]]></wp:meta_key>
		<wp:meta_value><![CDATA[no]]></wp:meta_value>
		</wp:postmeta>
							</item>
					<item>
		<title><![CDATA[Breath meditation and the basics]]></title>
		<link>https://socialv-wordpress.iqonic.design/lessons/breath-meditation-and-the-basics/</link>
		<pubDate>Tue, 20 Sep 2022 08:17:27 +0000</pubDate>
		<dc:creator><![CDATA[jenny]]></dc:creator>
		<guid isPermaLink="false">https:/lessons/breath-meditation-and-the-basics/</guid>
		<description></description>
		<content:encoded><![CDATA[The technique I'll be teaching is breath meditation. It's a good topic no matter what your religious background. As my teacher once said, the breath doesn't belong to Buddhism or Christianity or anyone at all. It's common property that anyone can meditate on. At the same time, of all the meditation topics there are, it's probably the most beneficial to the body, for when we're dealing with the breath, we're dealing not only with the air coming in and out of the lungs, but also with all the feelings of energy that course throughout the body with each breath. If you can learn to become sensitive to these feelings, and let them flow smoothly and unobstructed, you can help the body function more easily, and give the mind a handle for dealing with pain.

So let's all meditate for a few minutes. Sit comfortably erect, in a balanced position. You don't have to be ramrod straight like a soldier. Just try not to lean forward or back, to the left or the right. Close your eyes and say to yourself, 'May I be truly happy and free from suffering.' This may sound like a strange, even selfish, way to start meditating, but there are good reasons for it. One, if you can't wish for your own happiness, there is no way that you can honestly wish for the happiness of others. Some people need to remind themselves constantly that they deserve happiness — we all deserve it, but if we don't believe it, we will constantly find ways to punish ourselves, and we will end up punishing others in subtle or blatant ways as well.

Two, it's important to reflect on what true happiness is and where it can be found. A moment's reflection will show that you can't find it in the past or the future. The past is gone and your memory of it is undependable. The future is a blank uncertainty. So the only place we can really find happiness is in the present. But even here you have to know where to look. If you try to base your happiness on things that change — sights, sounds, sensations in general, people and things outside — you're setting yourself up for disappointment, like building your house on a cliff where there have been repeated landslides in the past. So true happiness has to be sought within. Meditation is thus like a treasure hunt: to find what has solid and unchanging worth in the mind, something that even death cannot touch.

To find this treasure we need tools. The first tool is to do what we're doing right now: to develop good will for ourselves. The second is to spread that good will to other living beings. Tell yourself: 'All living beings, no matter who they are, no matter what they have done to you in the past — may they all find true happiness too.' If you don't cultivate this thought, and instead carry grudges into your meditation, that's all you'll be able to see when you look inside.

Only when you have cleared the mind in this way, and set outside matters aside, are you ready to focus on the breath. Bring your attention to the sensation of breathing. Breathe in long and out long for a couple of times, focusing on any spot in the body where the breathing is easy to notice, and your mind feels comfortable focusing. This could be at the nose, at the chest, at the abdomen, or any spot at all. Stay with that spot, noticing how it feels as you breathe in and out. Don't force the breath, or bear down too heavily with your focus. Let the breath flow naturally, and simply keep track of how it feels. Savor it, as if it were an exquisite sensation you wanted to prolong. If your mind wanders off, simply bring it back. Don't get discouraged. If it wanders 100 times, bring it back 100 times. Show it that you mean business, and eventually it will listen to you.

If you want, you can experiment with different kinds of breathing. If long breathing feels comfortable, stick with it. If it doesn't, change it to whatever rhythm feels soothing to the body. You can try short breathing, fast breathing, slow breathing, deep breathing, shallow breathing — whatever feels most comfortable to you right now...

Once you have the breath comfortable at your chosen spot, move your attention to notice how the breathing feels in other parts of the body. Start by focusing on the area just below your navel. Breathe in and out, and notice how that area feels. If you don't feel any motion there, just be aware of the fact that there's no motion. If you do feel motion, notice the quality of the motion, to see if the breathing feels uneven there, or if there's any tension or tightness. If there's tension, think of relaxing it. If the breathing feels jagged or uneven, think of smoothing it out... Now move your attention over to the right of that spot — to the lower right-hand corner of the abdomen — and repeat the same process... Then over to the lower left-hand corner of the abdomen... Then up to the navel... right... left... to the solar plexus... right... left... the middle of the chest... right... left... to the base of the throat... right... left... to the middle of the head...[take several minutes for each spot]

If you were meditating at home, you could continue this process through your entire body — over the head, down the back, out the arms & legs to the tips of your finger & toes — but since our time is limited, I'll ask you to return your focus now to any one of the spots we've already covered. Let your attention settle comfortably there, and then let your conscious awareness spread to fill the entire body, from the head down to the toes, so that you're like a spider sitting in the middle of a web: It's sitting in one spot, but it's sensitive to the entire web. Keep your awareness expanded like this — you have to work at this, for its tendency will be to shrink to a single spot — and think of the breath coming in & out your entire body, through every pore. Let your awareness simply stay right there for a while — there's no where else you have to go, nothing else you have to think about... And then gently come out of meditation.]]></content:encoded>
		<excerpt:encoded><![CDATA[]]></excerpt:encoded>
		<wp:post_id>7147</wp:post_id>
		<wp:post_date><![CDATA[2022-09-20 08:17:27]]></wp:post_date>
		<wp:post_date_gmt><![CDATA[2022-09-20 08:17:27]]></wp:post_date_gmt>
		<wp:post_modified><![CDATA[2022-09-20 08:18:10]]></wp:post_modified>
		<wp:post_modified_gmt><![CDATA[2022-09-20 08:18:10]]></wp:post_modified_gmt>
		<wp:comment_status><![CDATA[open]]></wp:comment_status>
		<wp:ping_status><![CDATA[closed]]></wp:ping_status>
		<wp:post_name><![CDATA[breath-meditation-and-the-basics]]></wp:post_name>
		<wp:status><![CDATA[publish]]></wp:status>
		<wp:post_parent>0</wp:post_parent>
		<wp:menu_order>0</wp:menu_order>
		<wp:post_type><![CDATA[lp_lesson]]></wp:post_type>
		<wp:post_password><![CDATA[]]></wp:post_password>
		<wp:is_sticky>0</wp:is_sticky>
														<wp:postmeta>
		<wp:meta_key><![CDATA[_lp_duration]]></wp:meta_key>
		<wp:meta_value><![CDATA[0 minute]]></wp:meta_value>
		</wp:postmeta>
							<wp:postmeta>
		<wp:meta_key><![CDATA[_lp_preview]]></wp:meta_key>
		<wp:meta_value><![CDATA[no]]></wp:meta_value>
		</wp:postmeta>
							<wp:postmeta>
		<wp:meta_key><![CDATA[_edit_last]]></wp:meta_key>
		<wp:meta_value><![CDATA[1]]></wp:meta_value>
		</wp:postmeta>
							</item>
					<item>
		<title><![CDATA[Getting comfortable with Photoshop: Making a Poster]]></title>
		<link>https://socialv-wordpress.iqonic.design/lessons/getting-comfortable-with-photoshop-making-a-poster/</link>
		<pubDate>Tue, 20 Sep 2022 08:20:28 +0000</pubDate>
		<dc:creator><![CDATA[jenny]]></dc:creator>
		<guid isPermaLink="false">https:/lessons/getting-comfortable-with-photoshop-making-a-poster/</guid>
		<description></description>
		<content:encoded><![CDATA[Creating a poster can be done in Photoshop, Illustrator, InDesign, or even in all three.

Today with paragraph and character styles there may be little reason to leave Photoshop in the first place. The steps of designing still remain the same in Photoshop, the difference is that you will not need to learn how to navigate through a lot of extra applications to create a fun and engaging poster.

Follow this step-by-step tutorial on how to make a poster in Photoshop!
Figuring Out the Size, Color Mode, and Resolution

Canvases come in all sizes, resolutions, and color modes but determining your ideal canvas is dependent on the project you are going to start. It is important that you make the poster the size and resolution you need for output. Most posters are at these sizes: 22" x 28" , 24" x 36",  36" x 48”.

Photographs for print should be a minimum of 300 ppi for a high-end print job. For lower-end printing (office postscript printers) your resolution may be as low as 150 ppi. The resolution is based on the LPI of the printer so check before the project is started.

If you are not sure 300 ppi is the magic number. Our color mode will always start in RGB with usually a working color profile of sRGB. Sometimes based on the printing process, the RGB color mode may need to be converted to a CMYK color mode at the end, but we can always start in RGB and convert color modes later.

]]></content:encoded>
		<excerpt:encoded><![CDATA[]]></excerpt:encoded>
		<wp:post_id>7149</wp:post_id>
		<wp:post_date><![CDATA[2022-09-20 08:20:28]]></wp:post_date>
		<wp:post_date_gmt><![CDATA[2022-09-20 08:20:28]]></wp:post_date_gmt>
		<wp:post_modified><![CDATA[2022-09-20 08:24:54]]></wp:post_modified>
		<wp:post_modified_gmt><![CDATA[2022-09-20 08:24:54]]></wp:post_modified_gmt>
		<wp:comment_status><![CDATA[open]]></wp:comment_status>
		<wp:ping_status><![CDATA[closed]]></wp:ping_status>
		<wp:post_name><![CDATA[getting-comfortable-with-photoshop-making-a-poster]]></wp:post_name>
		<wp:status><![CDATA[publish]]></wp:status>
		<wp:post_parent>0</wp:post_parent>
		<wp:menu_order>0</wp:menu_order>
		<wp:post_type><![CDATA[lp_lesson]]></wp:post_type>
		<wp:post_password><![CDATA[]]></wp:post_password>
		<wp:is_sticky>0</wp:is_sticky>
														<wp:postmeta>
		<wp:meta_key><![CDATA[_lp_duration]]></wp:meta_key>
		<wp:meta_value><![CDATA[0 minute]]></wp:meta_value>
		</wp:postmeta>
							<wp:postmeta>
		<wp:meta_key><![CDATA[_lp_preview]]></wp:meta_key>
		<wp:meta_value><![CDATA[no]]></wp:meta_value>
		</wp:postmeta>
							<wp:postmeta>
		<wp:meta_key><![CDATA[_edit_last]]></wp:meta_key>
		<wp:meta_value><![CDATA[1]]></wp:meta_value>
		</wp:postmeta>
							</item>
					<item>
		<title><![CDATA[Create Custom Business Cards]]></title>
		<link>https://socialv-wordpress.iqonic.design/lessons/create-custom-business-cards/</link>
		<pubDate>Tue, 20 Sep 2022 08:25:44 +0000</pubDate>
		<dc:creator><![CDATA[jenny]]></dc:creator>
		<guid isPermaLink="false">https:/lessons/create-custom-business-cards/</guid>
		<description></description>
		<content:encoded><![CDATA[It uses the same design elements as the company’s website, letterheads, and even invoices.

Your business card should look like the rest of your company, because:

You’ll be able to create consistency
Your business will be instantly more recognizable
You’ll have an easier time building a brand
Go through your advertising materials and website to decide on the elements that would be useful for your business card design.

These elements usually include at least your business logo, the typography and color palette you’re using in other marketing materials.

Since your space will be limited, it’s no surprise that logos are the most common design elements used on business card designs.

They help to communicate what your company is all about at a glance.

Don’t have a logo yet? Use a free business logo generator to get ahead.

Step 4: Get creative with your business card design ????‍????
Grab your easels, notebooks, and drawing pads: it’s time to start designing business cards.

If you’re familiar with graphic design, go ahead and start a new file in Adobe InDesign.

No idea what that means? Don’t worry, we’ve got you covered.

Most design companies offering business card printing, like Canva, Spark, and Vistaprint, offer a free business card maker with ready-made business card templates.

Now, regardless of whether you use graphic design tools or a business card maker, pay attention to these things:

The layout. The logo, name, position, and contact information should be sized and positioned for maximum neatness and impact.
Your logo and business name. These details should be the business card’s main highlights. They have to be larger and carefully placed. Be sure to leave some empty space between the content as well so that the design doesn’t feel cramped.
Typography. When picking a typeface, choose two sans serif or serif font types since they’re clear and easy to read. One font can be used for the name and position, while the other one is for the contact details.
Colors. Don’t go crazy on the number of colors. Lots of different colors are not only distracting but also carry the risk of looking less professional. If you have a brand or style guide, use that when designing business cards – it will save you time.
Other graphic elements. You might also want to include some other, more decorative graphics on your business card design. Consider using icons for your phone number, email address, social media handles, and company address. But don’t go overboard: make sure these additional elements don’t divert too much attention away from the contact details.
Overall, in an appealing business card design, all the elements should complement each other.

When you’re happy with the result, convert the document to 300 dpi so that the print result is clear and sharp.

And if you’re using a business card maker tool, just hit the save button.]]></content:encoded>
		<excerpt:encoded><![CDATA[]]></excerpt:encoded>
		<wp:post_id>7153</wp:post_id>
		<wp:post_date><![CDATA[2022-09-20 08:25:44]]></wp:post_date>
		<wp:post_date_gmt><![CDATA[2022-09-20 08:25:44]]></wp:post_date_gmt>
		<wp:post_modified><![CDATA[2022-09-20 08:28:54]]></wp:post_modified>
		<wp:post_modified_gmt><![CDATA[2022-09-20 08:28:54]]></wp:post_modified_gmt>
		<wp:comment_status><![CDATA[open]]></wp:comment_status>
		<wp:ping_status><![CDATA[closed]]></wp:ping_status>
		<wp:post_name><![CDATA[create-custom-business-cards]]></wp:post_name>
		<wp:status><![CDATA[publish]]></wp:status>
		<wp:post_parent>0</wp:post_parent>
		<wp:menu_order>0</wp:menu_order>
		<wp:post_type><![CDATA[lp_lesson]]></wp:post_type>
		<wp:post_password><![CDATA[]]></wp:post_password>
		<wp:is_sticky>0</wp:is_sticky>
														<wp:postmeta>
		<wp:meta_key><![CDATA[_lp_duration]]></wp:meta_key>
		<wp:meta_value><![CDATA[0 minute]]></wp:meta_value>
		</wp:postmeta>
							<wp:postmeta>
		<wp:meta_key><![CDATA[_lp_preview]]></wp:meta_key>
		<wp:meta_value><![CDATA[no]]></wp:meta_value>
		</wp:postmeta>
							<wp:postmeta>
		<wp:meta_key><![CDATA[_edit_last]]></wp:meta_key>
		<wp:meta_value><![CDATA[1]]></wp:meta_value>
		</wp:postmeta>
							</item>
					<item>
		<title><![CDATA[Blanching Green Vegetables]]></title>
		<link>https://socialv-wordpress.iqonic.design/lessons/blanching-green-vegetables/</link>
		<pubDate>Tue, 20 Sep 2022 08:36:39 +0000</pubDate>
		<dc:creator><![CDATA[jenny]]></dc:creator>
		<guid isPermaLink="false">https:/lessons/blanching-green-vegetables/</guid>
		<description></description>
		<content:encoded><![CDATA[Green vegetables need to be blanched in boiling salted water in order to keep their crunchiness and color. The salt will stick the clorophile to the vegetables so that they mentain their natural and apetising color. After cooking them in simmering water, the green vegetables need to be plunged in ice cold water, so that they stop cooking immediately and mentain their appealling texture. This video will guide you through the entire process, step by step, so that you can easily learn how to blanch green vegetables.

<iframe width="560" height="315" src="https://www.youtube.com/embed/nH6OlbjGbaw" title="YouTube video player" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" allowfullscreen></iframe>]]></content:encoded>
		<excerpt:encoded><![CDATA[]]></excerpt:encoded>
		<wp:post_id>7155</wp:post_id>
		<wp:post_date><![CDATA[2022-09-20 08:36:39]]></wp:post_date>
		<wp:post_date_gmt><![CDATA[2022-09-20 08:36:39]]></wp:post_date_gmt>
		<wp:post_modified><![CDATA[2022-09-20 09:05:23]]></wp:post_modified>
		<wp:post_modified_gmt><![CDATA[2022-09-20 09:05:23]]></wp:post_modified_gmt>
		<wp:comment_status><![CDATA[open]]></wp:comment_status>
		<wp:ping_status><![CDATA[closed]]></wp:ping_status>
		<wp:post_name><![CDATA[blanching-green-vegetables]]></wp:post_name>
		<wp:status><![CDATA[publish]]></wp:status>
		<wp:post_parent>0</wp:post_parent>
		<wp:menu_order>0</wp:menu_order>
		<wp:post_type><![CDATA[lp_lesson]]></wp:post_type>
		<wp:post_password><![CDATA[]]></wp:post_password>
		<wp:is_sticky>0</wp:is_sticky>
														<wp:postmeta>
		<wp:meta_key><![CDATA[_lp_duration]]></wp:meta_key>
		<wp:meta_value><![CDATA[0 minute]]></wp:meta_value>
		</wp:postmeta>
							<wp:postmeta>
		<wp:meta_key><![CDATA[_lp_preview]]></wp:meta_key>
		<wp:meta_value><![CDATA[no]]></wp:meta_value>
		</wp:postmeta>
							<wp:postmeta>
		<wp:meta_key><![CDATA[_edit_last]]></wp:meta_key>
		<wp:meta_value><![CDATA[1]]></wp:meta_value>
		</wp:postmeta>
							</item>
					<item>
		<title><![CDATA[Data Types and More]]></title>
		<link>https://socialv-wordpress.iqonic.design/lessons/data-types-and-more/</link>
		<pubDate>Tue, 20 Sep 2022 08:36:46 +0000</pubDate>
		<dc:creator><![CDATA[jenny]]></dc:creator>
		<guid isPermaLink="false">https:/lessons/data-types-and-more/</guid>
		<description></description>
		<content:encoded><![CDATA[Data Types in PHP
The values assigned to a PHP variable may be of different data types including simple string and numeric types to more complex data types like arrays and objects.

PHP supports total eight primitive data types: Integer, Floating point number or Float, String, Booleans, Array, Object, resource and NULL. These data types are used to construct variables. Now let's discuss each one of them in detail.

PHP Integers
Integers are whole numbers, without a decimal point (..., -2, -1, 0, 1, 2, ...). Integers can be specified in decimal (base 10), hexadecimal (base 16 - prefixed with 0x) or octal (base 8 - prefixed with 0) notation, optionally preceded by a sign (- or +).
PHP Strings
Strings are sequences of characters, where every character is the same as a byte.

A string can hold letters, numbers, and special characters and it can be as large as up to 2GB (2147483647 bytes maximum). The simplest way to specify a string is to enclose it in single quotes (e.g. 'Hello world!'), however you can also use double quotes ("Hello world!").
PHP Floating Point Numbers or Doubles
Floating point numbers (also known as "floats", "doubles", or "real numbers") are decimal or fractional numbers, like demonstrated in the example below.
PHP Objects
An object is a data type that not only allows storing data but also information on, how to process that data. An object is a specific instance of a class which serve as templates for objects. Objects are created based on this template via the new keyword.

Every object has properties and methods corresponding to those of its parent class. Every object instance is completely independent, with its own properties and methods, and can thus be manipulated independently of other objects of the same class.
PHP NULL
The special NULL value is used to represent empty variables in PHP. A variable of type NULL is a variable without any data. NULL is the only possible value of type null.
When a variable is created without a value in PHP like $var; it is automatically assigned a value of null. Many novice PHP developers mistakenly considered both $var1 = NULL; and $var2 = ""; are same, but this is not true. Both variables are different — the $var1 has null value while $var2 indicates no value assigned to it.
PHP Resources
A resource is a special variable, holding a reference to an external resource.

Resource variables typically hold special handlers to opened files and database connections.







]]></content:encoded>
		<excerpt:encoded><![CDATA[]]></excerpt:encoded>
		<wp:post_id>7156</wp:post_id>
		<wp:post_date><![CDATA[2022-09-20 08:36:46]]></wp:post_date>
		<wp:post_date_gmt><![CDATA[2022-09-20 08:36:46]]></wp:post_date_gmt>
		<wp:post_modified><![CDATA[2022-09-20 08:39:51]]></wp:post_modified>
		<wp:post_modified_gmt><![CDATA[2022-09-20 08:39:51]]></wp:post_modified_gmt>
		<wp:comment_status><![CDATA[open]]></wp:comment_status>
		<wp:ping_status><![CDATA[closed]]></wp:ping_status>
		<wp:post_name><![CDATA[data-types-and-more]]></wp:post_name>
		<wp:status><![CDATA[publish]]></wp:status>
		<wp:post_parent>0</wp:post_parent>
		<wp:menu_order>0</wp:menu_order>
		<wp:post_type><![CDATA[lp_lesson]]></wp:post_type>
		<wp:post_password><![CDATA[]]></wp:post_password>
		<wp:is_sticky>0</wp:is_sticky>
														<wp:postmeta>
		<wp:meta_key><![CDATA[_lp_duration]]></wp:meta_key>
		<wp:meta_value><![CDATA[0 minute]]></wp:meta_value>
		</wp:postmeta>
							<wp:postmeta>
		<wp:meta_key><![CDATA[_lp_preview]]></wp:meta_key>
		<wp:meta_value><![CDATA[no]]></wp:meta_value>
		</wp:postmeta>
							<wp:postmeta>
		<wp:meta_key><![CDATA[_edit_last]]></wp:meta_key>
		<wp:meta_value><![CDATA[1]]></wp:meta_value>
		</wp:postmeta>
							</item>
					<item>
		<title><![CDATA[Blanching Root Vegetables]]></title>
		<link>https://socialv-wordpress.iqonic.design/lessons/blanching-root-vegetables/</link>
		<pubDate>Tue, 20 Sep 2022 08:37:01 +0000</pubDate>
		<dc:creator><![CDATA[jenny]]></dc:creator>
		<guid isPermaLink="false">https:/lessons/blanching-root-vegetables/</guid>
		<description></description>
		<content:encoded><![CDATA[Root vegetables need to be blanched from cold water, otherwise their outer layers would be cooked before the core. By the time the core cooks, the outer areas would be way overcooked. THis video will show you how to correctly blanch root vegetables in order to use them further, in more complicated dishes and formulas.

<iframe width="560" height="315" src="https://www.youtube.com/embed/ArD9LEH8zRY" title="YouTube video player" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" allowfullscreen></iframe>]]></content:encoded>
		<excerpt:encoded><![CDATA[]]></excerpt:encoded>
		<wp:post_id>7157</wp:post_id>
		<wp:post_date><![CDATA[2022-09-20 08:37:01]]></wp:post_date>
		<wp:post_date_gmt><![CDATA[2022-09-20 08:37:01]]></wp:post_date_gmt>
		<wp:post_modified><![CDATA[2022-09-20 09:04:45]]></wp:post_modified>
		<wp:post_modified_gmt><![CDATA[2022-09-20 09:04:45]]></wp:post_modified_gmt>
		<wp:comment_status><![CDATA[open]]></wp:comment_status>
		<wp:ping_status><![CDATA[closed]]></wp:ping_status>
		<wp:post_name><![CDATA[blanching-root-vegetables]]></wp:post_name>
		<wp:status><![CDATA[publish]]></wp:status>
		<wp:post_parent>0</wp:post_parent>
		<wp:menu_order>0</wp:menu_order>
		<wp:post_type><![CDATA[lp_lesson]]></wp:post_type>
		<wp:post_password><![CDATA[]]></wp:post_password>
		<wp:is_sticky>0</wp:is_sticky>
														<wp:postmeta>
		<wp:meta_key><![CDATA[_lp_duration]]></wp:meta_key>
		<wp:meta_value><![CDATA[0 minute]]></wp:meta_value>
		</wp:postmeta>
							<wp:postmeta>
		<wp:meta_key><![CDATA[_lp_preview]]></wp:meta_key>
		<wp:meta_value><![CDATA[no]]></wp:meta_value>
		</wp:postmeta>
							<wp:postmeta>
		<wp:meta_key><![CDATA[_edit_last]]></wp:meta_key>
		<wp:meta_value><![CDATA[1]]></wp:meta_value>
		</wp:postmeta>
							</item>
					<item>
		<title><![CDATA[Making Vegetable Stock]]></title>
		<link>https://socialv-wordpress.iqonic.design/lessons/making-vegetable-stock/</link>
		<pubDate>Tue, 20 Sep 2022 08:37:20 +0000</pubDate>
		<dc:creator><![CDATA[jenny]]></dc:creator>
		<guid isPermaLink="false">https:/lessons/making-vegetable-stock/</guid>
		<description></description>
		<content:encoded><![CDATA[Making stocks is one of the most important part of knowing how to cook. A basic vegetable stock is made of a definite range of vegetables, an aromatic bouquet and water. This video will show you how to make a correct vegetable stock respecting a few golden rules. Make clear and tasty vegetable stock without using cubes or other conentrates.


<iframe width="560" height="315" src="https://www.youtube.com/embed/ArD9LEH8zRY" title="YouTube video player" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" allowfullscreen></iframe>]]></content:encoded>
		<excerpt:encoded><![CDATA[]]></excerpt:encoded>
		<wp:post_id>7158</wp:post_id>
		<wp:post_date><![CDATA[2022-09-20 08:37:20]]></wp:post_date>
		<wp:post_date_gmt><![CDATA[2022-09-20 08:37:20]]></wp:post_date_gmt>
		<wp:post_modified><![CDATA[2022-09-20 09:03:45]]></wp:post_modified>
		<wp:post_modified_gmt><![CDATA[2022-09-20 09:03:45]]></wp:post_modified_gmt>
		<wp:comment_status><![CDATA[open]]></wp:comment_status>
		<wp:ping_status><![CDATA[closed]]></wp:ping_status>
		<wp:post_name><![CDATA[making-vegetable-stock]]></wp:post_name>
		<wp:status><![CDATA[publish]]></wp:status>
		<wp:post_parent>0</wp:post_parent>
		<wp:menu_order>0</wp:menu_order>
		<wp:post_type><![CDATA[lp_lesson]]></wp:post_type>
		<wp:post_password><![CDATA[]]></wp:post_password>
		<wp:is_sticky>0</wp:is_sticky>
														<wp:postmeta>
		<wp:meta_key><![CDATA[_lp_duration]]></wp:meta_key>
		<wp:meta_value><![CDATA[0 minute]]></wp:meta_value>
		</wp:postmeta>
							<wp:postmeta>
		<wp:meta_key><![CDATA[_lp_preview]]></wp:meta_key>
		<wp:meta_value><![CDATA[no]]></wp:meta_value>
		</wp:postmeta>
							<wp:postmeta>
		<wp:meta_key><![CDATA[_edit_last]]></wp:meta_key>
		<wp:meta_value><![CDATA[1]]></wp:meta_value>
		</wp:postmeta>
							</item>
					<item>
		<title><![CDATA[Object Oriented PHP Introduction]]></title>
		<link>https://socialv-wordpress.iqonic.design/lessons/object-oriented-php-introduction/</link>
		<pubDate>Tue, 20 Sep 2022 08:41:11 +0000</pubDate>
		<dc:creator><![CDATA[jenny]]></dc:creator>
		<guid isPermaLink="false">https:/lessons/object-oriented-php-introduction/</guid>
		<description></description>
		<content:encoded><![CDATA[Before we go in detail, lets define important terms related to Object Oriented Programming.

Class − This is a programmer-defined data type, which includes local functions as well as local data. You can think of a class as a template for making many instances of the same kind (or class) of object.

Object − An individual instance of the data structure defined by a class. You define a class once and then make many objects that belong to it. Objects are also known as instance.

Member Variable − These are the variables defined inside a class. This data will be invisible to the outside of the class and can be accessed via member functions. These variables are called attribute of the object once an object is created.

Member function − These are the function defined inside a class and are used to access object data.

Inheritance − When a class is defined by inheriting existing function of a parent class then it is called inheritance. Here child class will inherit all or few member functions and variables of a parent class.

Parent class − A class that is inherited from by another class. This is also called a base class or super class.

Child Class − A class that inherits from another class. This is also called a subclass or derived class.

Polymorphism − This is an object oriented concept where same function can be used for different purposes. For example function name will remain same but it take different number of arguments and can do different task.

Overloading − a type of polymorphism in which some or all of operators have different implementations depending on the types of their arguments. Similarly functions can also be overloaded with different implementation.

Data Abstraction − Any representation of data in which the implementation details are hidden (abstracted).

Encapsulation − refers to a concept where we encapsulate all the data and member functions together to form an object.

Constructor − refers to a special type of function which will be called automatically whenever there is an object formation from a class.

Destructor − refers to a special type of function which will be called automatically whenever an object is deleted or goes out of scope.]]></content:encoded>
		<excerpt:encoded><![CDATA[]]></excerpt:encoded>
		<wp:post_id>7162</wp:post_id>
		<wp:post_date><![CDATA[2022-09-20 08:41:11]]></wp:post_date>
		<wp:post_date_gmt><![CDATA[2022-09-20 08:41:11]]></wp:post_date_gmt>
		<wp:post_modified><![CDATA[2022-09-20 08:41:52]]></wp:post_modified>
		<wp:post_modified_gmt><![CDATA[2022-09-20 08:41:52]]></wp:post_modified_gmt>
		<wp:comment_status><![CDATA[open]]></wp:comment_status>
		<wp:ping_status><![CDATA[closed]]></wp:ping_status>
		<wp:post_name><![CDATA[object-oriented-php-introduction]]></wp:post_name>
		<wp:status><![CDATA[publish]]></wp:status>
		<wp:post_parent>0</wp:post_parent>
		<wp:menu_order>0</wp:menu_order>
		<wp:post_type><![CDATA[lp_lesson]]></wp:post_type>
		<wp:post_password><![CDATA[]]></wp:post_password>
		<wp:is_sticky>0</wp:is_sticky>
														<wp:postmeta>
		<wp:meta_key><![CDATA[_lp_duration]]></wp:meta_key>
		<wp:meta_value><![CDATA[0 minute]]></wp:meta_value>
		</wp:postmeta>
							<wp:postmeta>
		<wp:meta_key><![CDATA[_lp_preview]]></wp:meta_key>
		<wp:meta_value><![CDATA[no]]></wp:meta_value>
		</wp:postmeta>
							<wp:postmeta>
		<wp:meta_key><![CDATA[_edit_last]]></wp:meta_key>
		<wp:meta_value><![CDATA[1]]></wp:meta_value>
		</wp:postmeta>
							</item>
					<item>
		<title><![CDATA[Drawing Techniques to Draw and Sketch like a Pro]]></title>
		<link>https://socialv-wordpress.iqonic.design/lessons/drawing-techniques-to-draw-and-sketch-like-a-pro/</link>
		<pubDate>Tue, 20 Sep 2022 08:46:11 +0000</pubDate>
		<dc:creator><![CDATA[jenny]]></dc:creator>
		<guid isPermaLink="false">https:/lessons/drawing-techniques-to-draw-and-sketch-like-a-pro/</guid>
		<description></description>
		<content:encoded><![CDATA[Art is best learned by doing, so feel free to grab a piece of paper and follow along with these 17 drawing techniques. Add depth, contrast and your own unique style to your drawings by combining your favourite techniques.

All you need is a pencil, piece of paper or sketchbook, eraser and pencil sharpener. If you’re not sure about which are the best pencil supplies, skip to the last section.

By learning and improving your pencil drawing techniques, you can be more precise when rendering values in your artworks. Get creative with the marks you make and try out some new approaches to drawing.

Disclaimer: Fine Art Tutorials is a reader supported site. When you make purchases through links on this site, we may earn a small commission at no extra cost to you.
Layering
Layering pencil marks isn’t the same as layering paint. Graphite isn’t a transparent medium, so you can create a finished looking piece all in one layer. 

The concept of layering in drawing, refers to the process of starting with a light sketch then slowly building in the dark areas to create shadow. Most artists will use this process to plan out where the elements fit before committing to dark lines which are more difficult to erase. 

Underdrawing 
The underdrawing acts as the first layer of an artwork, it can form the first stage of the drawing process. This is especially true for pieces that require more accuracy or time investment. 

Draw a fine, light line to outline the main features of your subject. Then build on this layer afterwards with layers of shading. 

To improve the accuracy of your drawing, you could create a sketch on another piece of paper first, then transfer it with tracing paper or with the grid method.

Shading techniques
The following techniques of hatching, stippling and tonal sketching are all types of shading methods. These techniques will allow you to create values in your artwork. In art, a value is the relative lightness or darkness of a colour. So by using these different shading techniques, you can create highlights, shadows and give your drawing depth and form. ]]></content:encoded>
		<excerpt:encoded><![CDATA[]]></excerpt:encoded>
		<wp:post_id>7164</wp:post_id>
		<wp:post_date><![CDATA[2022-09-20 08:46:11]]></wp:post_date>
		<wp:post_date_gmt><![CDATA[2022-09-20 08:46:11]]></wp:post_date_gmt>
		<wp:post_modified><![CDATA[2022-09-20 08:47:37]]></wp:post_modified>
		<wp:post_modified_gmt><![CDATA[2022-09-20 08:47:37]]></wp:post_modified_gmt>
		<wp:comment_status><![CDATA[open]]></wp:comment_status>
		<wp:ping_status><![CDATA[closed]]></wp:ping_status>
		<wp:post_name><![CDATA[drawing-techniques-to-draw-and-sketch-like-a-pro]]></wp:post_name>
		<wp:status><![CDATA[publish]]></wp:status>
		<wp:post_parent>0</wp:post_parent>
		<wp:menu_order>0</wp:menu_order>
		<wp:post_type><![CDATA[lp_lesson]]></wp:post_type>
		<wp:post_password><![CDATA[]]></wp:post_password>
		<wp:is_sticky>0</wp:is_sticky>
														<wp:postmeta>
		<wp:meta_key><![CDATA[_lp_duration]]></wp:meta_key>
		<wp:meta_value><![CDATA[0 minute]]></wp:meta_value>
		</wp:postmeta>
							<wp:postmeta>
		<wp:meta_key><![CDATA[_lp_preview]]></wp:meta_key>
		<wp:meta_value><![CDATA[no]]></wp:meta_value>
		</wp:postmeta>
							<wp:postmeta>
		<wp:meta_key><![CDATA[_edit_last]]></wp:meta_key>
		<wp:meta_value><![CDATA[1]]></wp:meta_value>
		</wp:postmeta>
							</item>
					<item>
		<title><![CDATA[Oil Pastel Techniques]]></title>
		<link>https://socialv-wordpress.iqonic.design/lessons/oil-pastel-techniques/</link>
		<pubDate>Tue, 20 Sep 2022 08:52:47 +0000</pubDate>
		<dc:creator><![CDATA[jenny]]></dc:creator>
		<guid isPermaLink="false">https:/lessons/oil-pastel-techniques/</guid>
		<description></description>
		<content:encoded><![CDATA[What are oil pastels?
Oil pastels are a painting medium that has a stick or crayon form and combines a binder and pigments. As the binder is created with mineral oils and waxes mixed, you get a kind of a bit oily and soft stick. 

Pastel sticks and crayons are some of the other mediums that are comparable to oil pastel drawing. Comprising a non-drying binder and a pigment, oil pastels boast smooth consistency.

Meanwhile, wax crayons have wax consistency and pastel sticks are powdery. Where oil pastels blend seamlessly with other oil pastel colors, crayons fail to blend well. In comparison, pastel sticks mix greatly with themselves and other types of pastel.

Both oil pastels and pastel sticks boast color intensity, enhancing texture. They also can work on various surfaces. On the other hand, wax crayons give plain colors with not enough texture and are only used on paper. 

Further, there are various types of pastels, say pan pastels, hard pastels, and soft pastels. Oil pastels comprise an oil binder, so they are not as powdery as soft type. That is why blending them with a finger is harder. While you can layer oil pastels like soft ones, such is just to some degree. So consider this when you use oil pastels. Avoid applying too much material to the surface, keeping colors from being muddied.

Oil Pastel Techniques for Beginners
 
1. Sgraffito
Draw a couple of layers of pastels with contrasting colors. See to it that you use a dark color for the last layer. Scrape the design into the pastel layers using a paper clip or another sharp object.

2. Overlaying
Applying oil pastel layers helps attain a variety of color hues. First off, add pastel color layers. Next up, apply another color for another layer. Continue layering pastels until you accomplish the desired textured look. 

3. Scumbling
You add scribble marks over the surface. You can opt for two different colors or more. Draw scribble marks using one color of your choice. Repeat with another color. Overlap the various colors at a variety of points. Stop when you get the value and texture you wish.

4. Oil Blending
It is another preferable and simple way of drawing with oil pastel. Add colors next to one another. Then, you dip a brush into some oil to mix the colors.

5. Masking
It is another technique of oil pastel drawing for beginners. Tape where you do not intend to color and apply the pastels in the areas between the tape. When done, take the tape out.]]></content:encoded>
		<excerpt:encoded><![CDATA[]]></excerpt:encoded>
		<wp:post_id>7166</wp:post_id>
		<wp:post_date><![CDATA[2022-09-20 08:52:47]]></wp:post_date>
		<wp:post_date_gmt><![CDATA[2022-09-20 08:52:47]]></wp:post_date_gmt>
		<wp:post_modified><![CDATA[2022-09-20 08:54:56]]></wp:post_modified>
		<wp:post_modified_gmt><![CDATA[2022-09-20 08:54:56]]></wp:post_modified_gmt>
		<wp:comment_status><![CDATA[open]]></wp:comment_status>
		<wp:ping_status><![CDATA[closed]]></wp:ping_status>
		<wp:post_name><![CDATA[oil-pastel-techniques]]></wp:post_name>
		<wp:status><![CDATA[publish]]></wp:status>
		<wp:post_parent>0</wp:post_parent>
		<wp:menu_order>0</wp:menu_order>
		<wp:post_type><![CDATA[lp_lesson]]></wp:post_type>
		<wp:post_password><![CDATA[]]></wp:post_password>
		<wp:is_sticky>0</wp:is_sticky>
														<wp:postmeta>
		<wp:meta_key><![CDATA[_lp_duration]]></wp:meta_key>
		<wp:meta_value><![CDATA[0 minute]]></wp:meta_value>
		</wp:postmeta>
							<wp:postmeta>
		<wp:meta_key><![CDATA[_lp_preview]]></wp:meta_key>
		<wp:meta_value><![CDATA[no]]></wp:meta_value>
		</wp:postmeta>
							<wp:postmeta>
		<wp:meta_key><![CDATA[_edit_last]]></wp:meta_key>
		<wp:meta_value><![CDATA[1]]></wp:meta_value>
		</wp:postmeta>
							</item>
					<item>
		<title><![CDATA[DRAWING A PORTRAIT]]></title>
		<link>https://socialv-wordpress.iqonic.design/lessons/drawing-the-cueball/</link>
		<pubDate>Tue, 20 Sep 2022 08:59:12 +0000</pubDate>
		<dc:creator><![CDATA[jenny]]></dc:creator>
		<guid isPermaLink="false">https:/lessons/drawing-the-cueball/</guid>
		<description></description>
		<content:encoded><![CDATA[Self-portrait drawing can be a great way to practice sketching techniques as-let’s face-it-there is no shortage of material when you draw yourself! Sketching or drawing yourself.

Sketching or drawing can be a great way to learn a lot about sketching, shading and proportions. And, when you sketch, your pictures can be kinda funny. They don’t always represent a subject the same way.

But, making a sketch of yourself, whether you use a picture or look in a mirror, can teach you a lot about art and yourself. So, here are some tips on how to draw a self-portrait:

5 Tips to Improve Your Self-Portrait Drawing!
1. Start with a Light Sketch
Start with a light outline.

Everyone thinks they know the proportions of a face, but when you really study the human face, its easy to realize that the eyes are not near the top of the head. Instead, they are about half way between the crown of the head and your chin.  When you are learning how to draw a self-portrait, get the major features first. Sketch them lightly, to create a foundation so you can add in your details later. This way, if you make a mistake, you can easily erase it and it wont affect the minute details you will spend more time creating later on.

2. Add Shadows and Smudge Them
To make your self-portrait drawing look professionally done, add shadows.

You will want to go lightly here as well. To create realistic shadow effects, use a smudging tool or your finger to draw out the shadows. This will blend the medium you use whether it is graphite, charcoal, or pastel and your shadows will end up lighter and easier to clean up in case you make a mistake.

Lighting is important when finding shadows and one of the best ways to properly “light” yourself is to use top-lighting or lighting from above. This is something to take into consideration when you take the photo you will use to do your self-portrait or, during your set-up if you plan to use a mirror.

3. Do Your Hairline After You Outline
One of the trickier parts when learning how to draw a self-portrait is knowing when to do the hairline. Start with the head and work out the proper length. Fill it in with thick lines, then add shadowing and highlights as you go along. Outline first and sketch the hairline afterward.

4. Fine Details Come Last
After you have the outline and some of the shading done, it’s time for the fine details. 

Take some time filling in places in the eyes and lips, and also add some more shadowing to the neck so that your face doesn’t have the appearance of floating in the air. The finer details are the ones that really the portrait lend to the realistic quality of a self-portrait.

And, a little extra tip, (4.5) if you’ve used pencil, you can add some color at this point with colored pencils or even watercolors.
5.  Choose a good frame
If you like your portrait drawing, choose a great frame for it. A nice frame can complement your art nicely and highlight your sketching skills. A simple black frame is nice when using charcoal, or something made of light wood goes best with soft colors.

And if you ever get tired of drawing your own image, you can always get together with a friend and practice drawing portraits of each other. Or, do a portrait of yourself and have your friend do one of you as well. Don’t look while they are in progress, but afterwards, compare and contrast. Our perspectives can vary substantially from individual to individual.]]></content:encoded>
		<excerpt:encoded><![CDATA[]]></excerpt:encoded>
		<wp:post_id>7168</wp:post_id>
		<wp:post_date><![CDATA[2022-09-20 08:59:12]]></wp:post_date>
		<wp:post_date_gmt><![CDATA[2022-09-20 08:59:12]]></wp:post_date_gmt>
		<wp:post_modified><![CDATA[2022-09-20 09:03:57]]></wp:post_modified>
		<wp:post_modified_gmt><![CDATA[2022-09-20 09:03:57]]></wp:post_modified_gmt>
		<wp:comment_status><![CDATA[open]]></wp:comment_status>
		<wp:ping_status><![CDATA[closed]]></wp:ping_status>
		<wp:post_name><![CDATA[drawing-the-cueball]]></wp:post_name>
		<wp:status><![CDATA[publish]]></wp:status>
		<wp:post_parent>0</wp:post_parent>
		<wp:menu_order>0</wp:menu_order>
		<wp:post_type><![CDATA[lp_lesson]]></wp:post_type>
		<wp:post_password><![CDATA[]]></wp:post_password>
		<wp:is_sticky>0</wp:is_sticky>
														<wp:postmeta>
		<wp:meta_key><![CDATA[_lp_duration]]></wp:meta_key>
		<wp:meta_value><![CDATA[0 minute]]></wp:meta_value>
		</wp:postmeta>
							<wp:postmeta>
		<wp:meta_key><![CDATA[_edit_last]]></wp:meta_key>
		<wp:meta_value><![CDATA[1]]></wp:meta_value>
		</wp:postmeta>
							<wp:postmeta>
		<wp:meta_key><![CDATA[_lp_preview]]></wp:meta_key>
		<wp:meta_value><![CDATA[no]]></wp:meta_value>
		</wp:postmeta>
							</item>
					<item>
		<title><![CDATA[INTERIOR SHAPES]]></title>
		<link>https://socialv-wordpress.iqonic.design/lessons/drawing-eyes-exercise-c/</link>
		<pubDate>Tue, 20 Sep 2022 09:04:37 +0000</pubDate>
		<dc:creator><![CDATA[jenny]]></dc:creator>
		<guid isPermaLink="false">https:/lessons/drawing-eyes-exercise-c/</guid>
		<description></description>
		<content:encoded><![CDATA[Start making artwork
Discover how easy it is to build your artwork in Adobe Illustrator with simple vector shapes that you can reshape, combine, and color to make eye-catching illustrations.
Draw simple shapes
You can create a variety of primitive shapes with the vector Shape tools in Illustrator. Press and hold the Rectangle tool to view all the Shape tools, and then select the Rectangle tool. Drag on the artboard to draw a rectangle. As you drag, look for a diagonal magenta guide that shows a perfect square.
Draw any type of polygon
Press and hold the Rectangle tool in the Toolbar and select the Polygon tool. Click the artboard, and in the dialog box that opens, type the number of sides for your shape — for example, type 6 to create a hexagon.

You can always change your shape dynamically by dragging the side widget. Try creating a triangle.
It’s just as easy to adjust shapes with the shape tools
To move a shape, drag its center point.
To resize, drag any of the bounding box handles, or press Shift while dragging to constrain proportions.
To change proportions — for example, to create a tall, skinny triangle — drag a corner handle in any direction, adjust height with the top or bottom handles, and transform width with the side handles.
If you move your cursor away from any handle, you’ll see the rotate icon. Drag to rotate the shape freely.
Combine shapes creatively
You can also combine shapes to create new, more complex shapes in just a couple of clicks.

Draw some overlapping shapes. Switch to the Selection tool and drag a selection marquee around all the shapes to select them. Select the Shape Builder tool and drag a line through all the shapes you want to unite into one shape.
Break apart shapes
It’s just as easy to break apart and erase parts of overlapping shapes. Select all the shapes. With the Shape Builder tool, click any part you want to punch out as with a cookie cutter. Don’t worry if it still looks the same; you’ve divided them into separate pieces.

Press Option (MacOS) or Alt (Windows) and click or drag across any section to delete it. ]]></content:encoded>
		<excerpt:encoded><![CDATA[]]></excerpt:encoded>
		<wp:post_id>7172</wp:post_id>
		<wp:post_date><![CDATA[2022-09-20 09:04:37]]></wp:post_date>
		<wp:post_date_gmt><![CDATA[2022-09-20 09:04:37]]></wp:post_date_gmt>
		<wp:post_modified><![CDATA[2022-09-20 09:06:47]]></wp:post_modified>
		<wp:post_modified_gmt><![CDATA[2022-09-20 09:06:47]]></wp:post_modified_gmt>
		<wp:comment_status><![CDATA[open]]></wp:comment_status>
		<wp:ping_status><![CDATA[closed]]></wp:ping_status>
		<wp:post_name><![CDATA[drawing-eyes-exercise-c]]></wp:post_name>
		<wp:status><![CDATA[publish]]></wp:status>
		<wp:post_parent>0</wp:post_parent>
		<wp:menu_order>0</wp:menu_order>
		<wp:post_type><![CDATA[lp_lesson]]></wp:post_type>
		<wp:post_password><![CDATA[]]></wp:post_password>
		<wp:is_sticky>0</wp:is_sticky>
														<wp:postmeta>
		<wp:meta_key><![CDATA[_lp_duration]]></wp:meta_key>
		<wp:meta_value><![CDATA[0 minute]]></wp:meta_value>
		</wp:postmeta>
							<wp:postmeta>
		<wp:meta_key><![CDATA[_edit_last]]></wp:meta_key>
		<wp:meta_value><![CDATA[1]]></wp:meta_value>
		</wp:postmeta>
							<wp:postmeta>
		<wp:meta_key><![CDATA[_lp_preview]]></wp:meta_key>
		<wp:meta_value><![CDATA[no]]></wp:meta_value>
		</wp:postmeta>
							</item>
				</channel>
</rss>
	