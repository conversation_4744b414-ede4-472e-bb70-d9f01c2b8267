.mo-openid-app-icons>p{
	margin:0px !important;

}

.mo-openid-app-icons{
	display:block;
	margin:0 auto;
	/*display:inline !important;*/
}
.mo-openid-app-icons>a{
	box-shadow: inset 0 -1px 0 rgba(15, 15, 15, 0) !important;
}
.mo-openid-app-icons>code>a{

	box-shadow: inset 0 -1px 0 rgba(15, 15, 15, 0) !important;
}
.mo-openid-app-icons>a>img{
	box-shadow: 0 0 0 0px #fff !important;
}
.mo-openid-app-icons>code>a>img{
	box-shadow: 0 0 0 0px #fff !important;
}
.mo-openid-app-icons>.horizontal>a>img{
	box-shadow: 0 0 0 0px #fff !important;
}
.mo-openid-app-icons>.horizontal>a{
	box-shadow: inset 0 -1px 0 rgba(15, 15, 15, 0)!important;}

.mo-openid-app-icons>div>a.mo-openid-share-link>img.mo-openid-app-share-icons
{
	box-shadow:0 0 0 0px #fff !important;
}

.mo-openid-app-icons>div>a.mo-openid-share-link
{ box-shadow: inset 0 -1px 0 rgba(15, 15, 15, 0) !important;

}
aside.widget-area>section.widget_mo_openid_sharing_hor_wid>div.mo-openid-app-icons>div
{
	font-size:0	!important;
}
.mo-openid-app-icons>a>img{
	width:35px !important;
	height:35px !important;
	margin:8px 0px;
	display: inline !important;
}
.mo_btn-customtheme{
	color:#ffffff !important;
}
.mo_btn-custom-dec{
	text-decoration:none !important;
}
.custom-login-button{
	text-align:center !important;
	color:white !important;
	padding-top:8px !important;
	box-sizing: border-box !important;
}
.circle{
	border-radius: 999px !important;
}

.oval{
	border-radius: 4px !important;
}

.round{

	border-radius: 999px !important;
}

.roundededges{
	border-radius: 4px !important;
}
.mo_openid-login-button{
	text-align:center !important;
	color:white !important;
	padding-top:8px !important;
	box-sizing: border-box !important;

}
