@echo off
chcp 65001 >nul
echo ========================================
echo    SocialV WordPress 主题开发环境
echo ========================================
echo.

REM 检查 Docker Desktop 是否运行
echo 检查 Docker 环境...
docker ps >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Docker Desktop 正在运行
    echo.
    echo 选择启动方式:
    echo 1. 使用 Docker 环境 (推荐)
    echo 2. 使用本地 PHP 服务器
    echo 3. 退出
    echo.
    set /p choice="请选择 (1-3): "
    
    if "%choice%"=="1" goto docker_start
    if "%choice%"=="2" goto local_start
    if "%choice%"=="3" goto end
    goto invalid_choice
) else (
    echo ❌ Docker Desktop 未运行
    echo.
    echo 可用选项:
    echo 1. 启动 Docker Desktop 然后重新运行此脚本
    echo 2. 使用本地 PHP 服务器 (需要 PHP 环境)
    echo 3. 退出
    echo.
    set /p choice="请选择 (1-3): "
    
    if "%choice%"=="1" goto start_docker
    if "%choice%"=="2" goto local_start
    if "%choice%"=="3" goto end
    goto invalid_choice
)

:docker_start
echo.
echo 🐳 启动 Docker 开发环境...
cd socialv-dev
docker-compose up -d
if %errorlevel% equ 0 (
    echo.
    echo ✅ Docker 环境启动成功！
    echo.
    echo 📍 访问地址:
    echo    WordPress 站点: http://localhost:8080
    echo    管理后台: http://localhost:8080/wp-admin
    echo    phpMyAdmin: http://localhost:8081
    echo.
    echo 🔑 默认登录信息:
    echo    用户名: admin
    echo    密码: admin123
    echo.
    echo 💡 提示: 首次启动可能需要几分钟来下载镜像和初始化
    echo.
    pause
) else (
    echo ❌ Docker 启动失败，请检查 Docker Desktop 是否正常运行
    pause
)
goto end

:local_start
echo.
echo 🖥️  准备本地 PHP 开发环境...

REM 检查 PHP
php --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ PHP 未安装或未添加到系统 PATH
    echo.
    echo 解决方案:
    echo 1. 安装 XAMPP: https://www.apachefriends.org/
    echo 2. 安装 PHP: https://www.php.net/downloads
    echo 3. 使用 Laragon: https://laragon.org/
    echo.
    pause
    goto end
)

echo ✅ PHP 环境检查通过
echo.

REM 检查是否存在 WordPress
if not exist "local-server\wordpress\wp-config.php" (
    echo 📦 WordPress 未安装，正在准备...
    cd local-server
    call download-wordpress.bat
    copy wp-config.php wordpress\wp-config.php
    cd ..
)

REM 复制主题
echo 📋 复制 SocialV 主题...
if exist "socialv" (
    if not exist "local-server\wordpress\wp-content\themes\socialv" (
        mkdir "local-server\wordpress\wp-content\themes\socialv"
    )
    xcopy "socialv\*" "local-server\wordpress\wp-content\themes\socialv\" /E /I /Y >nul
    echo ✅ 主题复制完成
)

echo.
echo 🚀 启动本地开发服务器...
echo 📍 访问地址: http://localhost:8000
echo 💡 按 Ctrl+C 停止服务器
echo.
cd local-server\wordpress
start http://localhost:8000
php -S localhost:8000
goto end

:start_docker
echo.
echo 正在启动 Docker Desktop...
echo 请等待 Docker Desktop 完全启动后重新运行此脚本
start "" "C:\Program Files\Docker\Docker\Docker Desktop.exe"
timeout /t 3 >nul
goto end

:invalid_choice
echo.
echo ❌ 无效选择，请重新运行脚本
pause
goto end

:end
echo.
echo 感谢使用 SocialV 开发环境！
pause
