@echo off
echo ========================================
echo    SocialV WordPress Theme Development
echo ========================================
echo.

REM Check if Docker Desktop is running
echo Checking Docker environment...
docker ps >nul 2>&1
if %errorlevel% equ 0 (
    echo [OK] Docker Desktop is running
    echo.
    echo Choose startup method:
    echo 1. Use Docker environment (Recommended)
    echo 2. Use local PHP server
    echo 3. Exit
    echo.
    set /p choice="Please choose (1-3): "

    if "%choice%"=="1" goto docker_start
    if "%choice%"=="2" goto local_start
    if "%choice%"=="3" goto end
    goto invalid_choice
) else (
    echo [ERROR] Docker Desktop is not running
    echo.
    echo Available options:
    echo 1. Start Docker Desktop then re-run this script
    echo 2. Use local PHP server (requires PHP environment)
    echo 3. Exit
    echo.
    set /p choice="Please choose (1-3): "

    if "%choice%"=="1" goto start_docker
    if "%choice%"=="2" goto local_start
    if "%choice%"=="3" goto end
    goto invalid_choice
)

:docker_start
echo.
echo [INFO] Starting Docker development environment...
cd socialv-dev
docker-compose up -d
if %errorlevel% equ 0 (
    echo.
    echo [SUCCESS] Docker environment started successfully!
    echo.
    echo Access URLs:
    echo    WordPress site: http://localhost:8080
    echo    Admin panel: http://localhost:8080/wp-admin
    echo    phpMyAdmin: http://localhost:8081
    echo.
    echo Default login credentials:
    echo    Username: admin
    echo    Password: admin123
    echo.
    echo Note: First startup may take a few minutes to download images
    echo.
    pause
) else (
    echo [ERROR] Docker startup failed, please check Docker Desktop
    pause
)
goto end

:local_start
echo.
echo [INFO] Preparing local PHP development environment...

REM Check PHP
php --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] PHP not installed or not in system PATH
    echo.
    echo Solutions:
    echo 1. Install XAMPP: https://www.apachefriends.org/
    echo 2. Install PHP: https://www.php.net/downloads
    echo 3. Use Laragon: https://laragon.org/
    echo.
    pause
    goto end
)

echo [OK] PHP environment check passed
echo.

REM Check if WordPress exists
if not exist "local-server\wordpress\wp-config.php" (
    echo [INFO] WordPress not installed, preparing...
    cd local-server
    call download-wordpress.bat
    copy wp-config.php wordpress\wp-config.php
    cd ..
)

REM Copy theme
echo [INFO] Copying SocialV theme...
if exist "socialv" (
    if not exist "local-server\wordpress\wp-content\themes\socialv" (
        mkdir "local-server\wordpress\wp-content\themes\socialv"
    )
    xcopy "socialv\*" "local-server\wordpress\wp-content\themes\socialv\" /E /I /Y >nul
    echo [OK] Theme copied successfully
)

echo.
echo [INFO] Starting local development server...
echo Access URL: http://localhost:8000
echo Press Ctrl+C to stop server
echo.
cd local-server\wordpress
start http://localhost:8000
php -S localhost:8000
goto end

:start_docker
echo.
echo [INFO] Starting Docker Desktop...
echo Please wait for Docker Desktop to fully start, then re-run this script
start "" "C:\Program Files\Docker\Docker\Docker Desktop.exe"
timeout /t 3 >nul
goto end

:invalid_choice
echo.
echo [ERROR] Invalid choice, please re-run the script
pause
goto end

:end
echo.
echo Thank you for using SocialV development environment!
pause
