<?php

/**
 * SocialV\Utility\Custom_Helper\Helpers\Members class
 *
 * @package socialv
 */

namespace SocialV\Utility\Custom_Helper\Helpers;

use BP_Activity_Activity;
use BuddyPress_GIPHY;
use WP_Query;
use SocialV\Utility\Custom_Helper\Component;
use function SocialV\Utility\socialv;
use function add_action;
use Buddypress_Polls_Public;

class Activity extends Component
{
    public $activity_filters;
    public $activity_class;
    public $socialv_option;
    private $display_posts_style;

    public function __construct()
    {
        $this->socialv_option = get_option('socialv-options');
        $this->display_posts_style = (class_exists('Redux') && $this->socialv_option['display_activities_posts_style'] == 'list') ? true : false;
        $share_activity = (class_exists('Redux') && isset($this->socialv_option['is_socialv_enable_share_post'])) ? $this->socialv_option['is_socialv_enable_share_post'] : false;
        $share_on_activity = (class_exists('Redux') && isset($this->socialv_option['is_socialv_enable_share_post_on_activity'])) ? $this->socialv_option['is_socialv_enable_share_post_on_activity'] : false;

        // Gallery
        $this->activity_filters = [
            'activity_update',
            'mpp_media_upload',
            'activity',
            'new_avatar',
            'updated_profile',
            'joined_group',
            'friendship_accepted,friendship_created',
            'bbp_topic_create',
            'created_group',
            'group_details_updated',
            'activity_share',
        ];

        remove_action("bp_register_activity_actions", "sv_register_activity_actions");
        add_action('bp_register_activity_actions', [$this, 'socialv_register_activity_actions']);

        // Activity Share 
        add_action('socialv_share_activity', [$this, 'socialv_share_activity'], 10, 1);

        if (class_exists('mediapress')) {
            add_filter('wp_video_shortcode', [$this, 'socialv_remove_poster_image_from_video_shortcode'], 10, 5);

            // Remove Mediapress notification url 
            remove_filter('bp_activity_get_permalink', 'mpp_filter_activity_permalink', 10);
            remove_action('bp_after_activity_post_form', 'mpp_activity_upload_buttons');
            remove_action('bp_after_activity_post_form', 'mpp_activity_dropzone');
            add_filter('wp_video_extensions', [$this, "socialv_append_video_types"]);
            add_action('socialv_activity_upload_buttons', [$this, 'socialv_activity_upload_buttons']);
            add_action('socialv_activity_upload_dropzone', [$this, 'socialv_activity_upload_dropzone']);
            remove_action('bp_activity_entry_content', 'mpp_activity_inject_attached_media_html');
            add_action('bp_activity_entry_content', [$this, 'socialv_mpp_activity_inject_attached_media_html']);
        }

        remove_filter('gamipress_bp_activity_details', 'gamipress_bp_points_activity_details', 10, 6);
        remove_filter('gamipress_bp_activity_details', 'gamipress_bp_achievement_activity_details', 10, 6);
        remove_filter('gamipress_bp_activity_details', 'gamipress_bp_step_activity_details', 10, 6);
        remove_filter('gamipress_bp_activity_details', 'gamipress_bp_rank_activity_details', 10, 6);
        remove_filter('gamipress_bp_activity_details', 'gamipress_bp_rank_requirement_activity_details', 10, 6);

        // Gif Plugin
        if (class_exists('BuddyPress_GIPHY')) :
            remove_action('bp_activity_post_form_options', [BuddyPress_GIPHY::get_instance(), 'buddypress_giphy_post_gif_html'], 30);
            add_action('mpp_after_activity_upload_buttons', [$this, 'socialv_buddypress_giphy_post_gif_html'], 30);
        endif;

        // profile-update
        add_action('xprofile_updated_profile', [$this, "socialv_update_activity_action"], 10, 5);

        add_action("socialv_activity_header", [$this, "socialv_activity_header"]);

        //share option
        add_action("socialv_social_share", [$this, "socialv_social_share"]);

        if (($share_activity && $share_on_activity) == '1') {
            add_action("socialv_enable_share_post_on_activity", [$this, "socialv_enable_share_post_on_activity"]);

            //BuddyPress Activity Social Share
            if (class_exists('Buddypress_Share')) {
                add_action("bp_share_user_services", [$this, "socialv_enable_share_post_on_activity"]);
            }
        }

        if ($share_activity == '1') {
            $share_url_on_activity = (class_exists('Redux') && isset($this->socialv_option['socialv_copy_url'])) ? $this->socialv_option['socialv_copy_url'] : false;

            //share post url on activity page
            if ($share_url_on_activity == '1') {
                add_action("socialv_social_share", [$this, "socialv_enable_share_post_url_on_activity"]);
            }
        }

        add_filter('bp_activity_recurse_comments_start_ul', function ($ul) {
            return "<ul class='activity-comments'>";
        });

        add_filter('bp_after_has_activities_parse_args', [$this, 'socialv_friends_only_activity_args']);
        add_filter('bp_has_activities', [$this, "socialv_reverse_activity_comments"]);
        add_action('wp_ajax_socialv_user_activity_callback', [$this, 'socialv_user_activity_callback']);
        add_action('wp_ajax_nopriv_socialv_user_activity_callback', [$this, 'socialv_user_activity_callback']);
        add_action('wp_ajax_socialv_activity_liked_users', [$this, 'socialv_activity_liked_users']);
        add_action('wp_ajax_nopriv_socialv_activity_liked_users', [$this, 'socialv_activity_liked_users']);

        remove_action('wp_ajax_activity_mark_unfav', 'bp_legacy_theme_unmark_activity_favorite');
        remove_action('wp_ajax_nopriv_activity_mark_unfav', 'bp_legacy_theme_unmark_activity_favorite');
        add_action('wp_ajax_activity_mark_unfav', [$this, 'socialv_unmark_activity_favorite']);
        add_action('wp_ajax_nopriv_activity_mark_unfav', [$this, 'socialv_unmark_activity_favorite']);

        add_action("socialv_activity_like_users", [$this, "socialv_activity_like_users"]);

        add_filter("bp_activity_content_before_save", function ($content, $obj) {
            if ($obj->type == "activity_update" && empty(trim($content))) {
                return "&nbsp;";
            }
            return $content;
        }, 9, 2);


        add_filter('bp_get_activity_content_body', function ($content) {
            $activity_content = preg_replace("/<p>(?:\s|&nbsp;)*?<\/p>/i", '', $content);

            if (empty(trim($activity_content))) {
                return '';
            }
            return $content;
        });

        add_filter("bp_get_activity_show_filters_options", function ($filters, $context) {
            foreach ($filters as $key => $val) {
                if (!in_array($key, $this->activity_filters)) {
                    unset($filters[$key]);
                }
            }
            return $filters;
        }, 2, 999);

        add_action("wp_footer", [$this, "get_users_activity_liked_modal"]);

        add_filter('bp_after_has_activities_parse_args', [$this, 'my_bp_activities_include_activity_types']);
        // set the content on activity blog post.
        if (isset($this->socialv_option['display_blog_post_type']) && $this->socialv_option['display_blog_post_type'] == '1') {
            add_filter('bp_get_activity_content_body', [$this, 'socialv_blogs_activity_content_with_read_more'], 9999, 2);
            add_filter('socialv_add_blog_post_as_activity_content_callback', [$this, 'socialv_add_blog_post_as_activity_content_callback'], 10, 3);
            add_filter('socialv_add_feature_image_blog_post_as_activity_content', [$this, 'socialv_add_feature_image_blog_post_as_activity_content_callback'], 10, 2);
        }

        // set the hide post option in activity page.
        if (isset($this->socialv_option['is_socialv_enable_hide_post']) && $this->socialv_option['is_socialv_enable_hide_post'] == '1') {
            add_action('socialv_activity_footer', [$this, 'socialv_activity_undo_post']);
        }

        //poll plugin
        if (class_exists('Buddypress_Polls')) {
            add_action('mpp_after_activity_upload_buttons', [$this, 'socialv_bpolls_polls_update_html']);
        }

        $socialv_stop_action = isset($this->socialv_option['socialv_stop_action']) ? $this->socialv_option['socialv_stop_action'] : [];
        if (!empty($socialv_stop_action)):
            foreach ($socialv_stop_action as $key => $value) {
                if ($value == 1) {
                    $method = "socialv_stop_action_" . $key;
                    add_action('init', [$this, $method]);
                }
            }
        endif;

        new PinActivity();

        //buddypress query controller
        add_action('bp_before_activity_loop', [$this, 'socialv_buddypress_activity_query']);

        //hatag activity
        // add_action('bp_activity_before_save', [$this, 'socialv_activity_hastag'], 2, 1);


    }


    public function socialv_register_activity_actions()
    {

        $contexts = ["sitewide", "member", "group", "activity"];
        $components = ["sitewide", "members", "groups", "activity"];

        // Register the activity stream actions for all enabled gallery component.
        foreach ($components as $component) {
            bp_activity_set_action(
                $component,
                'activity_share',
                __('User shared activity', 'socialv'),
                false,
                __('Activity Shared', 'socialv'),
                $contexts
            );
        }
    }
    /**
     * Show Shared activity Post Start 
     **/
    function socialv_share_activity($activity_id)
    {
        global $wpdb;
        $activity = $wpdb->get_results(
            $wpdb->prepare(
                "SELECT user_id, content, date_recorded, item_id, secondary_item_id, type FROM {$wpdb->base_prefix}bp_activity WHERE id = %d",
                $activity_id
            )
        );
        if (!empty($activity)) :
            $type = $activity[0]->type;
            $content = apply_filters('bp_get_activity_content_body', $activity[0]->content, $activity[0]);
            $user_id = $activity[0]->user_id;
            $group_id = $activity[0]->item_id;
            $html = '<a class="reshare-activity-overlay" href="' . esc_url(bp_get_activity_directory_permalink() . "p/" . $activity_id) . '"></a><div class="shared-activity socialv-activity-parent activity_share ' . esc_attr($type) . '" "id="activity-' . $activity_id . '">';
            $html .= $this->socialv_share_activity_header($activity_id);
            $html .= '<div class="activity-content">';

            switch ($type) {
                    // Profile Activity
                case 'new_avatar':
                case 'new_member':
                case 'friendship_created':
                case 'updated_profile':
                    $html .= $this->socialv_activity_content_avatar($user_id);
                    break;
                    // Group Activity 
                case 'joined_group':
                case 'created_group':
                    $html .= $this->socialv_activity_content_created_group($group_id);
                    break;
                    // Media Activity
                case 'mpp_media_upload':
                    $html .= '<div class="activity-inner">';
                    if (preg_match('/\s/', $content)) {
                        $html .= '<p>' . stripslashes($content) . '</p>';
                    }
                    $html .= '</div>';
                    $html .= $this->socialv_get_mpp_injected_attached_media_html($activity_id);
                    break;
                    // Blog Activity
                case 'new_blog_post':
                    $blog_post = get_post($activity[0]->secondary_item_id);
                    $content = apply_filters('socialv_add_blog_post_as_activity_content_callback', '', $blog_post, $activity);
                    $html .= '<div class="activity-inner">' . $content . '</div>';
                    break;

                default:
                    $html .= '<div class="activity-inner">';
                    if (!empty($content)) {
                        $html .= '<p>' . stripslashes($content) . '</p>';
                    }
                    if (class_exists('BuddyPress_GIPHY')) {
                        $html .= $this->socialv_activity_gif_content($activity_id);
                    }
                    $html .= '</div>';
                    break;
            }
            $html .= '</div>';
            $html .= '</div>';
            // $html .= '</a>';
            echo $html;
        endif;
    }

    function socialv_activity_content_avatar($user_id)
    {
        $cover_src = bp_attachments_get_attachment('url', array('item_id' => $user_id));
        $profile_url = bp_core_fetch_avatar(
            array(
                'item_id' => $user_id,
                'type' => 'full',
                'width' => 140,
                'height' => 140,
                'class' => 'rounded',
            )
        );
        $user_url = bp_members_get_user_url($user_id);
        $user_name = bp_core_get_user_displayname($user_id);
        $meta_name = bp_members_get_user_slug($user_id);
        $meta_url = wp_nonce_url(
            add_query_arg(
                array(
                    'r' => $meta_name,
                ),
                bp_get_activity_directory_permalink()
            )
        );
        $content = '<div class="activity-inner"><div class="bp-member-activity-preview socialv-profile-activity">';
        $content .= '<div class="bp-member-preview-cover">';
        $content .= '<a href="' . esc_url($user_url) . '">';
        if (!empty($cover_src)) :
            $content .= '<img src="' . esc_url($cover_src) . '" alt=" ' . esc_attr__('image', 'socialv') . '" loading="lazy" />';
        else :
            $content .= '<img src="' . esc_url(SOCIALV_DEFAULT_COVER_IMAGE) . '" alt="' . esc_attr__('activity', 'socialv') . '" loading="lazy" />';
        endif;
        $content .= '</a>';
        $content .= '</div>';

        $content .= '<div class="bp-member-short-description">';
        if (!empty($profile_url)) :
            $content .= '<div class="bp-member-avatar-content has-cover-image has-cover-image"><a href="' . esc_url($user_url) . '">' . wp_kses_post($profile_url) . '</a></div>';
        endif;
        $content .= '<div class="socialv-profile-detail">
                        <h5 class="bp-member-short-description-title">
                            <a href="' . esc_url($user_url) . '">' . esc_html($user_name) . '</a>
                        </h5>
                        <div class="bp-member-nickname">
                            <a href="' . esc_url(is_user_logged_in() ? $meta_url : $user_url) . '">@' . esc_html($meta_name) . '</a>
                        </div>
                     </div>';
        $content .= '</div></div></div>';
        return $content;
    }

    function socialv_activity_content_created_group($group_id)
    {
        $group = groups_get_group($group_id);
        $group_url = bp_get_group_url($group);
        $group_name = bp_get_group_name($group);
        $group_cover_src = bp_get_group_cover_url($group);
        $group_profile = bp_core_fetch_avatar(
            array(
                'item_id' => $group_id,
                'object' => 'group',
                'type' => 'full',
                'width' => 140,
                'height' => 140,
                'class' => 'rounded',
            )
        );
        $content = '<div class="activity-inner"><div class="bp-group-activity-preview socialv-group-activity">';
        $content .= '<div class="bp-group-preview-cover"><a href="' . esc_url($group_url) . '">';
        if (!empty($group_cover_src)) :
            $content .= '<img src="' . esc_url($group_cover_src) . '" alt="' . esc_attr__('image', 'socialv') . '" loading="lazy" />';
        else :
            $content .= '<img src="' . esc_url(SOCIALV_DEFAULT_COVER_IMAGE) . '" alt="' . esc_attr__('group', 'socialv') . '" loading="lazy" />';
        endif;
        $content .= '</a></div>';

        $content .= '<div class="bp-group-short-description">';
        if (!empty($group_profile)) :
            $content .= '<div class="bp-group-avatar-content has-cover-image">
                        <a href="' . esc_url($group_url) . '">
                            ' . wp_kses_post($group_profile) . '
                        </a>
                    </div>';
        endif;

        $content .= '<div class="bp-group-short-description-title socialv-profile-detail"><a href="' . esc_url($group_url) . '">' . esc_html($group_name) . '</a>';
        $content .= '<div class="activity-group-meta">';
        $count = bp_get_group_total_members($group);
        $member_count = ($count != 1) ? esc_html__("Members", "socialv") . " " . $count : esc_html__("Member", "socialv") . " " . $count;
        $group_type = bp_get_group_type($group);
        $check_group_type = " " . strtolower($group_type);
        $args = [
            "group_type" => $group_type,
            "member_count" => $member_count
        ];
        $icons = [
            "public" => "icon-web",
            "private" => "iconly-Lock icli",
            "hidden" => "iconly-Hide icli",
            "member" => "iconly-User2 icli"
        ];

        $group_icon = "public";
        if (strpos($check_group_type, "public")) {
            $group_icon = "public";
        } elseif (strpos($check_group_type, "private")) {
            $group_icon = "private";
        } elseif (strpos($check_group_type, "hidden")) {
            $group_icon = "hidden";
        }
        if (!bp_is_group()) {
            $group_type = '<span class="socialv-group-type"><span> <i class="' . $icons[$group_icon] . '"></i>' . $args["group_type"] . '</span></span>';
        } else {
            $group_type = '<span class="socialv-group-type"><span>' . $args["group_type"] . '</span></span>';
        }
        $member_count = '<span class="socialv-group-members"><span><i class="' . $icons['member'] . '"></i>' . $args["member_count"] . '</span></span>';

        $content .= $group_type . $member_count;
        $content .= '</div>';
        $content .= '</div></div></div></div>';
        return $content;
    }

    function socialv_activity_gif_content($activity_id)
    {
        $bp_activity_gif_data = bp_activity_get_meta($activity_id, '_bp_activity_gif_data', true);
        if (!empty($bp_activity_gif_data['bp_activity_gif'])) {
            $content = '<div class="activity-attached-gif-container">
                <div class="gif-image-container">
                    <div class="gif-player">
                        <img src="' . esc_url($bp_activity_gif_data['bp_activity_gif']) . '" />
                    </div>
                </div>
            </div>';
            return $content;
        }
    }

    function socialv_share_activity_header($activity_id, $args = [])
    {
        global $wpdb, $activities_template;

        $activity = $wpdb->get_results(
            $wpdb->prepare(
                "SELECT user_id, action, content, date_recorded, type FROM {$wpdb->base_prefix}bp_activity WHERE id = %d",
                $activity_id
            )
        );
        $user_id = $activity[0]->user_id;
        $action = $activity[0]->action;
        $date_recorded = $activity[0]->date_recorded;
        $r = bp_parse_args(
            $args,
            array(
                'no_timestamp' => false,
            )
        );
        $time_since = sprintf(
            '<span class="time-since" data-livestamp="%1$s">%2$s</span>',
            bp_core_get_iso8601_date($date_recorded),
            bp_core_time_since($date_recorded)
        );
        $userlink = bp_core_get_userlink($user_id);
        $profile_link = trailingslashit(bp_members_get_user_url($user_id) . bp_get_profile_slug());
        $user_profile_link = '<a href="' . $profile_link . '">' . bp_core_get_user_displayname($user_id) . '</a>';

        // Add badge HTML if the user is verified
        $user_badge = (bp_get_activity_user_id() != $user_id) ? socialv()->socialv_get_verified_badge($user_id) : '';
        $activity_action = str_replace($userlink, $userlink . $user_badge . ' <span class="activity-subtext">', $action);
        $activity_action = str_replace($user_profile_link, $user_profile_link . $user_badge . '<span class="activity-subtext">', $activity_action);
        $activity_action .= "</span>";
        $loop_avatar_width = apply_filters("activity_loop_avatar_width", "65");
        $loop_avatar_height = apply_filters("activity_loop_avatar_height", "65");
        $content = '<div class="socialv-activity-header">
            <div class="socialv-activity-header-left">
                <div class="activity-avatar-sv">
                    <a href="' . bp_get_activity_user_link() . '">
                    ' . bp_get_activity_avatar('user_id=' . $user_id . '&type=full&width=' . $loop_avatar_width . '&height=' . $loop_avatar_height . '&class=rounded-circle') . '
                    </a>
                </div>
            </div>
            <div class="activity-header-wrapper">
                <div class="activity-header">
                    ' . apply_filters_ref_array(
            'bp_get_activity_action',
            array(
                $activity_action,
                &$activities_template->activity,
                $r
            )
        ) . '
                    ' . wp_kses($time_since, 'post') . '
                </div>
            </div>
        </div>';
        return $content;
    }

    /**
     * Show Shared activity Post End 
     **/

    function socialv_update_activity_action($user_id, $posted_field_ids, $errors, $old_values, $new_values)
    {

        $is_update_displayname = false;
        foreach ($old_values as $value) {
            if (in_array(bp_core_get_user_displayname(get_current_user_id()), $value))
                $is_update_displayname = true;
        }
        if ($is_update_displayname && bp_has_activities(bp_ajax_querystring('activity') . '&action=mpp_media_upload&user_id=' . $user_id)) {
            while (bp_activities()) :
                bp_the_activity();
                $ac = new BP_Activity_Activity(bp_get_activity_id());
                $ac->save();
            endwhile;
        }
    }
    function socialv_friends_only_activity_args($args)
    {
        if (isset($this->socialv_option['display_activity_showing_friends']) && $this->socialv_option['display_activity_showing_friends'] == 'no') {
            if (!bp_is_activity_directory() || !is_user_logged_in() || !empty($args['scope'])) {
                return $args;
            }

            $user_id = get_current_user_id();
            $user_ids = [];

            if (function_exists('friends_get_friend_user_ids')) {
                $user_ids = friends_get_friend_user_ids($user_id);
            }

            // include user's own too?
            array_push($user_ids, $user_id);
            $args['user_id'] = $user_ids;
        }

        return $args;
    }

    function socialv_reverse_activity_comments($has)
    {
        $comment_order = isset($this->socialv_option['display_comments_order']) ? $this->socialv_option['display_comments_order'] : 'ASC';
        if ($comment_order == 'DESC') {
            global $activities_template;
            foreach ($activities_template->activities as &$a) {
                if (is_array($a->children))
                    $a->children = array_reverse($a->children);
            }
        }
        return $has;
    }
    function my_bp_activities_include_activity_types($retval)
    {
        if (empty($retval['action'])) {
            $actions = bp_activity_get_actions_for_context();
            foreach ($actions as $action) {

                // Friends activity collapses two filters into one.
                if (in_array($action['key'], array('friendship_accepted', 'friendship_created'))) {
                    $action['key'] = 'friendship_accepted,friendship_created';
                }
                // The 'activity_update' filter is already used by the Activity component.
                if ('bp_groups_format_activity_action_group_activity_update' === $action['format_callback']) {
                    continue;
                }
                if (isset($this->socialv_option['display_blog_post_type']) && $this->socialv_option['display_blog_post_type'] == '1') {
                    if (!in_array($action['key'], ["new_blog_comment", "activity_comment"]))
                        $filters[] = $action['key'];
                } else {
                    if (!in_array($action['key'], ["new_blog_post", "new_blog_comment", "activity_comment"]))
                        $filters[] = $action['key'];
                }
            }
            $filters = array_merge($filters, $this->activity_filters);
            $retval['action'] = apply_filters("socialv_add_actions_to_activity", $filters);
        }
        if (bp_is_user_activity() && !bp_activity_can_favorite()) {
            $retval['object'] = 'activity';
            $retval['primary_id'] = 'groups';
        }
        return $retval;
    }

    // Remove the 'poster' parameter from the shortcode
    public function socialv_remove_poster_image_from_video_shortcode($html, $atts, $video, $post_id, $library)
    {
        $html = str_replace(' poster=', '', $html);
        return $html;
    }

    public function socialv_append_video_types($exts)
    {
        $exts[] = 'mov';
        return $exts;
    }

    public function socialv_activity_upload_buttons()
    {
        $component = mpp_get_current_component();
        $component_id = mpp_get_current_component_id();

        // If activity upload is disabled or the user is not allowed to upload to current component, don't show.
        if (!mpp_is_activity_upload_enabled($component) || !mpp_user_can_upload($component, $component_id)) {
            return;
        }

        // if we are here, the gallery activity stream upload is enabled,
        // let us see if we are on user profile and gallery is enabled.
        if (!mpp_is_enabled($component, $component_id)) {
            return;
        }
        // if we are on group page and either the group component is not enabled or gallery is not enabled for current group, do not show the icons.
        if (function_exists('bp_is_group') && bp_is_group() && (!mpp_is_active_component('groups') || !(function_exists('mpp_group_is_gallery_enabled') && mpp_group_is_gallery_enabled()))) {
            return;
        }
        // for now, avoid showing it on single gallery/media activity stream.
        if (mpp_is_single_gallery() || mpp_is_single_media()) {
            return;
        }

?>
        <div class="mpp-upload-buttons socialv-upload-file">
            <div id="mpp-activity-upload-buttons">
                <?php do_action('mpp_before_activity_upload_buttons'); // allow to add more type.  
                ?>

                <?php if (mpp_is_active_type('photo') && mpp_component_supports_type($component, 'photo')) : ?>
                    <a href="#" id="mpp-photo-upload" data-media-type="photo" title="<?php esc_attr_e('Upload photo', 'socialv'); ?>">
                        <label class="socialv-upload-btn-labels">
                            <span class="upload-icon"><i class="iconly-Image-2 icli"></i> </span>
                            <span>
                                <?php esc_html_e("Photos", "socialv"); ?>
                            </span>
                        </label>
                    </a>
                <?php endif; ?>

                <?php if (mpp_is_active_type('audio') && mpp_component_supports_type($component, 'audio')) : ?>
                    <a href="#" id="mpp-audio-upload" data-media-type="audio" title="<?php esc_attr_e('Upload audio', 'socialv'); ?>">
                        <label class="socialv-upload-btn-labels">
                            <span class="upload-icon"><i class="iconly-Game icli"></i></span>
                            <span>
                                <?php esc_html_e("Audios", "socialv"); ?>
                            </span>
                        </label>
                    </a>
                <?php endif; ?>

                <?php if (mpp_is_active_type('video') && mpp_component_supports_type($component, 'video')) : ?>
                    <a href="#" id="mpp-video-upload" data-media-type="video" title="<?php esc_attr_e('Upload video', 'socialv'); ?>">
                        <label class="socialv-upload-btn-labels">
                            <span class="upload-icon"><i class="iconly-Video icli"></i></span>
                            <span>
                                <?php esc_html_e("Videos", "socialv"); ?>
                            </span>
                        </label>
                    </a>
                <?php endif; ?>

                <?php if (mpp_is_active_type('doc') && mpp_component_supports_type($component, 'doc')) : ?>
                    <a href="#" id="mpp-doc-upload" data-media-type="doc" title="<?php esc_attr_e('Upload document', 'socialv'); ?>">
                        <label class="socialv-upload-btn-labels">
                            <span class="upload-icon"><i class="iconly-Document icli"></i></span>
                            <span>
                                <?php esc_html_e("Documents", "socialv"); ?>
                            </span>
                        </label>
                    </a>
                <?php endif; ?>
            </div>
            <?php do_action('mpp_after_activity_upload_buttons'); // allow to add more type.  
            ?>
        </div>
    <?php
    }

    function socialv_activity_upload_dropzone()
    {
    ?>
        <div id="mpp-activity-media-upload-container" class="mpp-media-upload-container mpp-upload-container-inactive">
            <!-- mediapress upload container -->
            <a href="#" class="mpp-upload-container-close" title="<?php esc_attr_e('Close', 'socialv'); ?>"><span>x</span></a>
            <!-- append uploaded media here -->
            <div id="mpp-uploaded-media-list-activity" class="mpp-uploading-media-list">
                <ul class="list-inline"></ul>
            </div>

            <?php do_action('mpp_after_activity_upload_medialist'); ?>

            <?php if (mpp_is_file_upload_enabled('activity')) : ?>
                <!-- drop files here for uploading -->
                <?php mpp_upload_dropzone('activity'); ?>
                <?php do_action('mpp_after_activity_upload_dropzone'); ?>
                <!-- show any feedback here -->
                <div id="mpp-upload-feedback-activity" class="mpp-feedback">
                    <ul></ul>
                </div>
            <?php endif; ?>
            <input type='hidden' name='mpp-context' class='mpp-context' value="activity" />
            <?php do_action('mpp_after_activity_upload_feedback'); ?>

            <?php if (mpp_is_remote_enabled('activity')) : ?>
                <!-- remote media -->
                <div class="mpp-remote-media-container">
                    <div class="mpp-feedback mpp-remote-media-upload-feedback">
                        <ul></ul>
                    </div>
                    <div class="mpp-remote-add-media-row mpp-remote-add-media-row-activity">
                        <input type="text" placeholder="<?php esc_attr_e('Enter a link', 'socialv'); ?>" value="" name="mpp-remote-media-url" id="mpp-remote-media-url" class="mpp-remote-media-url" />
                        <button id="mpp-add-remote-media" class="mpp-add-remote-media"><i class="icon-add"></i></button>
                    </div>

                    <?php wp_nonce_field('mpp_add_media', 'mpp-remote-media-nonce'); ?>
                </div>
                <!-- end of remote media -->
            <?php endif; ?>

        </div><!-- end of mediapress form container -->
    <?php
    }

    public function socialv_activity_header($args = [])
    {
        global $activities_template;
        $activity = $activities_template->activity;
        $activity_id = bp_get_activity_id();
        $user_id = $activity->user_id;
        $activity_action = $activity->action;
        $date_recorded = $activity->date_recorded;

        $r = bp_parse_args(
            $args,
            array(
                'no_timestamp' => false,
            )
        );

        $time_since = sprintf(
            '<span class="time-since" data-livestamp="%1$s">%2$s</span>',
            bp_core_get_iso8601_date($date_recorded),
            bp_core_time_since($date_recorded)
        );


        $userlink = bp_core_get_userlink($user_id);

        $profile_link = trailingslashit(bp_members_get_user_url($activity->user_id) . bp_get_profile_slug());
        $user_profile_link = '<a href="' . $profile_link . '">' . bp_core_get_user_displayname($activity->user_id) . '</a>';
        $activity_action = str_replace($userlink, $userlink . ' <span class="activity-subtext">', $activity_action);
        $activity_action = str_replace($user_profile_link, $user_profile_link . '<span class="activity-subtext">', $activity_action);
        $activity_action .= "</span>";
        $loop_avatar_width = apply_filters("activity_loop_avatar_width", "65");
        $loop_avatar_height = apply_filters("activity_loop_avatar_height", "65");

    ?>
        <div class="socialv-activity-header-left">
            <div class="activity-avatar-sv">
                <a href="<?php bp_activity_user_link(); ?>">
                    <?php bp_activity_avatar('type=full&width=' . $loop_avatar_width . '&height=' . $loop_avatar_height . '&class=rounded-circle'); ?>
                </a>
            </div>
        </div>
        <div class="activity-header-wrapper">
            <div class="activity-header">
                <!-- action -->
                <?php
                echo apply_filters_ref_array(
                    'bp_get_activity_action',
                    array(
                        $activity_action,
                        &$activities_template->activity,
                        $r
                    )
                );
                ?>

                <!-- time since -->
                <?php echo wp_kses($time_since, 'post'); ?>

            </div>
            <?php if (is_user_logged_in()) : ?>
                <div class="socialv-activity-header-right">
                    <div class="dropdown">
                        <?php if ($this->is_socialv_user_pin($activity_id, $this->socialv_current_component())) : ?>
                            <i class="icon-pin pinicon me-3 mt-2"></i>
                        <?php endif; ?>
                        <a class="btn-dropdown" href="javascript:vloid(0);" role="button" id="context-<?php echo esc_attr($activity_id); ?>" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="icon-toggle-dot"></i>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="context-<?php echo esc_attr($activity_id); ?>">
                            <?php if (bp_activity_can_favorite()) : ?>
                                <li>
                                    <?php if (!bp_get_activity_is_favorite()) : ?>

                                        <a href="<?php bp_activity_favorite_link(); ?>" class="dropdown-item fav">
                                            <?php esc_html_e("Add Favorite", "socialv"); ?>
                                        </a>

                                    <?php else : ?>

                                        <a href="<?php bp_activity_unfavorite_link(); ?>" class="dropdown-item unfav">
                                            <?php esc_html_e("Remove Favorite", "socialv"); ?>
                                        </a>

                                    <?php endif; ?>

                                </li>
                            <?php endif; ?>
                            <?php if ($this->socialv_helping_pin_activity($activity_id)) : ?>
                                <li>
                                    <?php if ($this->is_socialv_user_pin($activity_id, $this->socialv_current_component())) : ?>
                                        <a class="dropdown-item socialv-user-activity-btn has-socialv-pin" data-id="<?php echo esc_attr($activity_id); ?>" href="javascript:void(0)" data-unpin="<?php esc_attr_e("Unpin", "socialv"); ?>" data-pin="<?php esc_attr_e("Pin to Top", "socialv"); ?>" component="<?php echo esc_attr($this->socialv_current_component()); ?>" <?php echo esc_attr($this->socialv_helping_component()); ?>>
                                            <?php esc_html_e("Unpin", "socialv"); ?>
                                        </a>
                                    <?php else : ?>
                                        <a class="dropdown-item socialv-user-activity-btn has-socialv-pin" data-id="<?php echo esc_attr($activity_id); ?>" href="javascript:void(0)" data-unpin="<?php esc_attr_e("Unpin", "socialv"); ?>" data-pin="<?php esc_attr_e("Pin to Top", "socialv"); ?>" component="<?php echo esc_attr($this->socialv_current_component()); ?>" <?php echo esc_attr($this->socialv_helping_component()); ?>>
                                            <?php esc_html_e("Pin to Top", "socialv"); ?>
                                        </a>
                                    <?php endif; ?>
                                </li>
                            <?php endif; ?>

                            <?php if (shortcode_exists("imt_report_button")) : ?>
                                <li>
                                    <?php echo do_shortcode("[imt_report_button id=$activity_id type=activity classes='dropdown-item']"); ?>
                                </li>
                            <?php endif; ?>
                            <?php
                            if (isset($this->socialv_option['is_socialv_enable_hide_post']) && $this->socialv_option['is_socialv_enable_hide_post'] == '1') {
                                if (get_current_user_id() !== bp_get_activity_user_id()) { ?>
                                    <li><a href="javascript:void(0)" data-type="hide" data-activity_id="<?php echo esc_attr($activity_id); ?>" data-id="<?php echo esc_attr($user_id); ?>" class="dropdown-item hide-post-btn">
                                            <?php esc_html_e("Hide Post", "socialv"); ?>
                                        </a></li>
                            <?php }
                            } ?>
                            <?php if (bp_activity_user_can_delete()) : ?>
                                <a class="dropdown-item socialv_delete-activity" href="<?php bp_activity_delete_url(); ?>" data-success="<?php esc_attr_e('Activity deleted successfully', 'socialv') ?>">
                                    <?php esc_html_e("Delete", "socialv") ?>
                                </a>
                            <?php endif; ?>

                            <?php do_action("socialv_before_activity_header_right_end"); ?>
                        </ul>
                    </div>
                </div>
            <?php endif; ?>
        </div>
        <?php
    }

    function socialv_activity_group_meta()
    {
        global $activities_template;

        $group = bp_get_group($activities_template->activity->item_id);
        $count = bp_get_group_total_members($group->id);
        $member_count = ($count != 1) ? esc_html__("Members", "socialv") . " " . $count : esc_html__("Member", "socialv") . " " . $count;
        $group_type = bp_get_group_type($group->id);

        $check_group_type = " " . strtolower($group_type);
        $args = [
            "group_type" => $group_type,
            "member_count" => $member_count
        ];

        $icons = [
            "public" => "icon-web",
            "private" => "iconly-Lock icli",
            "hidden" => "iconly-Hide icli",
            "member" => "iconly-User2 icli"
        ];

        $group_icon = "public";
        if (strpos($check_group_type, "public"))
            $group_icon = "public";

        if (strpos($check_group_type, "private"))
            $group_icon = "private";

        if (strpos($check_group_type, "hidden"))
            $group_icon = "hidden";
        if (!bp_is_group()) {
            $group_type = '<span class="socialv-group-type"><span> <i class="' . $icons[$group_icon] . '"></i>' . $args["group_type"] . '</span></span>';
        } else {
            $group_type = '<span class="socialv-group-type"><span>' . $args["group_type"] . '</span></span>';
        }
        $member_count = '<span class="socialv-group-members"><span><i class="' . $icons['member'] . '"></i>' . $args["member_count"] . '</span></span>';

        $content = $group_type . $member_count;
        return apply_filters("socialv_activity_group_meta_html", $content, $args, $icons);
    }

    public function socialv_mpp_activity_inject_attached_media_html()
    {
        $activity_id = bp_get_activity_id();
        echo $this->socialv_get_mpp_injected_attached_media_html($activity_id);
    }

    public function socialv_get_mpp_injected_attached_media_html($activity_id = false)
    {
        $activity_id = $activity_id ? $activity_id : bp_get_activity_id();;
        $media_ids = mpp_activity_get_attached_media_ids($activity_id);
        if (empty($media_ids)) {
            return;
        }

        $lightbox = mpp_get_option('load_lightbox');
        $lightbox_enabled = !empty($lightbox) ? 1 : 0;
        $gallery_class = $lightbox_enabled ? ' zoom-gallery' : '';
        $count = count($media_ids);
        $no_of_media = ($count >= 5) ? '5' : $count;
        ob_start();
        if ($this->display_posts_style == true) {
            if ($count > 1) {
                echo '<div class="swiper socialv-gallery-status socialv-swiper-slider' . $gallery_class . '"> <div class="swiper-wrapper">';
                $loop_inner_class = "swiper-slide";
            } else {
                echo '<div class="socialv-gallery-status' . $gallery_class . '">';
                $loop_inner_class = "grid-item";
            }

            foreach ($media_ids as $key => $media_id) {
                if ($key > 5)
                    break;
                $media = mpp_get_media($media_id);
                if ($media) { ?>
                    <div class="<?php echo esc_attr($loop_inner_class); ?>">
                        <div class="video-wrap">
                            <?php echo $this->socialv_generate_image_div($media, $activity_id, $count); ?>
                        </div>
                    </div>
                <?php }
            }
            if ($count > 1) {
                echo '</div><div class="swiper-pagination"></div>';
            }
            echo '</div>';
        } else {

            // =======Grid Style===========

            echo '<div class="row post-row column-' . $no_of_media . $gallery_class . '">';
            foreach ($media_ids as $key => $media_id) {
                $media = mpp_get_media($media_id);
                if ($media) {
                    $class = 'post-column ';
                    $div_inner = '';
                    if ($count == 1) {
                        $class .= 'col-12';
                        $div_inner = $this->socialv_generate_image_div($media, $activity_id, $count);
                    } elseif ($count == 2) {
                        $class .= 'col-6';
                        $div_inner .= $this->socialv_generate_image_div($media, $activity_id, $count);
                    } elseif ($count == 3) {
                        $class .= ($key == 0) ? 'col-12' : 'col-6';
                        $div_inner .= $this->socialv_generate_image_div($media, $activity_id, $count);
                    } elseif ($count == 4) {
                        $class .= 'col-6';
                        $div_inner .= $this->socialv_generate_image_div($media, $activity_id, $count);
                    } elseif ($count >= 5) {
                        $class .= ($key < 5) ? 'col-12' : 'd-none';
                        $div_inner .= $this->socialv_generate_image_div($media, $activity_id, $count);
                        $div_inner .= ($count > 5 && $key == 4) ? ('<span class=socialv-media-total>+' . esc_html($count - 5) . '</span>') : '';
                    }
                    if ($count >= 5 && $key == 0) {
                        echo '<div class="col-6 post-column"><div class="row post-row two">';
                    } elseif ($count >= 5 && $key == 2) {
                        echo '</div></div><div class="col-6 post-column"><div class="row post-row three">';
                    }
                    echo '<div class="' . esc_attr($class) . '">' . $div_inner . '</div>';
                    if ($count >= 5) {
                        if ($media->type == 'audio' && $key == 3) {
                            echo '</div></div>';
                        } else if ($media->type != 'audio' && $key == 4) {
                            echo '</div></div>';
                        }
                    }
                }
            }
            echo '</div>';

            // =====================
        }

        return ob_get_clean();
    }

    // Helper function to generate inner image div
    function socialv_generate_image_div($media, $activity_id, $count)
    {
        $type = $media->type;
        $media_id = $media->id;
        $media_src = '';
        $div_html = '';
        $full_img_class = '';
        $link_wrapper = ($this->display_posts_style == false && $count != 1) ? ('<a class="mpp-activity-item-title" href="' . mpp_get_media_permalink($media_id) . '" title="' . esc_attr(mpp_get_media_title($media_id)) . '" data-mpp-type="' . esc_attr($type) . '" data-mpp-activity-id="' . esc_attr($activity_id) . '" data-mpp-media-id="' . esc_attr($media_id) . '">' . mpp_get_media_title($media_id) . '</a>') : '';

        if ($type == 'photo') {
            $is_external = mpp_get_media_meta($media_id, "_mpp_source", true);
            if ($is_external) {
                $media_src = $is_external;
                $img_attrs = "target=_blank";
            } else {
                $media_src = mpp_get_media_src('full', $media);
                $img_attrs = "class=popup-zoom mpp-media mpp-activity-media mpp-activity-media-" . $type;
            }

            $bg_img = (($this->socialv_option['is_post_blur_style'] == 'no') || ($this->display_posts_style == false && $count == 1)) ? ('style="background-image: url(' . esc_url($media_src) . ') "') : '';
            $full_img_class = (($this->socialv_option['is_post_blur_style'] == 'yes') && $count == 1) ? 'single-post-img' : '';

            $div_html = '<div class="post-wrap-inner mpp-' . $type . '-content mpp-activity-' . $type . '-content ' . $full_img_class . '" ' . $bg_img . ' >
                        <a href="' . esc_url($media_src) . '" ' . esc_attr($img_attrs) . '>
                            <img src="' . esc_url($media_src) . '" class="mpp-attached-media-item" alt="' . esc_attr__('Status Image', 'socialv') . '" loading="lazy">
                        </a></div>';

            return $div_html;
        } elseif ($type == 'video') {
            if (mpp_is_oembed_media($media)) {
                return '<div class="socialv_video_height post-wrap-inner mpp-activity-media-list mpp-activity-video-player">' . mpp_get_oembed_content($media, 'full') . $link_wrapper . '</div>';
            } else {
                $media_file = mpp_get_media_src('', $media);
                return '<div class="socialv_video_height post-wrap-inner mpp-activity-media-list mpp-activity-video-player">' . do_shortcode("[video src=" . $media_file . " controls]") . $link_wrapper . '</div>';
            }
        } elseif ($type == 'audio') {
            $link_wrapper = ($count > 5) ? $link_wrapper : '';
            $div_html = '<div class="post-wrap-inner mpp-activity-media-list mpp-activity-audio-player"><audio src="' . mpp_get_media_src('', $media) . '" controls></audio>' . $link_wrapper . '</div>';
            return $div_html;
        } else {
            $url = !mpp_is_doc_viewable($media) ? mpp_get_media_src('', $media) : mpp_get_media_permalink($media);
            $class = !mpp_is_doc_viewable($media) ? 'mpp-no-lightbox' : '';
            $target = !mpp_is_doc_viewable($media) ? '' : 'target=_blank';
            $div_html = '<div class="post-wrap-inner mpp-activity-media-list" data-mpp-type="' . esc_attr($type) . '">
                        <a href="' . esc_url($url) . '" ' . esc_attr($target) . ' class="mpp-media mpp-activity-media mpp-activity-media-doc ' . esc_attr($class) . '" data-mpp-type="' . esc_attr($type) . '" data-mpp-activity-id="' . esc_attr($activity_id) . '" data-mpp-media-id="' . esc_attr($media_id) . '">
                            <img src="' . mpp_get_media_src('thumbnail', $media_id) . '" class="mpp-attached-media-item " title="' . esc_attr(mpp_get_media_title($media_id)) . '" loading="lazy" />
                        </a></div>';
            return $div_html;
        }


        // } elseif ($type == 'doc') { 

        //     $postId = bp_get_activity_id();
        //     $media_src = mpp_get_media_src('', $media);

        //     $file_info = pathinfo( $media_src );
        //     $file_name = $file_info['basename'];

        //     $file_path = str_replace( home_url('/'), ABSPATH, $media_src );
        //     $file_size = ( file_exists( $file_path ) ) ? size_format( filesize( $file_path ) ) : '' ;

        //     $div_html .= '<div class='.( $file_info['extension'] === 'pdf' ? 'iq-pdf-preview' : 'iq-doc-preview' ).'>';

        //         if ( $file_info['extension'] === 'pdf' ) {
        //             $save_dir = wp_upload_dir()['basedir'] . '/doc-previews';
        //             ( !file_exists( $save_dir ) ) ? wp_mkdir_p( $save_dir ) : '';

        //             $image_url = bp_activity_get_meta( $activity_id, '_pdf_image_preview_url' );

        //             if ( empty( $image_url)  ) {
        //                 $image_url = socialv()->socialv_create_pdf_image_preview( $media_src, $save_dir );
        //                 if ( is_wp_error($image_url ) ) {
        //                     return '<div class="mpp-activity-doc-preview-error">' . $image_url->get_error_message() . '</div>';
        //                 }
        //                 bp_activity_update_meta( $activity_id, '_pdf_image_preview_url', $image_url );
        //             }

        //             $div_html .= '<div class="post-wrap-inner mpp-doc-content mpp-activity-doc-content ' . $full_img_class . '">
        //                 <a href="' . esc_url( $media_src ) . '" target="_blank" class="doc-previews-link">
        //                     <img src="' . esc_url(wp_upload_dir()['baseurl'] . '/doc-previews/' . basename( $image_url ) ) . '" class="mpp-attached-pdf-media-item" alt="' . esc_attr__('Document Preview', 'socialv') . '" loading="lazy">
        //                 </a>
        //             </div>';
        //         }   

        //         $div_html .= '<div class="iq-description-wrap d-flex gap-3">
        //             <a data-post-id="' . $postId . '" data-bs-target="#showfilepreview" data-bs-toggle="modal" class="show_file_preview">';
        //                 if( $file_info['extension'] === 'pdf' ) {
        //                     $div_html .= '<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="50" height="50" viewBox="0 0 256 256" xml:space="preserve"><defs></defs><g style="stroke: none; stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-linejoin: miter; stroke-miterlimit: 10; fill: none; fill-rule: nonzero; opacity: 1;" transform="translate(1.4065934065934016 1.4065934065934016) scale(2.81 2.81)" ><path d="M 77.474 17.28 L 61.526 1.332 C 60.668 0.473 59.525 0 58.311 0 H 15.742 c -2.508 0 -4.548 2.04 -4.548 4.548 v 80.904 c 0 2.508 2.04 4.548 4.548 4.548 h 58.516 c 2.508 0 4.549 -2.04 4.549 -4.548 V 20.496 C 78.807 19.281 78.333 18.138 77.474 17.28 z M 61.073 5.121 l 12.611 12.612 H 62.35 c -0.704 0 -1.276 -0.573 -1.276 -1.277 V 5.121 z M 74.258 87 H 15.742 c -0.854 0 -1.548 -0.694 -1.548 -1.548 V 4.548 C 14.194 3.694 14.888 3 15.742 3 h 42.332 v 13.456 c 0 2.358 1.918 4.277 4.276 4.277 h 13.457 v 64.719 C 75.807 86.306 75.112 87 74.258 87 z" style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-linejoin: miter; stroke-miterlimit: 10; fill: rgb(0,0,0); fill-rule: nonzero; opacity: 1;" transform=" matrix(1 0 0 1 0 0) " stroke-linecap="round" /><path d="M 26.806 75.278 c -1.29 0 -2.11 -0.732 -2.473 -1.15 l -0.14 -0.162 l -0.089 -0.193 c -0.854 -1.853 -1.232 -4.745 1.809 -8.01 c 2.483 -2.666 6.624 -4.957 11.318 -6.697 c 1.585 -2.964 3.067 -6.128 4.265 -9.157 c -2.997 -4.992 -4.234 -10.248 -2.971 -13.719 c 0.709 -1.948 2.179 -3.174 4.14 -3.452 l 0.211 -0.015 c 2.147 0 3.689 1.282 4.232 3.516 c 0.729 3.003 -0.306 7.954 -2.271 13.335 c 0.745 1.108 1.601 2.216 2.567 3.293 c 0.912 1.101 1.842 2.1 2.771 2.999 c 5.057 -0.67 9.654 -0.562 12.521 0.52 c 3.231 1.219 3.771 3.396 3.804 4.586 l 0.001 0.03 c 0.016 1.91 -0.942 3.359 -2.63 3.976 c -3.511 1.286 -9.379 -1.229 -14.678 -5.943 c -3.251 0.526 -6.669 1.374 -9.849 2.472 c -3.526 6.359 -7.529 11.694 -10.478 13.216 C 28.081 75.126 27.395 75.278 26.806 75.278 z M 26.714 72.252 c 0.112 0.046 0.321 0.038 0.776 -0.197 c 1.955 -1.009 4.643 -4.384 7.299 -8.718 c -2.787 1.308 -5.143 2.82 -6.681 4.471 C 26.094 69.97 26.427 71.524 26.714 72.252 z M 53.291 58.535 c 4.155 3.122 7.893 4.232 9.55 3.624 c 0.321 -0.117 0.662 -0.31 0.659 -1.119 c -0.01 -0.333 -0.063 -1.169 -1.864 -1.849 C 59.685 58.454 56.703 58.272 53.291 58.535 z M 43.53 52.896 c -0.677 1.611 -1.421 3.235 -2.208 4.829 c 1.733 -0.499 3.498 -0.924 5.243 -1.266 c -0.488 -0.526 -0.967 -1.072 -1.432 -1.634 C 44.57 54.198 44.035 53.553 43.53 52.896 z M 42.982 35.724 c -0.531 0.094 -1.232 0.375 -1.639 1.492 c -0.705 1.937 -0.214 5.265 1.519 8.907 c 1.234 -3.82 1.822 -7.146 1.329 -9.176 C 43.936 35.892 43.479 35.739 42.982 35.724 z" style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-linejoin: miter; stroke-miterlimit: 10; fill: rgb(0,0,0); fill-rule: nonzero; opacity: 1;" transform=" matrix(1 0 0 1 0 0) " stroke-linecap="round" /></g></svg>';
        //                 } elseif( $file_info['extension'] === 'txt' ) {
        //                     $div_html .= '<svg width="50" height="50" viewBox="0 0 50 50" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M37.887 47H12.113C10.394 47 9 45.613 9 43.901V5.479C9 4.11 10.115 3 11.491 3H29.5L41 14.5V43.901C41 45.613 39.606 47 37.887 47Z" stroke="black" stroke-width="2" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/><path d="M29 3V12.333C29 13.806 30.194 15 31.667 15H41" stroke="black" stroke-width="2" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/><path d="M13.5795 26.2102V25.2727H20.125V26.2102H17.3807V34H16.3239V26.2102H13.5795ZM22.2674 25.2727L24.5174 28.9034H24.5856L26.8356 25.2727H28.0799L25.3356 29.6364L28.0799 34H26.8356L24.5856 30.4375H24.5174L22.2674 34H21.0231L23.8356 29.6364L21.0231 25.2727H22.2674ZM28.978 26.2102V25.2727H35.5234V26.2102H32.7791V34H31.7223V26.2102H28.978Z" fill="black"/></svg>';
        //                 } else {
        //                     $div_html .= '<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 50 50" width="50px" height="50px"><path style="fill:none;stroke:#000000;stroke-width:2;stroke-linecap:round;stroke-linejoin:round;stroke-miterlimit:10;" d="M37.887,47H12.113C10.394,47,9,45.613,9,43.901V5.479C9,4.11,10.115,3,11.491,3H29.5L41,14.5v29.401C41,45.613,39.606,47,37.887,47z"/><path style="fill:none;stroke:#000000;stroke-width:2;stroke-linecap:round;stroke-linejoin:round;stroke-miterlimit:10;" d="M29,3v9.333C29,13.806,30.194,15,31.667,15H41"/><line style="fill:none;stroke:#000000;stroke-width:2;stroke-miterlimit:10;" x1="17" y1="25" x2="33" y2="25"/><line style="fill:none;stroke:#000000;stroke-width:2;stroke-miterlimit:10;" x1="17" y1="29" x2="33" y2="29"/><line style="fill:none;stroke:#000000;stroke-width:2;stroke-miterlimit:10;" x1="17" y1="33" x2="33" y2="33"/><line style="fill:none;stroke:#000000;stroke-width:2;stroke-miterlimit:10;" x1="17" y1="37" x2="26" y2="37"/></svg>';
        //                 }
        //                 $div_html .= '
        //                 <div class="d-inline-block align-middle">
        //                     <span class="iq-title">' . esc_html( $file_name ) . '</span>';
        //                     $div_html .=  ( file_exists( $file_path ) ) ? '<span class="iq-size me-1">' . esc_html( size_format( filesize( $file_path ) ) ) . '</span>' : '' ;
        //                     $div_html .= '<span class="iq-extension-type">'.pathinfo( $file_name, PATHINFO_EXTENSION ).'</span>
        //                     <span class="iq-helper-text"> <span> - </span><span class="iq-helper-text-click">Click to</span><span class="iq-helper-text-inner"> view</span></span>
        //                 </div>
        //             </a>
        //         </div>';

        //         $div_html .= '<div class="document-action-wrap">
        //             <a href="#" class="document-action_collapse" data-balloon-pos="up" data-tooltip-collapse="Collapse" data-balloon="Expand"><i class="bb-icon-merge bb-icon-l document-icon-collapse"></i></a>
        //             <a href="' . esc_url( $media_src ) . '" download><i class="icon-download"></i></a>
        //         </div>';

        //     $div_html .= '</div>';

        //     return $div_html;

        // } 

    }


    function socialv_social_share()
    {
        $user_id = bp_displayed_user_id();
        $activity_id = bp_get_activity_id();
        $url = urlencode(bp_get_activity_directory_permalink() . "p/" . $activity_id);
        $title = rawurlencode(strip_tags(bp_get_activity_action()));
        $args = [
            'facebook' => [
                'url' => "http://www.facebook.com/sharer/sharer.php?u=$url",
                'icon' => 'icon-facebook',
                'title' => esc_html__('Facebook', 'socialv'),
                'parameter' => 'target=_blank',
            ],
            'twitter' => [
                'url' => "https://twitter.com/intent/tweet?source=$url&text=$title:$url",
                'icon' => 'icon-twitter',
                'title' => esc_html__('Twitter', 'socialv'),
                'parameter' => 'target=_blank',
            ],
            'linkedin' => [
                'url' => "http://www.linkedin.com/shareArticle?mini=true&url=$url&title=$title",
                'icon' => 'icon-linkedin',
                'title' => esc_html__('Linkedin', 'socialv'),
                'parameter' => 'target=_blank',
            ],
            'pinterest' => [
                'url' => "http://pinterest.com/pin/create/button/?url=$url&description=$title",
                'icon' => 'icon-pinterest',
                'title' => esc_html__('Pinterest', 'socialv'),
                'parameter' => 'target=_blank',
            ],
            'whatsppp' => [
                'url' => 'https://api.whatsapp.com/send?text=' . rawurlencode(bp_get_activity_directory_permalink() . "p/" . bp_get_activity_id()),
                'icon' => 'icon-whats-app',
                'title' => esc_html__('Whatsapp', 'socialv'),
                'parameter' => 'target=_blank',
            ],
            'yahoo' => [
                'url' => '//compose.mail.yahoo.com/?Subject=' . $title . '&body=Link:' . $url,
                'icon' => 'icon-yahoo',
                'title' => esc_html__('Yahoo', 'socialv'),
                'parameter' => 'target=_blank',
            ],
            'skype' => [
                'url' => "http://web.skype.com/share?url=$url",
                'icon' => 'icon-skype',
                'title' => esc_html__('Skype', 'socialv'),
                'parameter' => 'target=_blank',
            ],
            'telegram' => [
                'url' => "https://t.me/share/url?url=$url",
                'icon' => 'icon-telegram',
                'title' => esc_html__('Telegram', 'socialv'),
                'parameter' => 'target=_blank',
            ],

        ];
        $socialv_share_post_options = isset($this->socialv_option['socialv_share_post_options']) ? $this->socialv_option['socialv_share_post_options'] : [];
        $content = "";
        foreach ($socialv_share_post_options as $key => $value) {
            if ($value == 1) {
                $class = !empty($args[$key]['class']) ? $key . "-share " . $args[$key]['class'] : $key . "-share";
                $content .= '<li><a href="' . esc_url($args[$key]['url']) . '" ' . esc_attr($args[$key]['parameter']) . ' class=' . esc_attr($class) . '><i class="' . esc_attr($args[$key]['icon']) . '"></i>' . $args[$key]['title'] . '</a></li>';
            }
        }
        $content = apply_filters('socialv_activity_social_share_icon', $content, $url);
        echo apply_filters("socialv_activity_social_share", $content, $args);
    }

    function socialv_enable_share_post_on_activity()
    {
        $postId = bp_get_activity_id();
        $title = '';
        $url = urlencode(bp_get_activity_directory_permalink() . "p/" . bp_get_activity_id());
        $act_url = (bp_get_activity_type() === 'activity_share') ? bp_activity_get_meta(bp_get_activity_id(), 'shared_activity_id', true) : bp_get_activity_id();
        $args = [
            'share_activity' => [
                'url' => ((bp_get_activity_type() === 'activity_share') ? bp_activity_get_meta(bp_get_activity_id(), 'shared_activity_id', true) : bp_get_activity_id()),
                'icon' => 'icon-share-box',
                'title' => esc_html__('Share on activity', 'socialv'),
                'parameter' => '',
            ],
        ];
        $content = "";
        if (class_exists('Buddypress_Share')) {
            foreach ($args as $key => $share) {
                $class = !empty($share['class']) ? $key . "-share " . $share['class'] : $key . "-share";
                $content = '<a data-post-id="' . $postId . '"act_id="' . $act_url . '" ' . esc_attr($share['parameter']) . ' class="share_activity-share socialv-share-post share-btn socialv-activity_comment"><i data-href="' . $act_url . '" class="' . esc_attr($share['icon']) . '"></i></a>';
            }
        } else {
            foreach ($args as $key => $share) {
                $class = !empty($share['class']) ? $key . "-share " . $share['class'] : $key . "-share";
                $content = '<li><a data-bs-target= "#shareonactivitypopup" data-bs-toggle="modal" data-post-id="' . $postId . '"act_id="' . $act_url . '" ' . esc_attr($share['parameter']) . ' class=' . esc_attr($class) . '><i data-href="' . $act_url . '" class="' . esc_attr($share['icon']) . '"></i>' . $share['title'] . '</a></li>';
            }
        }

        echo apply_filters("socialv_activity_social_share", $content, $args, $title);
    }

    function is_socialv_user_pin($activity_id, $key)
    {
        $bp_current_page = bp_current_component();
        if ($bp_current_page == "activity") {
            $user = wp_get_current_user();
            $user_id = $user->ID;
            $posts = get_user_meta($user_id, $key, true);
            $post_array = explode(', ', $posts);
            if (in_array($activity_id, $post_array)) {
                return true;
            }
            return false;
        } elseif ($bp_current_page == "groups") {
            $key =  Activity::socialv_current_component();
            $group_id = bp_get_current_group_id();
            if (!empty($group_id)) {
                $result_id = groups_get_groupmeta($group_id, $key, true);
                if (!empty($result_id) && in_array($activity_id, array_reduce($result_id, 'array_merge', array()))) {
                    return true;
                }
            }
            return false;
            function is_socialv_user_pin($activity_id, $key)
            {
                $bp_current_page = bp_current_component();
                if ($bp_current_page == "activity") {
                    $user = wp_get_current_user();
                    $user_id = $user->ID;
                    $posts = get_user_meta($user_id, $key, true);
                    $post_array = explode(', ', $posts);
                    if (in_array($activity_id, $post_array)) {
                        return true;
                    }
                    return false;
                } elseif ($bp_current_page == "groups") {
                    $key =  Activity::socialv_current_component();
                    $group_id = bp_get_current_group_id();
                    if (!empty($group_id)) {
                        $result_id = groups_get_groupmeta($group_id, $key, true);
                        if (!empty($result_id) && in_array($activity_id, array_reduce($result_id, 'array_merge', array()))) {
                            return true;
                        }
                    }
                    return false;
                }
                return false;
            }
        }
        return false;
    }

    function is_socialv_user_likes($id, $key)
    {
        $user = wp_get_current_user();
        $user_id = $user->ID;

        if ($key == "_socialv_activity_liked_users") {
            $posts = bp_activity_get_meta($id, $key, true);
        } else {
            $posts = get_post_meta($id, $key, true);
        }
        $post_array = explode(', ', $posts);

        if (in_array($user_id, $post_array)) {
            return true;
        }

        return false;
    }

    function socialv_blog_total_user_likes($id, $key)
    {
        $post_array = '';

        if ($key == "_socialv_activity_liked_users") {
            $posts = bp_activity_get_meta($id, $key, true);
        } else {
            $posts = get_post_meta($id, $key, true);
        }
        if (!empty($posts)) {
            $post_array = explode(', ', $posts);
            return count($post_array);
        }

        return '';
    }

    function socialv_user_activity_callback()
    {
        $id = json_decode($_GET['id']); // Get the ajax call
        $meta_key = sanitize_text_field($_GET['meta_key']);
        $data_type = $_GET['data_type'];
        $group_id = $_GET['group_id'];
        $feedback = '';
        $user = wp_get_current_user();
        $user_id = $user->ID;        

        if (in_array($meta_key, ["_socialv_activity_liked_users", "_socialv_posts_liked_users"])) {
            if ($this->socialv_set_user_likes($id, $meta_key, $user_id)) {
                $feedback = $this->socialv_blog_total_user_likes($id, $meta_key);
            }
        }elseif ($data_type == 'activity') {
            $feedback = $this->socialv_set_user_pin($id, $meta_key, $user_id);
        } else {
            $feedback = $this->socialv_set_group_pin($id, $meta_key, $user_id, $group_id);
        }
        wp_send_json_success($feedback);
        wp_die();
    }

    function socialv_set_user_pin($id, $meta_key, $user_id)
    {
        $currentvalue = get_user_meta($user_id, $meta_key, true);
        $post_array = explode(', ', $currentvalue);
        if (!in_array($id, $post_array)) {
            if (!empty($currentvalue)) {
                $newvalue = $currentvalue . ', ' . $id;
            } else {
                $newvalue = $id;
            }
            if (update_user_meta($user_id, $meta_key, $newvalue, $currentvalue)) {
                $feedback = array("has_activity" => $this->is_socialv_user_pin($id, $meta_key), "status" => true);
            }
        } else {
            $key = array_search($id, $post_array);
            unset($post_array[$key]);

            if (update_user_meta($user_id, $meta_key, implode(", ", $post_array), $currentvalue)) {
                $feedback = array("has_activity" => $this->is_socialv_user_pin($id, $meta_key), "status" => false);
            }
        }
        return $feedback;
    }
    function socialv_set_group_pin($id, $meta_key, $user_id, $group_id)
    {
        $group_pin_value = groups_get_groupmeta($group_id, $meta_key, true);
        if (empty($group_pin_value)) {
            $group_pin_value = [$user_id => [$id]];
            groups_update_groupmeta($group_id, $meta_key, $group_pin_value);
            $feedback = array("has_activity" => true, "status" => true);
        } else {
            $userflag = false;
            if (!in_array($id, array_reduce($group_pin_value, 'array_merge', array()))) {
                foreach ($group_pin_value as $key => $value) {
                    if ($key == $user_id) {
                        $userflag = true;
                    }
                }
                if (!$userflag) {
                    $group_pin_value[$user_id] = [$id];
                } elseif ($userflag) {
                    $group_pin_value[$user_id][] = $id;
                }
                groups_update_groupmeta($group_id, $meta_key, $group_pin_value);
                $feedback = array("has_activity" => $this->is_socialv_user_pin($id, $meta_key), "status" => true);
            } else {
                foreach ($group_pin_value as $key => $value) {
                    if (in_array($id, $value)) {
                        $index = array_search($id, $value);
                        unset($group_pin_value[$key][$index]);
                    }
                }
                groups_update_groupmeta($group_id, $meta_key, $group_pin_value);

                $feedback = array("has_activity" => $this->is_socialv_user_pin($id, $meta_key), "status" => false);
            }
        }
        return $feedback;
    }
    function socialv_set_user_likes($activity_id, $meta_key, $user_id)
    {
        if ($meta_key == "_socialv_activity_liked_users") {
            $currentvalue = bp_activity_get_meta($activity_id, $meta_key, true);
        } else {
            $currentvalue = get_post_meta($activity_id, $meta_key, true);
        }

        $post_array = explode(', ', $currentvalue);

        if (!in_array($user_id, $post_array)) {
            $newvalue = (!empty($currentvalue)) ? $currentvalue . ', ' . $user_id : $user_id;

            if ($meta_key == "_socialv_activity_liked_users" && bp_activity_update_meta($activity_id, $meta_key, $newvalue, $currentvalue)) {
                $args = array("has_activity" => $this->is_socialv_user_likes($user_id, $meta_key), "status" => true);
            } else {
                if ($meta_key == "_socialv_posts_liked_users" && update_post_meta($activity_id, $meta_key, $newvalue, $currentvalue)) {
                    $args = array("has_activity" => $this->is_socialv_user_likes($activity_id, $meta_key), "status" => true);
                }
            }
        } else {
            $key = array_search($user_id, $post_array);
            unset($post_array[$key]);

            if ($meta_key == "_socialv_activity_liked_users" && bp_activity_update_meta($activity_id, $meta_key, implode(", ", $post_array), $currentvalue)) {
                $args = array("has_activity" => $this->is_socialv_user_likes($user_id, $meta_key), "status" => false);
            } else {
                if ($meta_key == "_socialv_posts_liked_users" && update_post_meta($activity_id, $meta_key, implode(", ", $post_array), $currentvalue)) {
                    $args = array("has_activity" => $this->is_socialv_user_likes($user_id, $meta_key), "status" => false);
                }
            }
        }
        // notify-user
        if ($meta_key == "_socialv_activity_liked_users") {
            $args["component_name"] = "socialv_activity_like_notification";
            $args["component_action"] = "action_activity_liked";
            $args['enable_notification_key'] = "notification_activity_new_like";
            CustomNotifications::socialv_add_user_notification($activity_id, $args);
        }
        return $args;
    }

    function socialv_unmark_activity_favorite()
    {
        if (!bp_is_post_request()) {
            return;
        }

        if (!isset($_POST['nonce'])) {
            return;
        }

        // Either the 'mark' or 'unmark' nonce is accepted, for backward compatibility.
        $nonce = wp_unslash($_POST['nonce']);
        if (!wp_verify_nonce($nonce, 'mark_favorite') && !wp_verify_nonce($nonce, 'unmark_favorite')) {
            return;
        }

        if (bp_activity_remove_user_favorite($_POST['id']))
            esc_html_e('Add Favorite', 'socialv');
        else
            esc_html_e('Remove Favorite', 'socialv');

        exit;
    }

    function socialv_activity_like_users($users = "")
    {
        if (!empty($users)) {
            $users = array_reverse(explode(', ', $users));
            $users = array_diff($users, self::socialv_get_blocked_block_by_list(bp_get_activity_user_id()));
            $users = array_values($users);
            $count_likes = count($users);

            if ($count_likes > 0) {
                $user_length = $count_likes > 5 ? 4 : $count_likes - 1;
                ?>
                <div class="liked-member">
                    <ul class="member-thumb-group list-img-group">
                        <?php
                        for ($i = 0; $i <= $user_length; $i++) :
                            $profile_link = bp_members_get_user_url($users[$i]);
                        ?>
                            <li>
                                <a href="<?php echo esc_url($profile_link); ?>">
                                    <?php bp_activity_avatar('user_id=' . $users[$i]); ?>
                                </a>
                            </li>
                        <?php endfor; ?>
                    </ul>
                    <span class="total-member">
                        <?php echo esc_html__("Liked by ", "socialv"); ?>
                        <a href="<?php echo esc_url(bp_members_get_user_url($users[0])); ?>">
                            <?php echo bp_core_get_user_displayname($users[0]); ?>
                        </a>
                        <?php if ($count_likes != 1) : ?>
                            <?php echo esc_html__(" And ", "socialv"); ?>
                            <a href="javascript:void(0);" class="socialv-get-liked-users" data-id="<?php echo esc_attr(bp_get_activity_id()); ?>">
                                <?php echo esc_html($count_likes - 1); ?>
                                <?php echo (esc_html($count_likes - 1) > 1) ? esc_html__(" Others", "socialv") : esc_html__(" Other", "socialv"); ?>
                            </a>
                        <?php endif; ?>
                    </span>
                </div>
        <?php }
        }
    }

    function get_users_activity_liked_modal()
    {
        ?>
        <!-- Modal -->
        <div class="modal fade" id="liked-users-modal" tabindex="-1" aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered friend-list-popup">
                <div class="modal-content card-main">
                    <div class="card-inner">
                        <div class="name-list">
                            <h5 class="m-0">
                                <?php esc_html_e("People Who like this post", "socialv"); ?>
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body p-0">
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <?php
    }

    function socialv_activity_liked_users()
    {
        $activity_id = json_decode($_GET['id']); // Get the ajax call
        $users_likes = bp_activity_get_meta($activity_id, "_socialv_activity_liked_users", true);
        if (!empty($users_likes)) {
            $blocked_users = self::socialv_get_blocked_block_by_list();
            $users = array_reverse(explode(', ', $users_likes));
            echo "<ul class='p-0 m-0'>";
            foreach ($users as $user_id) :
                if (in_array($user_id, $blocked_users))
                    continue;

                $user_data = get_userdata($user_id);
                $profile_link = bp_members_get_user_url($user_id);
                $uname = "@" . $user_data->user_login;

        ?>
                <li>
                    <div class="user-who-liked">
                        <a href="<?php echo esc_url($profile_link); ?>">
                            <span class="mr-2">
                                <?php bp_activity_avatar('class=rounded-circle&user_id=' . $user_id); ?>
                            </span>
                        </a>
                        <div class="like-details">
                            <a href="<?php echo esc_url($profile_link); ?>">
                                <h6 class="mb-1 mt-0">
                                    <?php echo bp_core_get_user_displayname($user_id); ?>
                                    <?php
                                    if (class_exists("BP_Verified_Member"))
                                        echo VerifiedMember::getInstance()->socialv_get_user_badge($user_id);
                                    ?>
                                </h6>
                                <p class="m-0">
                                    <?php echo esc_html($uname); ?>
                                </p>
                            </a>
                            <div class="liked">
                                <i class="iconly-Heart icbo"></i>
                            </div>
                        </div>

                    </div>
                </li>
        <?php
            endforeach;
            echo "</ul>";
        }
        die();
    }
    public function socialv_get_blocked_block_by_list($user_id = null)
    {
        if (!is_user_logged_in())
            return [];
        $user_id = $user_id == null ? 69 : $user_id;
        $users = '';
        if (function_exists("imt_get_blocked_members_ids")) {
            $user_blocked = imt_get_blocked_members_ids($user_id);
            $users = !empty($user_blocked) ? implode(",", $user_blocked) : '';
        }
        if (function_exists("imt_get_members_blocked_by_ids")) {
            $user_block_by = imt_get_members_blocked_by_ids($user_id);
            $users .= !empty($user_block_by) ? "," . implode(",", $user_block_by) : '';
        }

        return !empty($users) ? explode(",", $users) : [];
    }


    /**
     * Buddypress_giphy_post_gif_html
     */
    public function socialv_buddypress_giphy_post_gif_html()
    {
        global $bp;
        $bpgp_settings = get_site_option('bpgp_settings');
        if (!isset($bpgp_settings['groups_gif_support']) && $bp->current_component == 'groups') {
            return;
        }
        if (!isset($bpgp_settings['profiles_gif_support']) && $bp->current_component == 'activity' && $bp->current_action == 'just-me') {
            return;
        }

        ?>

        <div class="post-elements-buttons-item bp-giphy-html-container mpp-upload-buttons socialv-upload-file">
            <div class="bp-giphy-media-search">
                <a class="bp-giphy-icon" title="<?php esc_attr_e('Choose a gif', 'socialv'); ?>"><label class="socialv-upload-btn-labels"><span class="upload-icon"><i class="wb-icons wb-icon-gif"></i></span><span>
                            <?php esc_html_e('Gif', 'socialv'); ?>
                        </span></label></a>
                <div class="bp-giphy-media-search-dropdown"></div>
            </div>
        </div>
    <?php
    }


    function socialv_blogs_activity_content_with_read_more($content, $activity)
    {
            if (property_exists($activity, 'component') && 'blogs' === $activity->component && isset($activity->secondary_item_id) && 'new_blog_' . get_post_type($activity->secondary_item_id) === $activity->type) {
            $blog_post = get_post($activity->secondary_item_id);
            // If we converted content to an object earlier, flip it back to a string.
            $content = apply_filters('socialv_add_blog_post_as_activity_content_callback', '', $blog_post, $activity);
        }
        return $content;
    }

    function socialv_add_feature_image_blog_post_as_activity_content_callback($content, $blog_post_id)
    {
        if (!empty($blog_post_id) && !empty(get_post_thumbnail_id($blog_post_id))) {
            $content .= sprintf(' <a class="bb-post-img-link" href="%s"></a><div class="blog-post-image" style="background:url(%s)"><div class="blog-post-image-inner"><img src="%s" /></div></div>', esc_url(get_permalink($blog_post_id)), esc_url(wp_get_attachment_image_url(get_post_thumbnail_id($blog_post_id), 'full')), esc_url(wp_get_attachment_image_url(get_post_thumbnail_id($blog_post_id), 'full')));
        }

        return $content;
    }


    function socialv_add_blog_post_as_activity_content_callback($content, $blog_post, $activity)
    {
        if (is_a($blog_post, 'WP_Post')) {
            $content_img = '<div class="socialv-blog-box">' . apply_filters('socialv_add_feature_image_blog_post_as_activity_content', '', $blog_post->ID);
            $post_title = sprintf('<h4 class="entry-title"><a class="socialv-post-title-link" href="%s"><span>%s</span></a></h4>', esc_url(get_permalink($blog_post->ID)), esc_html($blog_post->post_title));
            $content = bp_create_excerpt(bp_strip_script_and_style_tags(html_entity_decode(get_the_excerpt($blog_post->ID))));
            if (false !== strrpos($content, __('&hellip;', 'socialv'))) {
                $content = str_replace(' [&hellip;]', '&hellip;', $content);
                $content = apply_filters_ref_array('bp_get_activity_content', array($content, $activity));
                preg_match('/<iframe.*src=\"(.*)\".*><\/iframe>/isU', $content, $matches);
                if (isset($matches) && array_key_exists(0, $matches) && !empty($matches[0])) {
                    $iframe = $matches[0];
                    $content = strip_tags(preg_replace('/<iframe.*?\/iframe>/i', '', $content), '<a>');

                    $content .= $iframe;
                }
                $content = sprintf('%1$s <div class="socialv-blog-detail px-0 pb-0">%2$s %3$s', $content_img, $post_title, wpautop($content));
            } else {
                $content = apply_filters_ref_array('bp_get_activity_content', array($content, $activity));
                $content = strip_tags($content, '<a><iframe><img><span><div>');
                preg_match('/<iframe.*src=\"(.*)\".*><\/iframe>/isU', $content, $matches);
                if (isset($matches) && array_key_exists(0, $matches) && !empty($matches[0])) {
                    $content = $content;
                }
                $content = sprintf('%1$s <div class="socialv-blog-detail px-0 pb-0">%2$s %3$s', $content_img, $post_title, wpautop($content));
            }

            // Set view post button for activity post content.
            $activity_permalink = esc_url(get_permalink($blog_post->ID));
            $content .= sprintf(
                '<div class="blog-button"><a href="%1$s" class="socialv-button socialv-button-link view-blog">%2$s</a></div>',
                $activity_permalink,
                esc_html__('View Post', 'socialv'),
            );
            $content .= '</div></div>';
        }
        return $content;
    }


    function socialv_activity_undo_post()
    {
        if (!is_user_logged_in())
            return [];
        $activity_id = bp_get_activity_id(); ?>
        <div class="undo_activity_post" style="display: none">
            <div class="d-flex justify-content-between mb-3 gap-3">
                <div class="d-flex gap-3">
                    <i class="iconly-Close-Square icli fs-3 mt-1"></i>
                    <div class="wrp-main">
                        <?php echo '<h6>' . esc_html__("Post hidden", "socialv") . '</h6>';
                        echo '<p class=mt-1>' . esc_html__("You won't see this post in your Feed.", "socialv") . '</p>'; ?>
                    </div>
                </div>
                <span class="undo-btn mt-2"><a href="javascript:void(0)" data-type="undo" data-activity_id="<?php bp_activity_id(); ?>" data-id="<?php echo get_current_user_id(); ?>" class="hide-post-btn">
                        <?php esc_html_e("undo", "socialv"); ?>
                    </a></span>
            </div>
            <?php if (shortcode_exists("imt_report_button")) : ?>
                <div class="d-flex justify-content-between">
                    <div class="d-flex gap-3">
                        <i class="iconly-Info-Square icli fs-3 mt-1"></i>
                        <div class="wrp-main">
                            <?php
                            $name = esc_html__("Report Post", "socialv");
                            $content = esc_html__("I'm concerned about this post.", "socialv");
                            echo do_shortcode("[imt_report_button id=$activity_id type=activity  name='<h6>$name</h6>' content='<p class=mt-1>$content</p>']"); ?>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        </div>
        <?php
    }

    function socialv_remove_filters_with_method_name($hook_name = '', $method_name = '', $priority = 0)
    {
        global $wp_filter;
        // Loop on filters registered
        foreach ((array) $wp_filter[$hook_name][$priority] as $unique_id => $filter_array) {
            if (is_a($wp_filter[$hook_name], 'WP_Hook')) {
                unset($wp_filter[$hook_name]->callbacks[$priority][$unique_id]);
            } else {
                unset($wp_filter[$hook_name][$priority][$unique_id]);
            }
        }
        return false;
    }
    function socialv_bpolls_polls_update_html()
    {
        $this->socialv_remove_filters_with_method_name('bp_activity_post_form_options', 'bpolls_polls_update_html', 10);
        $polls_public = new Buddypress_Polls_Public('buddypress-polls', BPOLLS_PLUGIN_VERSION);
        $result = $polls_public->bpolls_is_user_allowed_polls();
        if ($result) { ?>
            <a class="bpolls-icon" href="#" title="<?php esc_attr_e('Add a poll', 'socialv'); ?>">
                <div class="post-elements-buttons-item bpolls-html-container">
                    <span class="upload-icon"><i class="icon-icon-poll"></i></span>
                    <span>
                        <?php esc_html_e("Add a poll", "socialv"); ?>
                    </span>
                </div>
            </a>
<?php
        }
    }
    public static function socialv_current_component()
    {
        if (bp_current_component() == "activity") {
            $current_action = bp_current_action();
            if ($current_action == "just-me") {
                return "_socialv_pin_just-me_activity";
            } elseif ($current_action == "mentions") {
                return "_socialv_pin_mentions_activity";
            } elseif ($current_action == "favorites") {
                return "_socialv_pin_favorites_activity";
            } elseif ($current_action == "friends") {
                return "_socialv_pin_friends_activity";
            } elseif ($current_action == "groups") {
                return "_socialv_pin_group_activity";
            } else {
                return "_socialv_pin_activity";
            }
        } elseif (bp_current_component() == "groups") {
            return "_socialv_pin_group_data";
        }
    }
    public function socialv_helping_component()
    {
        if (bp_current_component() == "activity") {
            return esc_attr('data-type = activity');
        }
        if (bp_current_component() == "groups") {
            return esc_attr('data-type = groups group-id = ' . bp_get_current_group_id() . '');
        }
    }
    public function socialv_helping_pin_activity($activity_id)
    {

        if (bp_current_component() == "activity") {
            return true;
        }
        if (bp_current_component() == "groups") {
            $group_id = bp_get_current_group_id();
            $user_id = get_current_user_id();
            if (groups_is_user_admin($user_id, $group_id)) {
                return true;
            }
            $result_id = groups_get_groupmeta($group_id, '_socialv_pin_group_data', true);
            if (empty($result_id)) {
                return true;
            }
            if (!in_array($activity_id, array_reduce($result_id, 'array_merge', array()))) {
                return true;
            } else {
                foreach ($result_id as $key => $innerArray) {
                    if ($user_id == $key && in_array($activity_id, $innerArray)) {
                        return true;
                    }
                }
            }
            return false;
        }
    }


    public function socialv_stop_action_register()
    {
        remove_action('bp_core_activated_user', 'bp_core_new_user_activity');
    }
    public function socialv_stop_action_avatar()
    {
        remove_action('bp_members_avatar_uploaded', 'bp_members_new_avatar_activity', 10, 4);
    }
    public function socialv_stop_action_group_activity()
    {
        add_action('bp_has_activities', [$this, 'bp_filter_groups_from_activity'], 10, 2);
    }

    public function bp_filter_groups_from_activity($a, $activities)
    {
        if (bp_is_current_component('activity')) {
            foreach ($activities->activities as $key => $activity) {
                // Check if the activity is related to groups
                if ($activity->component == 'groups') {
                    // Remove the activity from the list
                    unset($activities->activities[$key]);
                    // Adjust the activity counts
                    $activities->activity_count -= 1;
                    $activities->total_activity_count -= 1;
                    $activities->pag_num -= 1;
                }
            }

            // Re-index the activities array after unsetting
            $activities->activities = array_values($activities->activities);
        }

        return $activities;
    }

    public function socialv_enable_share_post_url_on_activity()
    {
        $url = bp_get_activity_directory_permalink() . "p/" . bp_get_activity_id();
        $args = [
            'url' => $url,
            'icon' => 'icon-copying',
            'title' => esc_html__('Copy URL', 'socialv'),
            'class' => 'socialv_copy_url'
        ];

        $content = '<li><a data_url="' . esc_attr($args['url']) . '" class="' . esc_attr($args['class']) . '"><i class="' . esc_attr($args['icon']) . '"></i>' . $args['title'] . '</a></li>';

        echo apply_filters("socialv_activity_social_share", $content, $args);
    }

    public function socialv_buddypress_activity_query()
    {

        add_filter('bp_use_legacy_activity_query', function ($value, $method, $args) {
            if ($method == "BP_Activity_Activity::get_activity_comments") {
                return true;
            }
        }, 10, 3);

        add_filter('bp_activity_comments_user_join_filter', function ($value, $activity_id, $left, $right, $spam = 'ham_only') {
            // Modify the SQL query to fetch only two comments
            global $wpdb, $bp;
            $bp = buddypress();

            $xprofile_active = function_exists('bp_is_active') && bp_is_active('xprofile');

            // Initialize fullname related variables
            $fullname_select = $fullname_from = $fullname_where = '';

            // Select the user's fullname with the query if XProfile is active and user has filled the field.
            if ($xprofile_active) {
                if (bp_has_profile()) {
                    // Get the field value if user has filled it.
                    $field_id = 1; // Modify this according to your field ID.
                    $field_value = bp_get_profile_field_data('field=' . $field_id);
                    if (!empty($field_value)) {
                        $fullname_select = ", pd.value as user_fullname";
                        $fullname_from = ", {$bp->profile->table_name_data} pd ";
                        $fullname_where = "AND pd.user_id = a.user_id AND pd.field_id = $field_id";
                    }
                }
            }


            // Don't retrieve activity comments marked as spam.
            if ('ham_only' == $spam) {
                $spam_sql = 'AND a.is_spam = 0';
            } elseif ('spam_only' == $spam) {
                $spam_sql = 'AND a.is_spam = 1';
            } else {
                $spam_sql = $spam;
            }

            //comment order
            $comment_order = isset($this->socialv_option['display_comments_order']) ? $this->socialv_option['display_comments_order'] : 'ASC';

            // Modify the SQL query to fetch only two comments
            $sql = $wpdb->prepare(
                "SELECT a.*, u.user_email, u.user_nicename, u.user_login, u.display_name{$fullname_select} 
                                       FROM {$bp->activity->table_name} a, {$wpdb->users} u{$fullname_from} 
                                       WHERE u.ID = a.user_id {$fullname_where} 
                                       AND a.type = 'activity_comment' {$spam_sql} 
                                       AND a.item_id = %d 
                                       AND a.mptt_left > %d 
                                       AND a.mptt_left < %d 
                                       ORDER BY a.date_recorded $comment_order
                                       LIMIT 2",
                $activity_id,
                $left,
                $right
            );

            return $sql;
        }, 10, 5);
    }


    // public function socialv_activity_hastag($activity)
    // {
    //     // Regular expression to find hashtags
    //     $pattern = '/#(\w+)/';

    //     // Base URL for the search link
    //     $base_url = esc_url(home_url('/?bp_hastag=true&s=%23'));

    //     // Replacement pattern with the base URL and hashtag
    //     $replacement = '<a href="' . $base_url . '$1">#$1</a>';

    //     // Modify the content property
    //     $activity->content = preg_replace($pattern, $replacement, $activity->content);

    //     // Return the modified activity object
    //     return $activity;
    // }



}
