version: '3.8'

services:
  # WordPress 服务
  wordpress:
    image: wordpress:6.4-php8.1-apache
    container_name: socialv-wordpress
    restart: unless-stopped
    ports:
      - "8080:80"
    environment:
      WORDPRESS_DB_HOST: db
      WORDPRESS_DB_USER: socialv_user
      WORDPRESS_DB_PASSWORD: socialv_password
      WORDPRESS_DB_NAME: socialv_db
      WORDPRESS_DEBUG: 1
      WORDPRESS_CONFIG_EXTRA: |
        define('WP_DEBUG', true);
        define('WP_DEBUG_LOG', true);
        define('WP_DEBUG_DISPLAY', false);
        define('SCRIPT_DEBUG', true);
    volumes:
      - wordpress_data:/var/www/html
      - ./socialv:/var/www/html/wp-content/themes/socialv
      - ./uploads:/var/www/html/wp-content/uploads
      - ./plugins:/var/www/html/wp-content/plugins
      - ./php-config/uploads.ini:/usr/local/etc/php/conf.d/uploads.ini
    depends_on:
      - db
    networks:
      - socialv-network

  # MySQL 数据库服务
  db:
    image: mysql:8.0
    container_name: socialv-mysql
    restart: unless-stopped
    environment:
      MYSQL_DATABASE: socialv_db
      MYSQL_USER: socialv_user
      MYSQL_PASSWORD: socialv_password
      MYSQL_ROOT_PASSWORD: root_password
    volumes:
      - db_data:/var/lib/mysql
    ports:
      - "3306:3306"
    networks:
      - socialv-network

  # phpMyAdmin 服务
  phpmyadmin:
    image: phpmyadmin/phpmyadmin
    container_name: socialv-phpmyadmin
    restart: unless-stopped
    ports:
      - "8081:80"
    environment:
      PMA_HOST: db
      PMA_USER: socialv_user
      PMA_PASSWORD: socialv_password
    depends_on:
      - db
    networks:
      - socialv-network

  # Redis 缓存服务（可选）
  redis:
    image: redis:7-alpine
    container_name: socialv-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    networks:
      - socialv-network

volumes:
  wordpress_data:
  db_data:

networks:
  socialv-network:
    driver: bridge
