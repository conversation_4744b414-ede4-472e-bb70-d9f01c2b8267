# SocialV WordPress 主题开发环境

## 📁 项目结构

```
trade2/
├── socialv/                    # 原始主题文件
├── socialv-dev/               # Docker 开发环境
│   ├── docker-compose.yml    # Docker 服务配置
│   ├── .env                   # 环境变量
│   ├── wp-config-local.php    # WordPress 配置
│   ├── socialv/               # 主题文件副本
│   ├── plugins/               # 插件目录
│   ├── uploads/               # 上传文件目录
│   └── scripts/               # 开发脚本
├── local-server/              # 本地 PHP 服务器环境
│   ├── start-server.bat       # 启动脚本
│   ├── download-wordpress.bat # WordPress 下载脚本
│   ├── wp-config.php          # WordPress 配置
│   └── wordpress/             # WordPress 安装目录
└── start-development.bat      # 主启动脚本
```

## 🚀 快速启动

### 方法一：一键启动（推荐）
```bash
# 双击运行或在命令行执行
start-development.bat
```

### 方法二：Docker 环境（推荐用于团队开发）
```bash
cd socialv-dev
docker-compose up -d
```

### 方法三：本地 PHP 服务器（轻量级）
```bash
cd local-server
start-server.bat
```

## 🌐 访问地址

### Docker 环境
- **WordPress 站点**: http://localhost:8080
- **管理后台**: http://localhost:8080/wp-admin
- **phpMyAdmin**: http://localhost:8081
- **数据库**: localhost:3306

### 本地 PHP 服务器
- **WordPress 站点**: http://localhost:8000
- **管理后台**: http://localhost:8000/wp-admin

## 🔑 默认登录信息

- **用户名**: admin
- **密码**: admin123
- **邮箱**: <EMAIL>

## 🛠️ 开发工具

### Docker 环境命令
```bash
# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f wordpress

# 停止服务
docker-compose down

# 重启服务
docker-compose restart

# 进入容器
docker-compose exec wordpress bash
```

### 安装推荐插件
```bash
cd socialv-dev
./scripts/install-plugins.sh
```

## 📦 推荐插件

- **BuddyPress** - 社交网络核心功能
- **WooCommerce** - 电商功能
- **LearnPress** - 在线学习
- **Query Monitor** - 调试工具
- **Debug Bar** - 调试信息
- **Redis Object Cache** - 缓存优化

## 🔧 环境要求

### Docker 环境
- Docker Desktop
- 8GB+ RAM 推荐

### 本地环境
- PHP 7.4+ (推荐 8.1)
- MySQL 5.7+ 或 SQLite
- Apache/Nginx (可选)

## 📝 开发注意事项

1. **主题开发**
   - 主题文件会自动同步
   - 支持热重载
   - 启用了调试模式

2. **数据库**
   - Docker 环境使用 MySQL
   - 本地环境可使用 SQLite
   - 数据持久化保存

3. **文件权限**
   - Windows 环境通常无需特殊设置
   - 如遇权限问题，以管理员身份运行

## 🚨 故障排除

### Docker 相关
- **端口冲突**: 修改 `.env` 文件中的端口
- **内存不足**: 增加 Docker Desktop 内存分配
- **启动失败**: 检查 Docker Desktop 是否运行

### 本地服务器
- **PHP 未找到**: 安装 PHP 或使用 XAMPP
- **端口占用**: 修改启动脚本中的端口号
- **权限错误**: 以管理员身份运行

## 📚 相关资源

- [WordPress 开发文档](https://developer.wordpress.org/)
- [BuddyPress 开发指南](https://codex.buddypress.org/)
- [SocialV 主题文档](https://socialv-wordpress.iqonic.design/)
- [Docker 官方文档](https://docs.docker.com/)

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支: `git checkout -b feature/new-feature`
3. 提交更改: `git commit -am 'Add new feature'`
4. 推送分支: `git push origin feature/new-feature`
5. 创建 Pull Request

---

**开发愉快！** 🎉
