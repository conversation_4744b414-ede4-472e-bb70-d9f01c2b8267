<?php
/**
 * SocialV 本地开发环境 WordPress 配置
 */

// 数据库配置 - SQLite 数据库（无需 MySQL）
define('DB_NAME', 'socialv_local');
define('DB_USER', 'root');
define('DB_PASSWORD', '');
define('DB_HOST', 'localhost');
define('DB_CHARSET', 'utf8mb4');
define('DB_COLLATE', '');

// 如果使用 SQLite 插件
if (file_exists(__DIR__ . '/wp-content/plugins/sqlite-integration/sqlite-integration.php')) {
    define('DB_FILE', 'database/socialv.db');
    define('DB_DIR', dirname(__FILE__) . '/wp-content/');
}

// 安全密钥
define('AUTH_KEY',         'local-dev-key-1');
define('SECURE_AUTH_KEY',  'local-dev-key-2');
define('LOGGED_IN_KEY',    'local-dev-key-3');
define('NONCE_KEY',        'local-dev-key-4');
define('AUTH_SALT',        'local-dev-salt-1');
define('SECURE_AUTH_SALT', 'local-dev-salt-2');
define('LOGGED_IN_SALT',   'local-dev-salt-3');
define('NONCE_SALT',       'local-dev-salt-4');

// WordPress 数据库表前缀
$table_prefix = 'wp_';

// 开发环境配置
define('WP_DEBUG', true);
define('WP_DEBUG_LOG', true);
define('WP_DEBUG_DISPLAY', true);
define('SCRIPT_DEBUG', true);
define('SAVEQUERIES', true);

// 内存和执行时间限制
define('WP_MEMORY_LIMIT', '512M');
ini_set('max_execution_time', 300);
ini_set('upload_max_filesize', '64M');
ini_set('post_max_size', '64M');

// 文件系统配置
define('FS_METHOD', 'direct');

// 禁用自动更新（开发环境）
define('AUTOMATIC_UPDATER_DISABLED', true);
define('WP_AUTO_UPDATE_CORE', false);

// 站点 URL 配置
define('WP_HOME', 'http://localhost:8000');
define('WP_SITEURL', 'http://localhost:8000');

// 禁用文件编辑
define('DISALLOW_FILE_EDIT', false);

// 开发环境特定设置
define('CONCATENATE_SCRIPTS', false);
define('COMPRESS_SCRIPTS', false);
define('COMPRESS_CSS', false);

// BuddyPress 配置
define('BP_DEFAULT_COMPONENT', 'activity');

// 绝对路径
if (!defined('ABSPATH')) {
    define('ABSPATH', __DIR__ . '/');
}

// 加载 WordPress
require_once ABSPATH . 'wp-settings.php';
