.socialv-widget-menu .socialv-post li a{padding-left:0}.widget.widget_recent_comments .recentcomments{color:var(--color-theme-primary)}.widget.widget_recent_comments #recentcomments li.recentcomments a{color:var(--global-font-color);padding-left:0}.widget.widget_recent_comments #recentcomments--1 li.recentcomments span a,.widget.widget_recent_comments #recentcomments li.recentcomments span a{color:var(--global-font-title)}.socialv-widget-menu .socialv-post li a:before,.widget.widget_recent_comments #recentcomments--1 li.recentcomments a:before,.widget.widget_recent_comments #recentcomments li.recentcomments a:before{display:none}.widget.widget_recent_comments #recentcomments--1 li.recentcomments a:hover,.widget.widget_recent_comments #recentcomments li.recentcomments a:hover{color:var(--color-theme-primary);text-decoration:underline!important;padding-left:0}.widget_calendar table caption{caption-side:inherit;color:var(--color-theme-secondary);text-transform:uppercase}.calendar_wrap .wp-calendar-table caption,.wp-block-calendar table caption{caption-side:inherit;text-align:center;color:var(--color-theme-white);background:var(--color-theme-primary);text-transform:uppercase}.widget_calendar table#wp-calendar{width:100%;text-align:center;margin-bottom:0;margin-top:0}.widget_calendar .calendar_wrap td#today{background:var(--color-theme-primary);color:var(--color-theme-white)}.widget_calendar .calendar_wrap td#today a{color:var(--color-theme-white)}.widget_calendar .calendar_wrap table th{background:var(--color-theme-white-box);color:var(--color-theme-secondary)}.wp-block-calendar table th{background:transparent;color:var(--color-theme-primary);font-weight:var(--font-weight-bold)}.widget_calendar .calendar_wrap table td,.widget_calendar .calendar_wrap table th{padding:.5em;text-align:center}.wp-block-calendar tbody td,.wp-block-calendar th{border-color:var(--border-color-light);color:var(--global-font-color)}.wp-calendar-nav{-webkit-box-pack:justify;-ms-flex-pack:justify;justify-content:space-between;display:-webkit-box;display:-ms-flexbox;display:flex;text-transform:uppercase}.widget_calendar table#wp-calendar{margin-bottom:.625em;caption-side:inherit}.widget.widget_archive ul li span.achiveCount,.widget.widget_archive ul li span.archiveCount,.widget.widget_block .wp-block-categories-list .archiveCount,.widget_categories ul li span.archiveCount{float:right;font-size:var(--font-size-small);font-weight:var(--font-weight-bold)}.download-item li{display:block;background:var(--global-white-light-color);padding-bottom:0!important}.download-item li i{background:var(--color-theme-primary);padding:1em;color:var(--color-theme-white);font-size:1.5em}.widget.get-file{margin-bottom:1em;padding:0;background:transparent}.widget.get-file a:hover{color:var(--color-theme-primary)}.widget.get-file a{text-decoration:none}.socialv-widget-menu ul.socialv-post li{border:none}.testimonail-widget-menu.widget{padding:0;background:transparent}.widget.widget_archive ul li a,.widget.widget_block ul.wp-block-archives li a,.widget.widget_block ul.wp-block-categories li a,.widget.widget_categories ul li a,.widget.widget_meta ul li a,.widget.widget_nav_menu ul li a,.widget.widget_pages ul li a{color:var(--global-font-color);font-size:var(--font-size-small);font-weight:var(--font-weight-bold);letter-spacing:var(--letter-spacing-two);text-transform:uppercase}.widget.widget_archive ul li a:hover,.widget.widget_block ul.wp-block-archives li a:hover,.widget.widget_block ul.wp-block-categories li a:hover,.widget.widget_categories ul li a:hover,.widget.widget_meta ul li a:hover,.widget.widget_nav_menu ul li a:hover,.widget.widget_pages ul li a:hover{color:var(--color-theme-primary)}.widget.widget_archive select,.widget_categories select,.widget select{border:.0625em solid var(--border-color-light);outline:none;border-radius:var(--border-radius);font-size:var(--font-size-body);line-height:var(--font-line-height-body)}.widget.widget_rss ul li{margin-bottom:1.25em}.widget.widget_rss ul li:last-child{margin-bottom:0}.widget.widget_rss ul li a{padding-left:0;font-size:1.125em;color:var(--global-font-title);font-weight:600}.widget.widget_rss ul li a:hover{color:var(--color-theme-primary)}.widget.widget_rss ul li a:before{display:none}.widget.widget_rss ul li .rss-date{text-transform:uppercase;display:inline-block;width:100%;margin:.313em 0;color:var(--color-theme-primary);font-size:.875em}.widget.widget_rss ul li cite{margin-top:.313em;display:inline-block;width:100%}.widget.widget_nav_menu ul li,.widget ul.menu li{margin-bottom:0}.widget.widget_nav_menu ul li .sub-menu,.widget ul.menu li .sub-menu{display:block!important;padding-left:1.25em}.widget.widget_nav_menu ul li a,.widget ul.menu li a{padding:.625em 0 .625em 1em;display:inline-block;width:100%}.widget.widget_nav_menu ul li i,.widget ul.menu li i{display:none}.widget.widget_nav_menu #menu-service-menu li.current-menu-item a{background:var(--color-theme-primary);color:var(--color-theme-white)}.socialv-widget-menu .socialv-post li a.date-widget{color:var(--color-theme-primary);margin-bottom:.625em;display:block;font-size:.75em;font-weight:700}.socialv-widget-menu .post-img .post-blog a.new-link h6{color:var(--color-theme-white);line-height:1.438em;display:block;overflow:hidden}.widget_recent_entries ul li .post-date{color:var(--color-theme-secondary);float:right}.widget_tag_cloud ul{margin:0;padding:0}.widget_tag_cloud ul li{padding:0;display:inline-block;margin:.188em .313em .188em 0}.widget.widget_block .wp-block-tag-cloud a,.widget_tag_cloud ul li a{display:block;color:var(--global-font-color);font-size:var(--font-size-body)!important}.widget.widget_block .wp-block-tag-cloud a{display:inline-block;position:relative;margin:0 .325em .625em 0;padding:0 .6em 0 0}.widget.widget_block .wp-block-tag-cloud a:last-child{padding:0;margin-right:0}.wp-block-image.size-full.image-sidebar{margin:-2em}.widget.widget_block .wp-block-tag-cloud a:hover,.widget_tag_cloud ul li a.tag-cloud-link:hover{color:var(--color-theme-primary)}.widget.widget_block .wp-block-tag-cloud a:before,.widget_tag_cloud ul li a:before{position:absolute;content:"";background:var(--global-font-color);top:50%;right:-.7em;height:.8em;width:1px;display:block;-webkit-transform:rotate(10deg) translateY(-50%);transform:rotate(10deg) translateY(-50%)}.widget.widget_block .wp-block-tag-cloud a:before{right:0}.widget.widget_block .wp-block-tag-cloud a:last-child:before{display:none}.wp-block-latest-comments__comment{display:inline-block;list-style:none;margin-bottom:.875em}.wp-block-latest-comments__comment:last-child{border:none;padding-bottom:0;margin-bottom:0}ol.wp-block-latest-comments{margin-bottom:0}@media(max-width:29.938em){.widget.widget_nav_menu ul li .sub-menu,.widget ul.menu li .sub-menu{padding-left:.625em}.widget_calendar .calendar_wrap table td,.widget_calendar .calendar_wrap table th{padding:.313em;font-size:var(--font-size-normal)}}
/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */