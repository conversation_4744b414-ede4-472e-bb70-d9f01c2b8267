#!/bin/bash

echo "========================================"
echo "    SocialV WordPress Development"
echo "========================================"
echo

# Check if <PERSON><PERSON> is running
echo "Checking Docker..."
if docker ps >/dev/null 2>&1; then
    echo "[OK] Docker is running. Starting Docker environment..."
    cd socialv-dev
    docker-compose up -d
    
    if [ $? -eq 0 ]; then
        echo
        echo "[SUCCESS] Docker environment started!"
        echo "WordPress: http://localhost:8080"
        echo "Admin: http://localhost:8080/wp-admin"
        echo "phpMyAdmin: http://localhost:8081"
        echo "Username: admin / Password: admin123"
        echo
        echo "Press Enter to continue..."
        read
    else
        echo "[ERROR] Failed to start Docker environment"
        echo "Press Enter to continue..."
        read
    fi
else
    echo "[INFO] Docker not running. Checking PHP..."
    
    # Check if PHP is available
    if command -v php >/dev/null 2>&1; then
        echo "[OK] PHP found. Starting local server..."
        
        # Check if WordPress exists
        if [ ! -d "local-server/wordpress" ]; then
            echo "[INFO] WordPress not found. Please download WordPress first:"
            echo "1. cd local-server"
            echo "2. Run download script or manually download WordPress"
            echo "Press Enter to continue..."
            read
            exit 1
        fi
        
        # Copy theme
        echo "[INFO] Copying theme..."
        if [ -d "socialv" ]; then
            mkdir -p "local-server/wordpress/wp-content/themes/socialv"
            cp -r socialv/* "local-server/wordpress/wp-content/themes/socialv/"
            echo "[OK] Theme copied successfully"
        fi
        
        # Copy config if not exists
        if [ ! -f "local-server/wordpress/wp-config.php" ]; then
            cp "local-server/wp-config.php" "local-server/wordpress/wp-config.php"
        fi
        
        echo
        echo "[INFO] Starting server on http://localhost:8000"
        echo "Press Ctrl+C to stop server"
        echo
        
        cd local-server/wordpress
        
        # Try to open browser (works on Windows with WSL)
        if command -v cmd.exe >/dev/null 2>&1; then
            cmd.exe /c start http://localhost:8000 >/dev/null 2>&1
        fi
        
        php -S localhost:8000
    else
        echo "[ERROR] Neither Docker nor PHP found."
        echo "Please install one of the following:"
        echo "1. Docker Desktop"
        echo "2. PHP or XAMPP"
        echo "Press Enter to continue..."
        read
    fi
fi
