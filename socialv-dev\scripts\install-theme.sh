#!/bin/bash

# SocialV 主题安装脚本
# 使用方法: ./scripts/install-theme.sh

echo "🎨 安装 SocialV 主题到 WordPress..."
echo

# 检查容器是否运行
if ! docker-compose ps | grep -q "wordpress.*Up"; then
    echo "❌ WordPress 容器未运行，请先启动: docker-compose up -d"
    exit 1
fi

# 检查本地主题文件
if [ ! -d "socialv" ]; then
    echo "❌ 未找到 socialv 主题目录"
    echo "请确保 socialv 主题文件在当前目录中"
    exit 1
fi

echo "📋 复制主题文件..."

# 确保主题目录存在
docker-compose exec -T wordpress bash -c "
    mkdir -p /var/www/html/wp-content/themes/socialv
"

# 复制主题文件（通过 Docker 卷挂载已经自动同步）
echo "✅ 主题文件已通过卷挂载自动同步"

# 修复权限
echo "🔧 修复文件权限..."
docker-compose exec -T wordpress bash -c "
    chown -R www-data:www-data /var/www/html/wp-content/themes/socialv/
    find /var/www/html/wp-content/themes/socialv/ -type d -exec chmod 755 {} \;
    find /var/www/html/wp-content/themes/socialv/ -type f -exec chmod 644 {} \;
"

# 检查主题是否正确安装
echo "🔍 验证主题安装..."
THEME_CHECK=$(docker-compose exec -T wordpress bash -c "
    if [ -f '/var/www/html/wp-content/themes/socialv/style.css' ]; then
        echo 'found'
    else
        echo 'not_found'
    fi
")

if [[ "$THEME_CHECK" == *"found"* ]]; then
    echo "✅ SocialV 主题安装成功！"
    echo
    echo "📍 下一步操作:"
    echo "   1. 访问 WordPress 管理后台: http://localhost:8080/wp-admin"
    echo "   2. 进入 外观 > 主题"
    echo "   3. 激活 SocialV 主题"
    echo
    echo "🔑 登录信息:"
    echo "   用户名: admin"
    echo "   密码: admin123"
else
    echo "❌ 主题安装失败，请检查文件是否正确"
    echo "主题目录内容:"
    docker-compose exec -T wordpress ls -la /var/www/html/wp-content/themes/socialv/
fi

echo
