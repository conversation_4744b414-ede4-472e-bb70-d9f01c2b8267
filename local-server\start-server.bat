@echo off
echo 启动 SocialV 本地开发服务器...
echo.

REM 检查 PHP 是否安装
php --version >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: PHP 未安装或未添加到 PATH
    echo 请安装 PHP 或使用 XAMPP
    pause
    exit /b 1
)

REM 设置项目路径
set PROJECT_PATH=%~dp0
set WORDPRESS_PATH=%PROJECT_PATH%wordpress
set THEME_PATH=%WORDPRESS_PATH%\wp-content\themes\socialv

echo 项目路径: %PROJECT_PATH%
echo WordPress 路径: %WORDPRESS_PATH%
echo 主题路径: %THEME_PATH%
echo.

REM 检查 WordPress 是否存在
if not exist "%WORDPRESS_PATH%" (
    echo 正在下载 WordPress...
    mkdir "%WORDPRESS_PATH%"
    
    REM 这里可以添加 WordPress 下载逻辑
    echo 请手动下载 WordPress 到 %WORDPRESS_PATH% 目录
    echo 或运行 download-wordpress.bat 脚本
    pause
    exit /b 1
)

REM 复制主题文件
echo 复制 SocialV 主题文件...
if exist "..\socialv" (
    xcopy "..\socialv" "%THEME_PATH%\" /E /I /Y
    echo 主题文件复制完成
) else (
    echo 警告: 未找到 socialv 主题目录
)

REM 启动 PHP 内置服务器
echo.
echo 启动 PHP 开发服务器...
echo 访问地址: http://localhost:8000
echo 按 Ctrl+C 停止服务器
echo.

cd /d "%WORDPRESS_PATH%"
php -S localhost:8000
