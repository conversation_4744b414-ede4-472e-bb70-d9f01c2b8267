@echo off
echo 下载 WordPress 最新版本...
echo.

REM 检查 curl 是否可用
curl --version >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: curl 未安装
    echo 请手动下载 WordPress: https://wordpress.org/latest.zip
    pause
    exit /b 1
)

REM 创建 WordPress 目录
if not exist "wordpress" mkdir wordpress

REM 下载 WordPress
echo 正在下载 WordPress...
curl -L -o wordpress.zip https://wordpress.org/latest.zip

REM 检查下载是否成功
if not exist "wordpress.zip" (
    echo 下载失败，请检查网络连接
    pause
    exit /b 1
)

REM 解压 WordPress
echo 正在解压 WordPress...
powershell -command "Expand-Archive -Path 'wordpress.zip' -DestinationPath '.' -Force"

REM 移动文件到正确位置
if exist "wordpress\wordpress" (
    xcopy "wordpress\wordpress\*" "wordpress\" /E /I /Y
    rmdir "wordpress\wordpress" /S /Q
)

REM 清理
del wordpress.zip

echo.
echo WordPress 下载完成！
echo 位置: %~dp0wordpress
echo.
pause
