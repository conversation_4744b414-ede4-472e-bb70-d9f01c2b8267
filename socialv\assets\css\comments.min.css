.comment-respond,.comments-area{clear:both}.comments-area{margin-top:2em;background:var(--color-theme-white-box);padding:2em;border-radius:var(--border-radius)}.comments-area>.comments-title{margin-top:0}article.entry .socialv-blog-box:last-child{margin-bottom:0}.comment-respond a#cancel-comment-reply-link{border:none;color:var(--color-theme-primary);position:relative;z-index:9;display:inline-block;text-align:center;white-space:nowrap;vertical-align:middle;padding:0;font-size:var(--font-size-small);line-height:1.5;text-transform:uppercase;letter-spacing:.031em;margin-left:1.25em;font-weight:var(--font-weight-semi-bold);text-decoration:underline}.commentlist{margin:0;padding:0;list-style:none}.commentlist .comment{margin-top:2.5em;margin-bottom:0;vertical-align:top;padding:0;list-style:none}.socialv-comments-info .title a{color:var(--global-font-title)}.socialv-comments-info .title:hover a{color:var(--color-theme-primary)}.commentlist .socialv-comments-media blockquote{background:var(--color-theme-white-box)}ol.commentlist .pingback,ol.commentlist .trackback{margin-bottom:1.875em;border-bottom:.0625em solid rgba(0,0,0,.08);padding-bottom:1.875em}ol.commentlist .pingback a,ol.commentlist .trackback a{color:var(--global-font-color)}ol.commentlist .pingback a:hover,ol.commentlist .trackback a:hover{color:var(--color-theme-primary)}.commentlist li .comment-respond{margin-top:2.5em}.commentlist .socialv-comments-media{padding:1.875em;background:var(--global-body-bgcolor);position:relative;border-radius:var(--border-radius)}.socialv-reply .comment-reply-link{position:absolute;top:0;left:auto;right:0;font-size:var(--font-size-normal);background:var(--color-theme-primary);border-radius:var(--border-radius);color:var(--color-theme-white);padding:.5em 1em;text-transform:uppercase;letter-spacing:var(--letter-spacing-two);font-weight:var(--font-weight-bold)}.commentlist ol.children{padding-left:3.75em}.commentlist .socialv-comment-wrap{display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-align:start;-ms-flex-align:start;align-items:flex-start}.commentlist .socialv-comments-photo{padding-right:1em}.commentlist .socialv-comments-photo img{width:5em;height:auto;border-radius:5.625em}.commentlist .socialv-comments-info{position:relative;display:inline-block;width:100%}.commentlist #div-comment-7 .socialv-comments-info{width:87%}.commentlist .socialv-comments-info .title{margin:0}.commentlist .socialv-comments-info .title a:hover{text-decoration:none}.commentlist .socialv-comment-metadata{color:var(--global-font-color)}.commentlist .socialv-comment-metadata i{padding-right:.313em;color:var(--color-theme-white)}.commentlist .socialv-comments-media .reply a>i{position:absolute;margin-left:-1.563em;font-size:var(--font-size-h5);-webkit-transform:rotateX(148deg);transform:rotateX(148deg);color:var(--color-theme-primary);line-height:normal}.commentlist .socialv-comments-media .socialv-comment-metadata svg{color:var(--color-theme-primary)}.comment-awaiting-moderation,.no-comments{font-style:italic;margin:1em 0}.comment-form-author,.comment-form-email,.comment-form-url{margin-bottom:1.875em;display:block;float:left;width:100%}.socialv-reply.socialv-button-style-2{display:inline-block;position:absolute;right:0;top:0}.commentlist .socialv-comment-metadata .edit-link a{margin-left:.313em;color:var(--color-theme-primary-hover);text-decoration:underline}.comment-respond .form-submit{margin-top:0}.comment-respond .comment-reply-title{margin-bottom:.625em;margin-top:1.5em}.socialv-comments-info .socialv-comment-metadata i{color:var(--color-theme-primary)}.comment-respond .comment-reply-title,.comments-area .comments-title{position:relative;padding-bottom:0}.comment-respond input,.comment-respond textarea{float:inherit}.commentlist .socialv-comment-metadata a{font-size:var(--font-size-normal);-webkit-transition:all .5s ease-in-out;transition:all .5s ease-in-out;color:var(--global-font-color);text-transform:capitalize;margin:.313em 0}p.comment-form-cookies-consent{display:-webkit-box;display:-ms-flexbox;display:flex;width:100%;-webkit-box-align:start;-ms-flex-align:start;align-items:start;margin:0 0 1.875em}.comment-respond label{margin-bottom:0}.wp-block-latest-comments__comment-date,.wp-block-latest-posts__post-date{font-size:var(--font-size-normal);text-transform:uppercase;color:var(--global-font-color)}.commentlist .socialv-comment-metadata{color:var(--color-theme-white);text-transform:uppercase;margin:0;font-size:var(--font-size-normal);padding:0;position:relative;overflow:hidden;display:inline-block}.commentlist li.socialv-comments-item{margin-bottom:1.875em;font-size:var(--font-size-h5);color:var(--global-font-title)}.commentlist li.socialv-comments-item .socialv-comment-type-date{margin-top:1em}.post-password-form input[type=submit]{width:auto;background:var(--color-theme-primary);border:none}.has-dates .wp-block-latest-comments__comment,.has-excerpts .wp-block-latest-comments__comment,.wp-block-latest-comments__comment{display:inline-block;width:100%}.comment-respond .comment-notes,.comment-respond .logged-in-as{padding:0;margin:0}.comment-respond .comment-form-comment{margin:1.875em 0;display:inline-block;width:100%;height:9.375em}.comment-respond .comment-form-url{margin-right:0}.comment-respond .form-submit{display:inline-block;margin-bottom:0}.widget ul .recentcomments .comment-author-link a:before,.widget ul .recentcomments a:before{display:none}.widget ul .recentcomments .comment-author-link a{font-weight:var(--font-weight-medium)}.widget ul .recentcomments a{font-weight:var(--font-weight-regular)}.scrollbar-thumb,.scrollbar-track-y{width:.25em!important}.scrollbar-thumb{background:var(--color-theme-primary)!important}@media(max-width:767px){.socialv-reply.socialv-button-style-2{position:static;display:block;padding-left:1.875em}.commentlist ol.children{padding-left:0}.socialv-reply .comment-reply-link{position:static}.comments-area{margin-top:1em;padding:1em}.commentlist .socialv-comments-media{padding:1em}.comment-respond .comment-reply-title,.commentlist .comment{margin-top:1em}.comment-respond .comment-form-comment{margin:1em 0}.commentlist li .comment-respond{margin-top:1em}}@media (max-width:479px){.comment-respond a#cancel-comment-reply-link{display:block;text-align:left;margin:1em 0 0}}
/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */